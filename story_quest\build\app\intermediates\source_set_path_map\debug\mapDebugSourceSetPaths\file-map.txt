com.example.story_quest.app-preference-1.2.1-0 C:\Users\<USER>\.gradle\caches\8.12\transforms\05840cd26434a51e94c2f67f7d2b7d17\transformed\preference-1.2.1\res
com.example.story_quest.app-jetified-profileinstaller-1.3.1-1 C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\res
com.example.story_quest.app-core-runtime-2.2.0-2 C:\Users\<USER>\.gradle\caches\8.12\transforms\0c23e4478c7e45c54339a51b0bc648a1\transformed\core-runtime-2.2.0\res
com.example.story_quest.app-lifecycle-runtime-2.7.0-3 C:\Users\<USER>\.gradle\caches\8.12\transforms\279b54dca5c2c80f76eb634089e1705d\transformed\lifecycle-runtime-2.7.0\res
com.example.story_quest.app-jetified-core-ktx-1.13.1-4 C:\Users\<USER>\.gradle\caches\8.12\transforms\2c118c78baf4a34f5413be949726fa93\transformed\jetified-core-ktx-1.13.1\res
com.example.story_quest.app-appcompat-1.1.0-5 C:\Users\<USER>\.gradle\caches\8.12\transforms\2ef2678f1ebb7c2f233ed01d9a85f9da\transformed\appcompat-1.1.0\res
com.example.story_quest.app-recyclerview-1.0.0-6 C:\Users\<USER>\.gradle\caches\8.12\transforms\38bc8d20ff474b94f74f85045e4fb4e8\transformed\recyclerview-1.0.0\res
com.example.story_quest.app-jetified-credentials-1.2.0-rc01-7 C:\Users\<USER>\.gradle\caches\8.12\transforms\3cdebf5eb7fc2e0836e09b33315aab36\transformed\jetified-credentials-1.2.0-rc01\res
com.example.story_quest.app-jetified-play-services-auth-21.0.0-8 C:\Users\<USER>\.gradle\caches\8.12\transforms\41bee34b6117e933e1088d15118d4023\transformed\jetified-play-services-auth-21.0.0\res
com.example.story_quest.app-transition-1.4.1-9 C:\Users\<USER>\.gradle\caches\8.12\transforms\43c25f44db5940861cccaeacc78fafad\transformed\transition-1.4.1\res
com.example.story_quest.app-jetified-core-1.0.0-10 C:\Users\<USER>\.gradle\caches\8.12\transforms\510eda741f6a7a51b1171059867bfa71\transformed\jetified-core-1.0.0\res
com.example.story_quest.app-jetified-startup-runtime-1.1.1-11 C:\Users\<USER>\.gradle\caches\8.12\transforms\51d00d3cc9188cf0f50b73233f930468\transformed\jetified-startup-runtime-1.1.1\res
com.example.story_quest.app-jetified-datastore-preferences-release-12 C:\Users\<USER>\.gradle\caches\8.12\transforms\53917f4aea66f866e4264acc81528c36\transformed\jetified-datastore-preferences-release\res
com.example.story_quest.app-jetified-window-1.2.0-13 C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\res
com.example.story_quest.app-jetified-lifecycle-livedata-core-ktx-2.7.0-14 C:\Users\<USER>\.gradle\caches\8.12\transforms\5ba3f3d59eb387ed1a0c3ad74574d0f2\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.story_quest.app-jetified-play-services-base-18.1.0-15 C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\res
com.example.story_quest.app-jetified-play-services-basement-18.4.0-16 C:\Users\<USER>\.gradle\caches\8.12\transforms\6250b576436e3aba2e4229fd698d3169\transformed\jetified-play-services-basement-18.4.0\res
com.example.story_quest.app-core-1.13.1-17 C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\res
com.example.story_quest.app-jetified-lifecycle-process-2.7.0-18 C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\res
com.example.story_quest.app-jetified-activity-1.8.1-19 C:\Users\<USER>\.gradle\caches\8.12\transforms\71b82a96bccd5cf1b6d4635f06f69580\transformed\jetified-activity-1.8.1\res
com.example.story_quest.app-jetified-fragment-ktx-1.7.1-20 C:\Users\<USER>\.gradle\caches\8.12\transforms\77674444d0352378709652b3e912c268\transformed\jetified-fragment-ktx-1.7.1\res
com.example.story_quest.app-fragment-1.7.1-21 C:\Users\<USER>\.gradle\caches\8.12\transforms\77905b615a0bd81ba1a19cccce8c83cf\transformed\fragment-1.7.1\res
com.example.story_quest.app-slidingpanelayout-1.2.0-22 C:\Users\<USER>\.gradle\caches\8.12\transforms\79a15a92dceba3287d2de7717a435261\transformed\slidingpanelayout-1.2.0\res
com.example.story_quest.app-browser-1.4.0-23 C:\Users\<USER>\.gradle\caches\8.12\transforms\8411a046f9616658209ad8ea4a5d985f\transformed\browser-1.4.0\res
com.example.story_quest.app-jetified-credentials-play-services-auth-1.2.0-rc01-24 C:\Users\<USER>\.gradle\caches\8.12\transforms\85a7c287bc0d10f9a5bf501b6ebf14db\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\res
com.example.story_quest.app-jetified-firebase-common-21.0.0-25 C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\res
com.example.story_quest.app-jetified-savedstate-ktx-1.2.1-26 C:\Users\<USER>\.gradle\caches\8.12\transforms\93e83977f0dcb20483c40e50ddeed575\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.story_quest.app-jetified-appcompat-resources-1.1.0-27 C:\Users\<USER>\.gradle\caches\8.12\transforms\a469fed0d64cd7f181196e2a976e8ebd\transformed\jetified-appcompat-resources-1.1.0\res
com.example.story_quest.app-jetified-activity-ktx-1.8.1-28 C:\Users\<USER>\.gradle\caches\8.12\transforms\a54e080bb886fdfe8fe2e3033a9aa5c2\transformed\jetified-activity-ktx-1.8.1\res
com.example.story_quest.app-jetified-window-java-1.2.0-29 C:\Users\<USER>\.gradle\caches\8.12\transforms\a61f6ab104b7d9a0d5f94024d8f886bc\transformed\jetified-window-java-1.2.0\res
com.example.story_quest.app-jetified-lifecycle-runtime-ktx-2.7.0-30 C:\Users\<USER>\.gradle\caches\8.12\transforms\a8ac8f99209a058c57cf60c07c3f6140\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.story_quest.app-coordinatorlayout-1.0.0-31 C:\Users\<USER>\.gradle\caches\8.12\transforms\a8d612f1abde141a0d8a862167841471\transformed\coordinatorlayout-1.0.0\res
com.example.story_quest.app-jetified-core-common-2.0.3-32 C:\Users\<USER>\.gradle\caches\8.12\transforms\ac765428d701a2351974cfbfd0c53614\transformed\jetified-core-common-2.0.3\res
com.example.story_quest.app-jetified-annotation-experimental-1.4.0-33 C:\Users\<USER>\.gradle\caches\8.12\transforms\c7a55ffd175bbd2bf74c834bca7adc94\transformed\jetified-annotation-experimental-1.4.0\res
com.example.story_quest.app-lifecycle-viewmodel-2.7.0-34 C:\Users\<USER>\.gradle\caches\8.12\transforms\dbed50419cdce6780c51a60af639ff57\transformed\lifecycle-viewmodel-2.7.0\res
com.example.story_quest.app-jetified-savedstate-1.2.1-35 C:\Users\<USER>\.gradle\caches\8.12\transforms\e3f83a61a7f84ce41318866e9e003fb1\transformed\jetified-savedstate-1.2.1\res
com.example.story_quest.app-lifecycle-livedata-core-2.7.0-36 C:\Users\<USER>\.gradle\caches\8.12\transforms\e5b98eb6b2458a16ed03eb065e382584\transformed\lifecycle-livedata-core-2.7.0\res
com.example.story_quest.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-37 C:\Users\<USER>\.gradle\caches\8.12\transforms\ea0409d04a80a1be2a9adfb086edff2b\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.story_quest.app-jetified-datastore-release-38 C:\Users\<USER>\.gradle\caches\8.12\transforms\ec90ce586fda296bb57a806c602ef6c9\transformed\jetified-datastore-release\res
com.example.story_quest.app-jetified-datastore-core-release-39 C:\Users\<USER>\.gradle\caches\8.12\transforms\ee907f6692eb2c1a4c1040e6456c22ff\transformed\jetified-datastore-core-release\res
com.example.story_quest.app-jetified-lifecycle-viewmodel-ktx-2.7.0-40 C:\Users\<USER>\.gradle\caches\8.12\transforms\f09e0f903595aa0da6e59048557d638a\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.story_quest.app-jetified-tracing-1.2.0-41 C:\Users\<USER>\.gradle\caches\8.12\transforms\fdecbf64fb324c950c3491ebe8f83e46\transformed\jetified-tracing-1.2.0\res
com.example.story_quest.app-debug-42 D:\AliM Studio\App 01\App\story_quest\android\app\src\debug\res
com.example.story_quest.app-main-43 D:\AliM Studio\App 01\App\story_quest\android\app\src\main\res
com.example.story_quest.app-google-services-44 D:\AliM Studio\App 01\App\story_quest\build\app\generated\res\google-services\debug
com.example.story_quest.app-pngs-45 D:\AliM Studio\App 01\App\story_quest\build\app\generated\res\pngs\debug
com.example.story_quest.app-resValues-46 D:\AliM Studio\App 01\App\story_quest\build\app\generated\res\resValues\debug
com.example.story_quest.app-packageDebugResources-47 D:\AliM Studio\App 01\App\story_quest\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.story_quest.app-packageDebugResources-48 D:\AliM Studio\App 01\App\story_quest\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.story_quest.app-debug-49 D:\AliM Studio\App 01\App\story_quest\build\app\intermediates\merged_res\debug\mergeDebugResources
com.example.story_quest.app-debug-50 D:\AliM Studio\App 01\App\story_quest\build\firebase_auth\intermediates\packaged_res\debug\packageDebugResources
com.example.story_quest.app-debug-51 D:\AliM Studio\App 01\App\story_quest\build\firebase_core\intermediates\packaged_res\debug\packageDebugResources
com.example.story_quest.app-debug-52 D:\AliM Studio\App 01\App\story_quest\build\firebase_storage\intermediates\packaged_res\debug\packageDebugResources
com.example.story_quest.app-debug-53 D:\AliM Studio\App 01\App\story_quest\build\flutter_tts\intermediates\packaged_res\debug\packageDebugResources
com.example.story_quest.app-debug-54 D:\AliM Studio\App 01\App\story_quest\build\google_sign_in_android\intermediates\packaged_res\debug\packageDebugResources
com.example.story_quest.app-debug-55 D:\AliM Studio\App 01\App\story_quest\build\path_provider_android\intermediates\packaged_res\debug\packageDebugResources
com.example.story_quest.app-debug-56 D:\AliM Studio\App 01\App\story_quest\build\shared_preferences_android\intermediates\packaged_res\debug\packageDebugResources
com.example.story_quest.app-debug-57 D:\AliM Studio\App 01\App\story_quest\build\sqflite_android\intermediates\packaged_res\debug\packageDebugResources
