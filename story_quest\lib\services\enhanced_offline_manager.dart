/// Enhanced offline manager for improved story caching and playback
///
/// Provides advanced offline capabilities including intelligent preloading,
/// cache management, and robust fallback mechanisms for seamless offline experience.
library;

import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';
import '../models/story_model.dart';
import 'database_service.dart';
import 'firebase_service.dart';

/// Cache priority levels for intelligent management
enum CachePriority {
  low(1),
  normal(2),
  high(3),
  critical(4);

  const CachePriority(this.level);
  final int level;
}

/// Cache entry metadata
class CacheEntry {
  final String storyId;
  final String assetPath;
  final int size;
  final DateTime lastAccessed;
  final DateTime downloadedAt;
  final CachePriority priority;
  final String checksum;

  const CacheEntry({
    required this.storyId,
    required this.assetPath,
    required this.size,
    required this.lastAccessed,
    required this.downloadedAt,
    required this.priority,
    required this.checksum,
  });

  Map<String, dynamic> toJson() => {
    'story_id': storyId,
    'asset_path': assetPath,
    'size': size,
    'last_accessed': lastAccessed.millisecondsSinceEpoch,
    'downloaded_at': downloadedAt.millisecondsSinceEpoch,
    'priority': priority.level,
    'checksum': checksum,
  };

  factory CacheEntry.fromJson(Map<String, dynamic> json) => CacheEntry(
    storyId: json['story_id'],
    assetPath: json['asset_path'],
    size: json['size'],
    lastAccessed: DateTime.fromMillisecondsSinceEpoch(json['last_accessed']),
    downloadedAt: DateTime.fromMillisecondsSinceEpoch(json['downloaded_at']),
    priority: CachePriority.values.firstWhere((p) => p.level == json['priority']),
    checksum: json['checksum'],
  );
}

/// Enhanced offline manager with intelligent caching
class EnhancedOfflineManager {
  final DatabaseService _databaseService;
  final FirebaseService _firebaseService;
  
  late Directory _cacheDirectory;
  late Directory _storiesDirectory;
  final Map<String, CacheEntry> _cacheIndex = {};
  
  // Cache configuration
  static const int maxCacheSizeBytes = 500 * 1024 * 1024; // 500MB
  static const int maxCacheEntries = 1000;
  static const Duration cacheExpiryDuration = Duration(days: 30);
  
  bool _isInitialized = false;

  EnhancedOfflineManager({
    required DatabaseService databaseService,
    required FirebaseService firebaseService,
  }) : _databaseService = databaseService,
       _firebaseService = firebaseService;

  /// Initializes the enhanced offline manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Set up cache directories
      final appDir = await getApplicationDocumentsDirectory();
      _cacheDirectory = Directory(path.join(appDir.path, 'story_cache'));
      _storiesDirectory = Directory(path.join(_cacheDirectory.path, 'stories'));
      
      await _cacheDirectory.create(recursive: true);
      await _storiesDirectory.create(recursive: true);

      // Create cache tables
      await _createCacheTables();
      
      // Load cache index
      await _loadCacheIndex();
      
      // Perform cache maintenance
      await _performCacheMaintenance();
      
      _isInitialized = true;
    } catch (e) {
      throw Exception('Failed to initialize enhanced offline manager: $e');
    }
  }

  /// Creates database tables for cache management
  Future<void> _createCacheTables() async {
    final db = await _databaseService.database;
    
    await db.execute('''
      CREATE TABLE IF NOT EXISTS cache_entries (
        id TEXT PRIMARY KEY,
        story_id TEXT NOT NULL,
        asset_path TEXT NOT NULL,
        size INTEGER NOT NULL,
        last_accessed INTEGER NOT NULL,
        downloaded_at INTEGER NOT NULL,
        priority INTEGER NOT NULL,
        checksum TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_cache_story_id ON cache_entries(story_id)
    ''');

    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_cache_last_accessed ON cache_entries(last_accessed)
    ''');
  }

  /// Loads cache index from database
  Future<void> _loadCacheIndex() async {
    final db = await _databaseService.database;
    final results = await db.query('cache_entries');
    
    _cacheIndex.clear();
    for (final row in results) {
      final entry = CacheEntry.fromJson(row);
      _cacheIndex[entry.assetPath] = entry;
    }
  }

  /// Downloads and caches a story with all its assets
  Future<bool> downloadStory(String storyId, {CachePriority priority = CachePriority.normal}) async {
    if (!_isInitialized) await initialize();

    try {
      // Check if story is already cached
      if (await isStoryCached(storyId)) {
        await _updateStoryPriority(storyId, priority);
        return true;
      }

      // Create story directory
      final storyDir = Directory(path.join(_storiesDirectory.path, storyId));
      await storyDir.create(recursive: true);

      // Download story metadata
      final storyData = await _firebaseService.downloadStoryData(storyId);
      if (storyData == null) return false;

      final story = StoryModel.fromJson(storyData);
      
      // Download and cache all assets
      final assetPaths = await _getStoryAssetPaths(story);
      final downloadTasks = <Future<bool>>[];

      for (final assetPath in assetPaths) {
        downloadTasks.add(_downloadAndCacheAsset(storyId, assetPath, priority));
      }

      final results = await Future.wait(downloadTasks);
      final allSuccessful = results.every((success) => success);

      if (allSuccessful) {
        // Cache story metadata
        await _cacheStoryMetadata(storyId, story, priority);
        
        // Update download status in database
        await _updateStoryDownloadStatus(storyId, true);
      }

      return allSuccessful;
    } catch (e) {
      return false;
    }
  }

  /// Gets all asset paths for a story
  Future<List<String>> _getStoryAssetPaths(StoryModel story) async {
    final assetPaths = <String>[];
    
    // Add cover image
    assetPaths.add('${story.storyId}/images/story_cover.jpg');
    
    // Add scene images
    for (final scene in story.scenes) {
      if (scene.backgroundImage.isNotEmpty) {
        assetPaths.add('${story.storyId}/images/${scene.backgroundImage}');
      }
      
      // Add character images
      for (final character in scene.characters) {
        if (character.imagePath.isNotEmpty) {
          assetPaths.add('${story.storyId}/images/${character.imagePath}');
        }
      }
    }
    
    // Add audio files
    for (final scene in story.scenes) {
      if (scene.audioFile.isNotEmpty) {
        assetPaths.add('${story.storyId}/audio/${scene.audioFile}');
      }
    }

    return assetPaths;
  }

  /// Downloads and caches a single asset
  Future<bool> _downloadAndCacheAsset(String storyId, String assetPath, CachePriority priority) async {
    try {
      // Download asset from Firebase
      final assetData = await _firebaseService.downloadAsset(assetPath);
      if (assetData == null) return false;

      // Calculate checksum
      final checksum = sha256.convert(assetData).toString();
      
      // Save to local cache
      final localPath = path.join(_storiesDirectory.path, assetPath);
      final localFile = File(localPath);
      await localFile.create(recursive: true);
      await localFile.writeAsBytes(assetData);

      // Create cache entry
      final cacheEntry = CacheEntry(
        storyId: storyId,
        assetPath: assetPath,
        size: assetData.length,
        lastAccessed: DateTime.now(),
        downloadedAt: DateTime.now(),
        priority: priority,
        checksum: checksum,
      );

      // Update cache index
      _cacheIndex[assetPath] = cacheEntry;
      
      // Save to database
      await _saveCacheEntry(cacheEntry);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Caches story metadata
  Future<void> _cacheStoryMetadata(String storyId, StoryModel story, CachePriority priority) async {
    final metadataPath = path.join(_storiesDirectory.path, storyId, 'metadata.json');
    final metadataFile = File(metadataPath);
    
    final metadata = {
      'story': story.toJson(),
      'cached_at': DateTime.now().toIso8601String(),
      'priority': priority.level,
    };
    
    await metadataFile.writeAsString(jsonEncode(metadata));
  }

  /// Checks if a story is fully cached
  Future<bool> isStoryCached(String storyId) async {
    if (!_isInitialized) await initialize();

    try {
      // Check if metadata exists
      final metadataPath = path.join(_storiesDirectory.path, storyId, 'metadata.json');
      final metadataFile = File(metadataPath);
      
      if (!await metadataFile.exists()) return false;

      // Load metadata to get asset list
      final metadataContent = await metadataFile.readAsString();
      final metadata = jsonDecode(metadataContent);
      final story = StoryModel.fromJson(metadata['story']);
      
      // Check if all assets are cached
      final assetPaths = await _getStoryAssetPaths(story);
      
      for (final assetPath in assetPaths) {
        final localPath = path.join(_storiesDirectory.path, assetPath);
        if (!await File(localPath).exists()) return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Gets cached story data
  Future<StoryModel?> getCachedStory(String storyId) async {
    if (!_isInitialized) await initialize();

    try {
      final metadataPath = path.join(_storiesDirectory.path, storyId, 'metadata.json');
      final metadataFile = File(metadataPath);
      
      if (!await metadataFile.exists()) return null;

      final metadataContent = await metadataFile.readAsString();
      final metadata = jsonDecode(metadataContent);
      
      // Update last accessed time
      await _updateAssetAccess(storyId);
      
      return StoryModel.fromJson(metadata['story']);
    } catch (e) {
      return null;
    }
  }

  /// Gets local path for cached asset
  String? getCachedAssetPath(String assetPath) {
    if (!_cacheIndex.containsKey(assetPath)) return null;
    
    final localPath = path.join(_storiesDirectory.path, assetPath);
    return File(localPath).existsSync() ? localPath : null;
  }

  /// Performs cache maintenance (cleanup, optimization)
  Future<void> _performCacheMaintenance() async {
    await _cleanupExpiredEntries();
    await _enforceStorageLimits();
    await _validateCacheIntegrity();
  }

  /// Cleans up expired cache entries
  Future<void> _cleanupExpiredEntries() async {
    final now = DateTime.now();
    final expiredEntries = <String>[];

    for (final entry in _cacheIndex.entries) {
      final cacheEntry = entry.value;
      if (now.difference(cacheEntry.lastAccessed) > cacheExpiryDuration) {
        expiredEntries.add(entry.key);
      }
    }

    for (final assetPath in expiredEntries) {
      await _removeCacheEntry(assetPath);
    }
  }

  /// Enforces storage limits using LRU eviction
  Future<void> _enforceStorageLimits() async {
    // Check total cache size
    int totalSize = _cacheIndex.values.fold(0, (sum, entry) => sum + entry.size);
    
    if (totalSize <= maxCacheSizeBytes && _cacheIndex.length <= maxCacheEntries) {
      return;
    }

    // Sort by priority and last accessed time
    final sortedEntries = _cacheIndex.entries.toList()
      ..sort((a, b) {
        final priorityCompare = a.value.priority.level.compareTo(b.value.priority.level);
        if (priorityCompare != 0) return priorityCompare;
        return a.value.lastAccessed.compareTo(b.value.lastAccessed);
      });

    // Remove entries until within limits
    for (final entry in sortedEntries) {
      if (totalSize <= maxCacheSizeBytes && _cacheIndex.length <= maxCacheEntries) {
        break;
      }
      
      if (entry.value.priority != CachePriority.critical) {
        await _removeCacheEntry(entry.key);
        totalSize -= entry.value.size;
      }
    }
  }

  /// Validates cache integrity and removes corrupted files
  Future<void> _validateCacheIntegrity() async {
    final corruptedEntries = <String>[];

    for (final entry in _cacheIndex.entries) {
      final localPath = path.join(_storiesDirectory.path, entry.key);
      final file = File(localPath);
      
      if (!await file.exists()) {
        corruptedEntries.add(entry.key);
        continue;
      }

      try {
        final fileData = await file.readAsBytes();
        final actualChecksum = sha256.convert(fileData).toString();
        
        if (actualChecksum != entry.value.checksum) {
          corruptedEntries.add(entry.key);
        }
      } catch (e) {
        corruptedEntries.add(entry.key);
      }
    }

    for (final assetPath in corruptedEntries) {
      await _removeCacheEntry(assetPath);
    }
  }

  /// Saves cache entry to database
  Future<void> _saveCacheEntry(CacheEntry entry) async {
    final db = await _databaseService.database;
    await db.insert(
      'cache_entries',
      {
        'id': entry.assetPath,
        ...entry.toJson(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Removes cache entry
  Future<void> _removeCacheEntry(String assetPath) async {
    // Remove from filesystem
    final localPath = path.join(_storiesDirectory.path, assetPath);
    final file = File(localPath);
    if (await file.exists()) {
      await file.delete();
    }

    // Remove from database
    final db = await _databaseService.database;
    await db.delete('cache_entries', where: 'id = ?', whereArgs: [assetPath]);

    // Remove from index
    _cacheIndex.remove(assetPath);
  }

  /// Updates story priority
  Future<void> _updateStoryPriority(String storyId, CachePriority priority) async {
    final db = await _databaseService.database;
    await db.update(
      'cache_entries',
      {'priority': priority.level},
      where: 'story_id = ?',
      whereArgs: [storyId],
    );

    // Update in-memory index
    for (final entry in _cacheIndex.entries) {
      if (entry.value.storyId == storyId) {
        _cacheIndex[entry.key] = CacheEntry(
          storyId: entry.value.storyId,
          assetPath: entry.value.assetPath,
          size: entry.value.size,
          lastAccessed: entry.value.lastAccessed,
          downloadedAt: entry.value.downloadedAt,
          priority: priority,
          checksum: entry.value.checksum,
        );
      }
    }
  }

  /// Updates asset access time
  Future<void> _updateAssetAccess(String storyId) async {
    final now = DateTime.now();
    final db = await _databaseService.database;
    
    await db.update(
      'cache_entries',
      {'last_accessed': now.millisecondsSinceEpoch},
      where: 'story_id = ?',
      whereArgs: [storyId],
    );

    // Update in-memory index
    for (final entry in _cacheIndex.entries) {
      if (entry.value.storyId == storyId) {
        _cacheIndex[entry.key] = CacheEntry(
          storyId: entry.value.storyId,
          assetPath: entry.value.assetPath,
          size: entry.value.size,
          lastAccessed: now,
          downloadedAt: entry.value.downloadedAt,
          priority: entry.value.priority,
          checksum: entry.value.checksum,
        );
      }
    }
  }

  /// Updates story download status
  Future<void> _updateStoryDownloadStatus(String storyId, bool isDownloaded) async {
    final db = await _databaseService.database;
    await db.update(
      'stories',
      {'is_downloaded': isDownloaded ? 1 : 0},
      where: 'story_id = ?',
      whereArgs: [storyId],
    );
  }

  /// Gets cache statistics
  Future<Map<String, dynamic>> getCacheStats() async {
    if (!_isInitialized) await initialize();

    final totalSize = _cacheIndex.values.fold(0, (sum, entry) => sum + entry.size);
    final storiesCached = _cacheIndex.values.map((e) => e.storyId).toSet().length;
    
    return {
      'total_entries': _cacheIndex.length,
      'total_size_bytes': totalSize,
      'total_size_mb': (totalSize / (1024 * 1024)).toStringAsFixed(2),
      'stories_cached': storiesCached,
      'cache_utilization': (totalSize / maxCacheSizeBytes * 100).toStringAsFixed(1),
    };
  }

  /// Clears all cached data
  Future<void> clearCache() async {
    if (!_isInitialized) await initialize();

    // Remove all files
    if (await _storiesDirectory.exists()) {
      await _storiesDirectory.delete(recursive: true);
      await _storiesDirectory.create();
    }

    // Clear database
    final db = await _databaseService.database;
    await db.delete('cache_entries');

    // Clear index
    _cacheIndex.clear();
  }
}
