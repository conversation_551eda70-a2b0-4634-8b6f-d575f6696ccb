/// Firebase service for story content management
/// 
/// <PERSON>les downloading story zip files from Firebase Storage,
/// local caching, and retry logic for network operations.
library;

import 'dart:io';
import 'dart:typed_data';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;

/// Download progress callback
typedef ProgressCallback = void Function(int received, int total);

/// Firebase service for story management
class FirebaseService {
  final FirebaseStorage _storage = FirebaseStorage.instance;
  static const int _maxRetries = 3;
  static const Duration _baseDelay = Duration(seconds: 1);

  /// Downloads a story zip file from Firebase Storage
  /// 
  /// [storyId] - The unique identifier for the story
  /// [onProgress] - Optional callback for download progress updates
  /// Returns the local file path where the story was saved
  Future<String> downloadStory(
    String storyId, {
    ProgressCallback? onProgress,
  }) async {
    final fileName = '$storyId.zip';
    final localPath = await _getLocalStoryPath(fileName);
    
    // Check if file already exists locally
    final localFile = File(localPath);
    if (await localFile.exists()) {
      print('Story $storyId already exists locally');
      return localPath;
    }

    // Download from Firebase with retry logic
    return await _downloadWithRetry(
      storyId,
      fileName,
      localPath,
      onProgress: onProgress,
    );
  }

  /// Downloads a story with retry logic and exponential backoff
  Future<String> _downloadWithRetry(
    String storyId,
    String fileName,
    String localPath, {
    ProgressCallback? onProgress,
  }) async {
    Exception? lastException;
    
    for (int attempt = 1; attempt <= _maxRetries; attempt++) {
      try {
        print('Downloading story $storyId (attempt $attempt/$_maxRetries)');
        
        // Get download URL from Firebase Storage
        final ref = _storage.ref().child('stories/$fileName');
        final downloadUrl = await ref.getDownloadURL();
        
        // Download the file
        await _downloadFile(downloadUrl, localPath, onProgress: onProgress);
        
        print('Successfully downloaded story $storyId');
        return localPath;
        
      } catch (e) {
        lastException = e is Exception ? e : Exception(e.toString());
        print('Download attempt $attempt failed: $e');
        
        if (attempt < _maxRetries) {
          // Exponential backoff: 1s, 2s, 4s
          final delay = Duration(
            milliseconds: _baseDelay.inMilliseconds * (1 << (attempt - 1)),
          );
          print('Retrying in ${delay.inSeconds} seconds...');
          await Future.delayed(delay);
        }
      }
    }
    
    throw lastException ?? Exception('Download failed after $_maxRetries attempts');
  }

  /// Downloads a file from URL to local path
  Future<void> _downloadFile(
    String url,
    String localPath, {
    ProgressCallback? onProgress,
  }) async {
    final response = await http.get(Uri.parse(url));
    
    if (response.statusCode != 200) {
      throw Exception('HTTP ${response.statusCode}: Failed to download file');
    }
    
    final bytes = response.bodyBytes;
    final file = File(localPath);
    
    // Ensure directory exists
    await file.parent.create(recursive: true);
    
    // Write file
    await file.writeAsBytes(bytes);
    
    // Report progress
    onProgress?.call(bytes.length, bytes.length);
  }

  /// Gets the local path for storing a story file
  Future<String> _getLocalStoryPath(String fileName) async {
    final appDir = await getApplicationDocumentsDirectory();
    final storiesDir = Directory('${appDir.path}/stories');
    
    // Ensure stories directory exists
    if (!await storiesDir.exists()) {
      await storiesDir.create(recursive: true);
    }
    
    return '${storiesDir.path}/$fileName';
  }

  /// Checks if a story exists locally
  Future<bool> isStoryDownloaded(String storyId) async {
    final fileName = '$storyId.zip';
    final localPath = await _getLocalStoryPath(fileName);
    return await File(localPath).exists();
  }

  /// Gets the local path for a downloaded story
  Future<String?> getLocalStoryPath(String storyId) async {
    final fileName = '$storyId.zip';
    final localPath = await _getLocalStoryPath(fileName);
    final file = File(localPath);
    
    if (await file.exists()) {
      return localPath;
    }
    return null;
  }

  /// Deletes a locally cached story
  Future<void> deleteLocalStory(String storyId) async {
    final fileName = '$storyId.zip';
    final localPath = await _getLocalStoryPath(fileName);
    final file = File(localPath);
    
    if (await file.exists()) {
      await file.delete();
      print('Deleted local story: $storyId');
    }
  }

  /// Gets the size of a story file in Firebase Storage
  Future<int> getStorySize(String storyId) async {
    try {
      final fileName = '$storyId.zip';
      final ref = _storage.ref().child('stories/$fileName');
      final metadata = await ref.getMetadata();
      return metadata.size ?? 0;
    } catch (e) {
      print('Failed to get story size: $e');
      return 0;
    }
  }

  /// Lists all available stories in Firebase Storage
  Future<List<String>> listAvailableStories() async {
    try {
      final ref = _storage.ref().child('stories');
      final result = await ref.listAll();
      
      return result.items
          .map((item) => item.name)
          .where((name) => name.endsWith('.zip'))
          .map((name) => name.replaceAll('.zip', ''))
          .toList();
    } catch (e) {
      print('Failed to list available stories: $e');
      return [];
    }
  }

  /// Gets the total size of all locally cached stories
  Future<int> getLocalCacheSize() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final storiesDir = Directory('${appDir.path}/stories');
      
      if (!await storiesDir.exists()) {
        return 0;
      }
      
      int totalSize = 0;
      await for (final entity in storiesDir.list()) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
        }
      }
      
      return totalSize;
    } catch (e) {
      print('Failed to calculate cache size: $e');
      return 0;
    }
  }

  /// Clears all locally cached stories
  Future<void> clearLocalCache() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final storiesDir = Directory('${appDir.path}/stories');
      
      if (await storiesDir.exists()) {
        await storiesDir.delete(recursive: true);
        print('Cleared local story cache');
      }
    } catch (e) {
      print('Failed to clear cache: $e');
      rethrow;
    }
  }
}
