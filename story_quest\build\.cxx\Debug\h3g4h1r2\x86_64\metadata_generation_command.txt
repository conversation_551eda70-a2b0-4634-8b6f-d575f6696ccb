                        -HC:\flutter\packages\flutter_tools\gradle\src\main\scripts
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-<PERSON><PERSON>DROID_ABI=x86_64
-DC<PERSON>KE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=D:\Install\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=D:\Install\Android\Sdk\ndk\27.0.12077973
-DC<PERSON>KE_TOOLCHAIN_FILE=D:\Install\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\Install\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\AliM Studio\App 01\App\story_quest\build\app\intermediates\cxx\Debug\h3g4h1r2\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\AliM Studio\App 01\App\story_quest\build\app\intermediates\cxx\Debug\h3g4h1r2\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BD:\AliM Studio\App 01\App\story_quest\build\.cxx\Debug\h3g4h1r2\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2