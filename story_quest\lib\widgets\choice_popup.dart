/// Choice popup widget for interactive story decisions
///
/// Displays choice options with visual elements, animations,
/// and accessibility support for story branching moments.
library;

import 'package:flutter/material.dart';
import '../models/choice_model.dart';
import '../utils/accessibility_helper.dart';

/// Callback type for choice selection
typedef ChoiceCallback = void Function(ChoiceModel choice);

/// Choice popup widget for story decisions
class ChoicePopup extends StatefulWidget {
  /// List of available choices
  final List<ChoiceModel> choices;
  
  /// Title text for the choice popup
  final String title;
  
  /// Optional subtitle or context
  final String? subtitle;
  
  /// Callback when a choice is selected
  final ChoiceCallback? onChoiceSelected;
  
  /// Whether the popup can be dismissed by tapping outside
  final bool barrierDismissible;

  const ChoicePopup({
    super.key,
    required this.choices,
    this.title = 'What happens next?',
    this.subtitle,
    this.onChoiceSelected,
    this.barrierDismissible = false,
  });

  @override
  State<ChoicePopup> createState() => _ChoicePopupState();
}

class _ChoicePopupState extends State<ChoicePopup>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late List<AnimationController> _choiceControllers;
  late List<Animation<double>> _choiceAnimations;

  @override
  void initState() {
    super.initState();
    
    // Initialize main popup animations
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    // Initialize choice button animations
    _choiceControllers = List.generate(
      widget.choices.length,
      (index) => AnimationController(
        duration: Duration(milliseconds: 300 + (index * 100)),
        vsync: this,
      ),
    );

    _choiceAnimations = _choiceControllers.map((controller) =>
      Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOutBack),
      ),
    ).toList();

    // Start animations
    _slideController.forward();
    _scaleController.forward();
    
    // Stagger choice animations
    for (int i = 0; i < _choiceControllers.length; i++) {
      Future.delayed(Duration(milliseconds: 200 + (i * 150)), () {
        if (mounted) {
          _choiceControllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _scaleController.dispose();
    for (final controller in _choiceControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => widget.barrierDismissible,
      child: Material(
        color: Colors.black.withValues(alpha: 0.7),
        child: Center(
          child: SlideTransition(
            position: _slideAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: _buildPopupContent(),
            ),
          ),
        ),
      ),
    );
  }

  /// Builds the main popup content
  Widget _buildPopupContent() {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    
    return Container(
      margin: EdgeInsets.all(isTablet ? 40 : 20),
      constraints: BoxConstraints(
        maxWidth: isTablet ? 500 : 350,
        maxHeight: screenSize.height * 0.8,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          _buildChoicesList(),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  /// Builds the popup header
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Color(0xFF1976D2),
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Decision icon
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.psychology,
              color: Colors.white,
              size: 32,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Title
          AccessibilityHelper.createAccessibleText(
            widget.title,
            style: const TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            semanticLabel: 'Choice moment: ${widget.title}',
          ),
          
          // Subtitle
          if (widget.subtitle != null) ...[
            const SizedBox(height: 8),
            AccessibilityHelper.createAccessibleText(
              widget.subtitle!,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Builds the list of choice buttons
  Widget _buildChoicesList() {
    return Flexible(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 20),
            ...widget.choices.asMap().entries.map((entry) {
              final index = entry.key;
              final choice = entry.value;
              return AnimatedBuilder(
                animation: _choiceAnimations[index],
                builder: (context, child) {
                  return Transform.scale(
                    scale: _choiceAnimations[index].value,
                    child: Opacity(
                      opacity: _choiceAnimations[index].value,
                      child: _buildChoiceButton(choice, index),
                    ),
                  );
                },
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  /// Builds individual choice button
  Widget _buildChoiceButton(ChoiceModel choice, int index) {
    final colors = [
      const Color(0xFF4CAF50), // Green
      const Color(0xFF2196F3), // Blue
      const Color(0xFFFF9800), // Orange
      const Color(0xFF9C27B0), // Purple
    ];
    
    final color = colors[index % colors.length];
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: AccessibilityHelper.createAccessibleButton(
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              // Choice number
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Choice text
              Expanded(
                child: Text(
                  choice.option,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    height: 1.3,
                  ),
                ),
              ),
              
              // Arrow icon
              const Icon(
                Icons.arrow_forward_ios,
                color: Colors.white,
                size: 16,
              ),
            ],
          ),
        ),
        onPressed: () => _selectChoice(choice),
        semanticLabel: 'Choice ${index + 1}: ${choice.option}',
      ),
    );
  }

  /// Handles choice selection
  void _selectChoice(ChoiceModel choice) {
    // Animate selection
    AccessibilityHelper.provideHapticFeedback();
    
    // Close popup and notify parent
    Navigator.of(context).pop();
    widget.onChoiceSelected?.call(choice);
  }
}

/// Shows a choice popup dialog
Future<ChoiceModel?> showChoicePopup({
  required BuildContext context,
  required List<ChoiceModel> choices,
  String title = 'What happens next?',
  String? subtitle,
  bool barrierDismissible = false,
}) {
  return showDialog<ChoiceModel>(
    context: context,
    barrierDismissible: barrierDismissible,
    builder: (context) => ChoicePopup(
      choices: choices,
      title: title,
      subtitle: subtitle,
      barrierDismissible: barrierDismissible,
      onChoiceSelected: (choice) {
        Navigator.of(context).pop(choice);
      },
    ),
  );
}
