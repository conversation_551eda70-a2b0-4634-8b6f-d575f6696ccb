/// Performance monitoring service for app optimization
///
/// Tracks app performance metrics, memory usage, and loading times
/// to help identify bottlenecks and optimize user experience.
library;

import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// Performance metric types
enum MetricType {
  loadTime,
  memoryUsage,
  frameRate,
  networkLatency,
  cacheHitRate,
  userInteraction,
}

/// Performance metric data
class PerformanceMetric {
  final MetricType type;
  final String name;
  final double value;
  final String unit;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  const PerformanceMetric({
    required this.type,
    required this.name,
    required this.value,
    required this.unit,
    required this.timestamp,
    this.metadata = const {},
  });

  Map<String, dynamic> toJson() => {
    'type': type.name,
    'name': name,
    'value': value,
    'unit': unit,
    'timestamp': timestamp.toIso8601String(),
    'metadata': metadata,
  };
}

/// Performance monitoring service
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  final List<PerformanceMetric> _metrics = [];
  final Map<String, Stopwatch> _activeTimers = {};
  Timer? _memoryMonitorTimer;
  bool _isMonitoring = false;

  // Performance thresholds
  static const double maxLoadTimeMs = 3000;
  static const double maxMemoryUsageMB = 200;
  static const double minFrameRate = 30;

  /// Starts performance monitoring
  void startMonitoring() {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    
    // Start memory monitoring
    _memoryMonitorTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _recordMemoryUsage(),
    );

    // Monitor frame rate in debug mode
    if (kDebugMode) {
      _startFrameRateMonitoring();
    }
  }

  /// Stops performance monitoring
  void stopMonitoring() {
    _isMonitoring = false;
    _memoryMonitorTimer?.cancel();
    _memoryMonitorTimer = null;
  }

  /// Starts timing an operation
  void startTimer(String operationName) {
    _activeTimers[operationName] = Stopwatch()..start();
  }

  /// Stops timing an operation and records the metric
  void stopTimer(String operationName, {Map<String, dynamic>? metadata}) {
    final stopwatch = _activeTimers.remove(operationName);
    if (stopwatch == null) return;

    stopwatch.stop();
    final loadTime = stopwatch.elapsedMilliseconds.toDouble();

    _recordMetric(PerformanceMetric(
      type: MetricType.loadTime,
      name: operationName,
      value: loadTime,
      unit: 'ms',
      timestamp: DateTime.now(),
      metadata: metadata ?? {},
    ));

    // Check for performance issues
    if (loadTime > maxLoadTimeMs) {
      _reportPerformanceIssue(
        'Slow operation: $operationName took ${loadTime}ms',
        operationName,
        loadTime,
      );
    }
  }

  /// Records a custom metric
  void recordMetric({
    required MetricType type,
    required String name,
    required double value,
    required String unit,
    Map<String, dynamic>? metadata,
  }) {
    _recordMetric(PerformanceMetric(
      type: type,
      name: name,
      value: value,
      unit: unit,
      timestamp: DateTime.now(),
      metadata: metadata ?? {},
    ));
  }

  /// Records memory usage
  Future<void> _recordMemoryUsage() async {
    try {
      // Get memory info from platform
      final memoryInfo = await _getMemoryInfo();
      
      _recordMetric(PerformanceMetric(
        type: MetricType.memoryUsage,
        name: 'app_memory_usage',
        value: memoryInfo['used_mb'] ?? 0.0,
        unit: 'MB',
        timestamp: DateTime.now(),
        metadata: memoryInfo,
      ));

      // Check for memory issues
      final usedMB = memoryInfo['used_mb'] ?? 0.0;
      if (usedMB > maxMemoryUsageMB) {
        _reportPerformanceIssue(
          'High memory usage: ${usedMB}MB',
          'memory_usage',
          usedMB,
        );
      }
    } catch (e) {
      // Handle error silently
    }
  }

  /// Gets memory information from platform
  Future<Map<String, dynamic>> _getMemoryInfo() async {
    try {
      if (Platform.isAndroid) {
        // Use method channel to get Android memory info
        const platform = MethodChannel('story_quest/performance');
        final result = await platform.invokeMethod('getMemoryInfo');
        return Map<String, dynamic>.from(result);
      } else if (Platform.isIOS) {
        // Use method channel to get iOS memory info
        const platform = MethodChannel('story_quest/performance');
        final result = await platform.invokeMethod('getMemoryInfo');
        return Map<String, dynamic>.from(result);
      }
    } catch (e) {
      // Fallback to basic info
    }

    // Fallback implementation
    return {
      'used_mb': 50.0, // Placeholder
      'available_mb': 150.0,
      'total_mb': 200.0,
    };
  }

  /// Starts frame rate monitoring
  void _startFrameRateMonitoring() {
    // This would typically use Flutter's performance overlay
    // For now, we'll simulate frame rate monitoring
    Timer.periodic(const Duration(seconds: 5), (_) {
      // Simulate frame rate measurement
      final frameRate = 60.0; // Placeholder
      
      _recordMetric(PerformanceMetric(
        type: MetricType.frameRate,
        name: 'app_frame_rate',
        value: frameRate,
        unit: 'fps',
        timestamp: DateTime.now(),
      ));

      if (frameRate < minFrameRate) {
        _reportPerformanceIssue(
          'Low frame rate: ${frameRate}fps',
          'frame_rate',
          frameRate,
        );
      }
    });
  }

  /// Records network latency
  void recordNetworkLatency(String operation, int latencyMs) {
    _recordMetric(PerformanceMetric(
      type: MetricType.networkLatency,
      name: operation,
      value: latencyMs.toDouble(),
      unit: 'ms',
      timestamp: DateTime.now(),
    ));
  }

  /// Records cache hit rate
  void recordCacheHitRate(String cacheType, double hitRate) {
    _recordMetric(PerformanceMetric(
      type: MetricType.cacheHitRate,
      name: cacheType,
      value: hitRate,
      unit: '%',
      timestamp: DateTime.now(),
    ));
  }

  /// Records user interaction timing
  void recordUserInteraction(String interaction, int responseTimeMs) {
    _recordMetric(PerformanceMetric(
      type: MetricType.userInteraction,
      name: interaction,
      value: responseTimeMs.toDouble(),
      unit: 'ms',
      timestamp: DateTime.now(),
    ));
  }

  /// Records a metric internally
  void _recordMetric(PerformanceMetric metric) {
    _metrics.add(metric);
    
    // Keep only recent metrics to prevent memory bloat
    if (_metrics.length > 1000) {
      _metrics.removeRange(0, _metrics.length - 1000);
    }
  }

  /// Reports a performance issue
  void _reportPerformanceIssue(String message, String operation, double value) {
    if (kDebugMode) {
      print('Performance Issue: $message');
    }
    
    // In production, this could send to analytics service
    // For now, we'll just log it
  }

  /// Gets performance summary
  Map<String, dynamic> getPerformanceSummary() {
    if (_metrics.isEmpty) {
      return {'message': 'No performance data available'};
    }

    final now = DateTime.now();
    final recentMetrics = _metrics.where(
      (metric) => now.difference(metric.timestamp).inMinutes < 60,
    ).toList();

    final summary = <String, dynamic>{};
    
    // Group metrics by type
    for (final type in MetricType.values) {
      final typeMetrics = recentMetrics.where((m) => m.type == type).toList();
      if (typeMetrics.isEmpty) continue;

      final values = typeMetrics.map((m) => m.value).toList();
      summary[type.name] = {
        'count': values.length,
        'average': values.reduce((a, b) => a + b) / values.length,
        'min': values.reduce((a, b) => a < b ? a : b),
        'max': values.reduce((a, b) => a > b ? a : b),
        'unit': typeMetrics.first.unit,
      };
    }

    return summary;
  }

  /// Gets detailed metrics for a specific type
  List<PerformanceMetric> getMetricsByType(MetricType type) {
    return _metrics.where((metric) => metric.type == type).toList();
  }

  /// Gets metrics for a specific time range
  List<PerformanceMetric> getMetricsInRange(DateTime start, DateTime end) {
    return _metrics.where((metric) {
      return metric.timestamp.isAfter(start) && metric.timestamp.isBefore(end);
    }).toList();
  }

  /// Clears all recorded metrics
  void clearMetrics() {
    _metrics.clear();
  }

  /// Gets current performance status
  Map<String, dynamic> getCurrentStatus() {
    final summary = getPerformanceSummary();
    final issues = <String>[];

    // Check for performance issues
    if (summary.containsKey('loadTime')) {
      final loadTime = summary['loadTime'];
      if (loadTime['average'] > maxLoadTimeMs) {
        issues.add('Average load time is high: ${loadTime['average'].toStringAsFixed(1)}ms');
      }
    }

    if (summary.containsKey('memoryUsage')) {
      final memory = summary['memoryUsage'];
      if (memory['average'] > maxMemoryUsageMB) {
        issues.add('Average memory usage is high: ${memory['average'].toStringAsFixed(1)}MB');
      }
    }

    if (summary.containsKey('frameRate')) {
      final frameRate = summary['frameRate'];
      if (frameRate['average'] < minFrameRate) {
        issues.add('Average frame rate is low: ${frameRate['average'].toStringAsFixed(1)}fps');
      }
    }

    return {
      'is_monitoring': _isMonitoring,
      'metrics_count': _metrics.length,
      'active_timers': _activeTimers.keys.toList(),
      'performance_issues': issues,
      'summary': summary,
    };
  }

  /// Exports metrics to JSON
  List<Map<String, dynamic>> exportMetrics() {
    return _metrics.map((metric) => metric.toJson()).toList();
  }
}

/// Performance monitoring mixin for widgets
mixin PerformanceTrackingMixin {
  final PerformanceMonitor _monitor = PerformanceMonitor();

  /// Tracks widget build time
  void trackBuildTime(String widgetName, VoidCallback buildFunction) {
    _monitor.startTimer('build_$widgetName');
    buildFunction();
    _monitor.stopTimer('build_$widgetName');
  }

  /// Tracks async operation
  Future<T> trackAsyncOperation<T>(String operationName, Future<T> operation) async {
    _monitor.startTimer(operationName);
    try {
      final result = await operation;
      _monitor.stopTimer(operationName, metadata: {'success': true});
      return result;
    } catch (e) {
      _monitor.stopTimer(operationName, metadata: {'success': false, 'error': e.toString()});
      rethrow;
    }
  }

  /// Records user interaction
  void trackUserInteraction(String interaction) {
    final stopwatch = Stopwatch()..start();
    
    // This would typically be called after the interaction completes
    Future.delayed(Duration.zero, () {
      stopwatch.stop();
      _monitor.recordUserInteraction(interaction, stopwatch.elapsedMilliseconds);
    });
  }
}
