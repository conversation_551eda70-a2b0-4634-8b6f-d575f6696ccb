/// Child profile data model
///
/// Represents a child's profile with personal information, preferences,
/// progress tracking, and parental controls.
library;

/// Child profile model
class ChildProfileModel {
  final String id;
  final String name;
  final int age;
  final String avatarPath;
  final String parentId;
  final DateTime createdAt;
  final DateTime lastActiveAt;
  
  // Preferences
  final String preferredLanguage;
  final double ttsSpeed;
  final double volume;
  final bool subtitlesEnabled;
  final String theme;
  
  // Progress tracking
  final int storiesCompleted;
  final int totalReadingTime; // in minutes
  final List<String> favoriteStories;
  final List<String> completedStories;
  final Map<String, dynamic> currentProgress; // story_id -> progress data
  
  // Parental controls
  final int dailyTimeLimitMinutes;
  final bool contentFilterEnabled;
  final List<String> allowedCategories;
  final bool notificationsEnabled;
  
  // Achievements
  final List<String> earnedBadges;
  final int totalBadges;
  final Map<String, int> badgesByType;

  const ChildProfileModel({
    required this.id,
    required this.name,
    required this.age,
    required this.avatarPath,
    required this.parentId,
    required this.createdAt,
    required this.lastActiveAt,
    this.preferredLanguage = 'en-US',
    this.ttsSpeed = 1.0,
    this.volume = 1.0,
    this.subtitlesEnabled = true,
    this.theme = 'default',
    this.storiesCompleted = 0,
    this.totalReadingTime = 0,
    this.favoriteStories = const [],
    this.completedStories = const [],
    this.currentProgress = const {},
    this.dailyTimeLimitMinutes = 60,
    this.contentFilterEnabled = true,
    this.allowedCategories = const ['educational', 'adventure', 'friendship'],
    this.notificationsEnabled = true,
    this.earnedBadges = const [],
    this.totalBadges = 0,
    this.badgesByType = const {},
  });

  /// Creates a copy of this profile with updated fields
  ChildProfileModel copyWith({
    String? id,
    String? name,
    int? age,
    String? avatarPath,
    String? parentId,
    DateTime? createdAt,
    DateTime? lastActiveAt,
    String? preferredLanguage,
    double? ttsSpeed,
    double? volume,
    bool? subtitlesEnabled,
    String? theme,
    int? storiesCompleted,
    int? totalReadingTime,
    List<String>? favoriteStories,
    List<String>? completedStories,
    Map<String, dynamic>? currentProgress,
    int? dailyTimeLimitMinutes,
    bool? contentFilterEnabled,
    List<String>? allowedCategories,
    bool? notificationsEnabled,
    List<String>? earnedBadges,
    int? totalBadges,
    Map<String, int>? badgesByType,
  }) {
    return ChildProfileModel(
      id: id ?? this.id,
      name: name ?? this.name,
      age: age ?? this.age,
      avatarPath: avatarPath ?? this.avatarPath,
      parentId: parentId ?? this.parentId,
      createdAt: createdAt ?? this.createdAt,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      ttsSpeed: ttsSpeed ?? this.ttsSpeed,
      volume: volume ?? this.volume,
      subtitlesEnabled: subtitlesEnabled ?? this.subtitlesEnabled,
      theme: theme ?? this.theme,
      storiesCompleted: storiesCompleted ?? this.storiesCompleted,
      totalReadingTime: totalReadingTime ?? this.totalReadingTime,
      favoriteStories: favoriteStories ?? this.favoriteStories,
      completedStories: completedStories ?? this.completedStories,
      currentProgress: currentProgress ?? this.currentProgress,
      dailyTimeLimitMinutes: dailyTimeLimitMinutes ?? this.dailyTimeLimitMinutes,
      contentFilterEnabled: contentFilterEnabled ?? this.contentFilterEnabled,
      allowedCategories: allowedCategories ?? this.allowedCategories,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      earnedBadges: earnedBadges ?? this.earnedBadges,
      totalBadges: totalBadges ?? this.totalBadges,
      badgesByType: badgesByType ?? this.badgesByType,
    );
  }

  /// Converts the profile to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'age': age,
      'avatar_path': avatarPath,
      'parent_id': parentId,
      'created_at': createdAt.toIso8601String(),
      'last_active_at': lastActiveAt.toIso8601String(),
      'preferred_language': preferredLanguage,
      'tts_speed': ttsSpeed,
      'volume': volume,
      'subtitles_enabled': subtitlesEnabled,
      'theme': theme,
      'stories_completed': storiesCompleted,
      'total_reading_time': totalReadingTime,
      'favorite_stories': favoriteStories,
      'completed_stories': completedStories,
      'current_progress': currentProgress,
      'daily_time_limit_minutes': dailyTimeLimitMinutes,
      'content_filter_enabled': contentFilterEnabled,
      'allowed_categories': allowedCategories,
      'notifications_enabled': notificationsEnabled,
      'earned_badges': earnedBadges,
      'total_badges': totalBadges,
      'badges_by_type': badgesByType,
    };
  }

  /// Creates a profile from a JSON map
  factory ChildProfileModel.fromJson(Map<String, dynamic> json) {
    return ChildProfileModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      age: json['age'] ?? 5,
      avatarPath: json['avatar_path'] ?? 'assets/avatars/default.png',
      parentId: json['parent_id'] ?? '',
      createdAt: DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
      lastActiveAt: DateTime.tryParse(json['last_active_at'] ?? '') ?? DateTime.now(),
      preferredLanguage: json['preferred_language'] ?? 'en-US',
      ttsSpeed: (json['tts_speed'] ?? 1.0).toDouble(),
      volume: (json['volume'] ?? 1.0).toDouble(),
      subtitlesEnabled: json['subtitles_enabled'] ?? true,
      theme: json['theme'] ?? 'default',
      storiesCompleted: json['stories_completed'] ?? 0,
      totalReadingTime: json['total_reading_time'] ?? 0,
      favoriteStories: List<String>.from(json['favorite_stories'] ?? []),
      completedStories: List<String>.from(json['completed_stories'] ?? []),
      currentProgress: Map<String, dynamic>.from(json['current_progress'] ?? {}),
      dailyTimeLimitMinutes: json['daily_time_limit_minutes'] ?? 60,
      contentFilterEnabled: json['content_filter_enabled'] ?? true,
      allowedCategories: List<String>.from(json['allowed_categories'] ?? ['educational', 'adventure', 'friendship']),
      notificationsEnabled: json['notifications_enabled'] ?? true,
      earnedBadges: List<String>.from(json['earned_badges'] ?? []),
      totalBadges: json['total_badges'] ?? 0,
      badgesByType: Map<String, int>.from(json['badges_by_type'] ?? {}),
    );
  }

  /// Gets reading progress percentage (0-100)
  double get readingProgressPercentage {
    if (storiesCompleted == 0) return 0.0;
    // Assuming there are about 50 stories total (this could be dynamic)
    const totalAvailableStories = 50;
    return (storiesCompleted / totalAvailableStories * 100).clamp(0.0, 100.0);
  }

  /// Gets daily reading time in minutes for today
  int get todayReadingTime {
    // This would typically be calculated from a separate tracking system
    // For now, return a placeholder
    return 0;
  }

  /// Checks if daily time limit is reached
  bool get isDailyTimeLimitReached {
    return todayReadingTime >= dailyTimeLimitMinutes;
  }

  /// Gets remaining reading time for today
  int get remainingReadingTime {
    return (dailyTimeLimitMinutes - todayReadingTime).clamp(0, dailyTimeLimitMinutes);
  }

  /// Gets the child's current level based on stories completed
  int get currentLevel {
    if (storiesCompleted < 5) return 1;
    if (storiesCompleted < 15) return 2;
    if (storiesCompleted < 30) return 3;
    if (storiesCompleted < 50) return 4;
    return 5;
  }

  /// Gets the level name
  String get levelName {
    switch (currentLevel) {
      case 1:
        return 'Beginning Reader';
      case 2:
        return 'Growing Reader';
      case 3:
        return 'Confident Reader';
      case 4:
        return 'Advanced Reader';
      case 5:
        return 'Master Reader';
      default:
        return 'Reader';
    }
  }

  /// Gets available avatar options
  static List<String> get availableAvatars {
    return [
      'assets/avatars/boy1.png',
      'assets/avatars/boy2.png',
      'assets/avatars/boy3.png',
      'assets/avatars/girl1.png',
      'assets/avatars/girl2.png',
      'assets/avatars/girl3.png',
      'assets/avatars/animal1.png',
      'assets/avatars/animal2.png',
      'assets/avatars/animal3.png',
    ];
  }

  /// Gets available themes
  static List<String> get availableThemes {
    return [
      'default',
      'forest',
      'ocean',
      'space',
      'fairy_tale',
      'adventure',
    ];
  }

  /// Validates profile data
  bool get isValid {
    return name.isNotEmpty && 
           age >= 3 && age <= 12 && 
           avatarPath.isNotEmpty &&
           parentId.isNotEmpty;
  }

  @override
  String toString() {
    return 'ChildProfileModel(id: $id, name: $name, age: $age, level: $currentLevel)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChildProfileModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
