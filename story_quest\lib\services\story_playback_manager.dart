/// Story playback state manager for Story Quest app
///
/// Manages all story playback states, transitions, and business logic
/// with comprehensive error handling and offline support.
library;

import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/story_play_state.dart';
import '../models/story_model.dart';
import '../models/scene_model.dart';
import '../models/choice_model.dart';
import '../services/api_service.dart';
import '../services/database_helper.dart';
import '../services/tts_service.dart';
import '../services/service_locator.dart';

/// Story playback state manager
class StoryPlaybackManager extends ChangeNotifier {
  static final StoryPlaybackManager _instance = StoryPlaybackManager._internal();
  factory StoryPlaybackManager() => _instance;
  StoryPlaybackManager._internal();

  // Services
  final ApiService _apiService = getIt<ApiService>();
  final DatabaseHelper _databaseHelper = getIt<DatabaseHelper>();
  final TtsService _ttsService = getIt<TtsService>();

  // Current state
  StoryPlaybackStateData _currentState = kInitialStoryPlaybackState;

  /// Gets the current playback state
  StoryPlaybackStateData get currentState => _currentState;

  /// Gets the current story
  Story? get currentStory => _currentState.story;

  /// Gets the current scene
  Scene? get currentScene => _currentState.currentScene;

  /// Checks if currently playing
  bool get isPlaying => _currentState.state == StoryPlaybackState.playing;

  /// Checks if currently paused
  bool get isPaused => _currentState.state == StoryPlaybackState.paused;

  /// Checks if in error state
  bool get hasError => _currentState.hasError;

  /// Initializes the playback manager
  Future<void> initialize() async {
    try {
      // Initialize TTS service
      await _ttsService.initialize();
      
      // Set initial state
      _updateState(_currentState.copyWith(
        state: StoryPlaybackState.idle,
      ));
    } catch (e) {
      _handleError(StoryPlaybackError.unknown, 'Failed to initialize playback manager: $e');
    }
  }

  /// Starts loading a story by ID
  Future<void> loadStory(String storyId) async {
    if (_currentState.state == StoryPlaybackState.loading) {
      return; // Already loading
    }

    _updateState(_currentState.copyWith(
      state: StoryPlaybackState.loading,
    ).clearError());

    try {
      // Check connectivity
      final connectivity = await Connectivity().checkConnectivity();
      final isOffline = connectivity.contains(ConnectivityResult.none);

      Story? story;

      if (!isOffline) {
        // Try loading from API first
        try {
          story = await _apiService.getStory(storyId);
          if (story != null) {
            // Cache the story locally
            await _databaseHelper.insertStory(story);
          }
        } catch (e) {
          debugPrint('Failed to load story from API: $e');
        }
      }

      // Fallback to local storage
      if (story == null) {
        story = await _databaseHelper.getStory(storyId);
      }

      if (story == null) {
        _handleError(
          StoryPlaybackError.loadingFailed,
          'Story not found. Please check your connection and try again.',
        );
        return;
      }

      // Validate story data
      if (story.scenes.isEmpty) {
        _handleError(
          StoryPlaybackError.dataCorruption,
          'Story data is incomplete. Please try downloading again.',
        );
        return;
      }

      // Successfully loaded
      _updateState(_currentState.copyWith(
        state: StoryPlaybackState.playing,
        story: story,
        currentScene: story.scenes.first,
        currentSceneIndex: 0,
        isOffline: isOffline,
        progress: 0.0,
      ).clearError());

      // Start narrating the first scene
      await _startNarration();

    } catch (e) {
      _handleError(
        StoryPlaybackError.loadingFailed,
        'Failed to load story: ${e.toString()}',
      );
    }
  }

  /// Starts narrating the current scene
  Future<void> _startNarration() async {
    final scene = _currentState.currentScene;
    if (scene == null) return;

    try {
      _updateState(_currentState.copyWith(isNarrating: true));

      // Use TTS to narrate the scene text
      await _ttsService.speak(scene.text);

      // Check for choices or moral moments after narration
      await _checkSceneCompletion();

    } catch (e) {
      _handleError(
        StoryPlaybackError.audioError,
        'Failed to play audio. Continuing with text only.',
      );
      
      // Continue without audio
      await _checkSceneCompletion();
    }
  }

  /// Checks what to do after scene narration completes
  Future<void> _checkSceneCompletion() async {
    final scene = _currentState.currentScene;
    if (scene == null) return;

    _updateState(_currentState.copyWith(isNarrating: false));

    // Check for choices
    if (scene.choices.isNotEmpty) {
      _updateState(_currentState.copyWith(
        state: StoryPlaybackState.choicePrompt,
        availableChoices: scene.choices,
      ));
      return;
    }

    // Check for moral reflection
    if (scene.reflection != null && scene.reflection!.isNotEmpty) {
      _updateState(_currentState.copyWith(
        state: StoryPlaybackState.moralMoment,
        moralReflection: scene.reflection,
      ));
      return;
    }

    // No choices or reflection, continue to next scene
    await _proceedToNextScene();
  }

  /// Handles user choice selection
  Future<void> selectChoice(Choice choice) async {
    if (_currentState.state != StoryPlaybackState.choicePrompt) {
      return;
    }

    try {
      final story = _currentState.story;
      if (story == null) {
        _handleError(StoryPlaybackError.choiceError, 'Story data not available');
        return;
      }

      // Find the next scene based on choice
      final nextSceneIndex = story.scenes.indexWhere(
        (scene) => scene.id == choice.nextSceneId,
      );

      if (nextSceneIndex == -1) {
        _handleError(StoryPlaybackError.choiceError, 'Invalid choice destination');
        return;
      }

      // Update to the chosen scene
      final nextScene = story.scenes[nextSceneIndex];
      final progress = (nextSceneIndex + 1) / story.scenes.length;

      _updateState(_currentState.copyWith(
        state: StoryPlaybackState.playing,
        currentScene: nextScene,
        currentSceneIndex: nextSceneIndex,
        progress: progress,
        availableChoices: [],
      ));

      // Start narrating the new scene
      await _startNarration();

    } catch (e) {
      _handleError(
        StoryPlaybackError.choiceError,
        'Failed to process choice: ${e.toString()}',
      );
    }
  }

  /// Acknowledges moral reflection and continues
  Future<void> acknowledgeMoralMoment() async {
    if (_currentState.state != StoryPlaybackState.moralMoment) {
      return;
    }

    _updateState(_currentState.copyWith(
      state: StoryPlaybackState.playing,
      moralReflection: null,
    ));

    // Continue to next scene
    await _proceedToNextScene();
  }

  /// Proceeds to the next scene in sequence
  Future<void> _proceedToNextScene() async {
    final story = _currentState.story;
    if (story == null) return;

    final nextIndex = _currentState.currentSceneIndex + 1;

    // Check if story is complete
    if (nextIndex >= story.scenes.length) {
      _updateState(_currentState.copyWith(
        state: StoryPlaybackState.completed,
        progress: 1.0,
      ));
      return;
    }

    // Move to next scene
    final nextScene = story.scenes[nextIndex];
    final progress = (nextIndex + 1) / story.scenes.length;

    _updateState(_currentState.copyWith(
      currentScene: nextScene,
      currentSceneIndex: nextIndex,
      progress: progress,
    ));

    // Start narrating the new scene
    await _startNarration();
  }

  /// Pauses story playback
  Future<void> pauseStory() async {
    if (_currentState.state != StoryPlaybackState.playing) {
      return;
    }

    try {
      // Stop TTS
      await _ttsService.stop();

      _updateState(_currentState.copyWith(
        state: StoryPlaybackState.paused,
        isNarrating: false,
        canResume: true,
      ));

    } catch (e) {
      _handleError(
        StoryPlaybackError.audioError,
        'Failed to pause audio',
      );
    }
  }

  /// Resumes story playback
  Future<void> resumeStory() async {
    if (_currentState.state != StoryPlaybackState.paused) {
      return;
    }

    try {
      _updateState(_currentState.copyWith(
        state: StoryPlaybackState.playing,
        canResume: false,
      ));

      // Resume narration if there's current scene text
      final scene = _currentState.currentScene;
      if (scene != null) {
        await _startNarration();
      }

    } catch (e) {
      _handleError(
        StoryPlaybackError.resumeError,
        'Failed to resume playback: ${e.toString()}',
      );
    }
  }

  /// Stops story playback and returns to idle
  Future<void> stopStory() async {
    try {
      // Stop TTS
      await _ttsService.stop();

      _updateState(kInitialStoryPlaybackState);

    } catch (e) {
      debugPrint('Error stopping story: $e');
      // Force reset to idle even if stop fails
      _updateState(kInitialStoryPlaybackState);
    }
  }

  /// Retries loading after an error
  Future<void> retryAfterError() async {
    if (!_currentState.hasError) return;

    final story = _currentState.story;
    if (story != null) {
      // Retry from current position
      _updateState(_currentState.copyWith(
        state: StoryPlaybackState.playing,
      ).clearError());
      
      await _startNarration();
    } else {
      // Need to reload story completely
      _updateState(_currentState.copyWith(
        state: StoryPlaybackState.idle,
      ).clearError());
    }
  }

  /// Handles errors and updates state
  void _handleError(StoryPlaybackError error, String message) {
    debugPrint('StoryPlaybackManager Error: $error - $message');
    
    _updateState(_currentState.setError(error, message));
  }

  /// Updates the current state and notifies listeners
  void _updateState(StoryPlaybackStateData newState) {
    if (_currentState != newState) {
      _currentState = newState;
      notifyListeners();
      
      debugPrint('Story state changed: ${newState.state} - ${newState.stateDescription}');
    }
  }

  /// Disposes resources
  @override
  void dispose() {
    _ttsService.stop();
    super.dispose();
  }
}
