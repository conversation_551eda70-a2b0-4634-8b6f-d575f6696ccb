/// Badge service for managing user achievements and rewards
///
/// Handles badge creation, storage, retrieval, and validation.
/// Integrates with database service for persistence.
library;

import 'dart:convert';
import '../models/badge_model.dart';
import '../models/story_model.dart';
import '../models/choice_model.dart';
import 'database_service.dart';
import 'service_locator.dart';

/// Service for managing badges and achievements
class BadgeService {
  final DatabaseService _databaseService;
  
  /// Cache of user badges
  final Map<String, List<BadgeModel>> _badgeCache = {};

  BadgeService(this._databaseService);

  /// Initializes the badge service
  Future<void> initialize() async {
    await _createBadgeTables();
  }

  /// Creates database tables for badges if they don't exist
  Future<void> _createBadgeTables() async {
    final db = await _databaseService.database;
    
    await db.execute('''
      CREATE TABLE IF NOT EXISTS badges (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        type TEXT NOT NULL,
        rarity TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        story_id TEXT NOT NULL,
        scene_id TEXT NOT NULL,
        choice_text TEXT NOT NULL,
        earned_at INTEGER NOT NULL,
        is_new INTEGER NOT NULL DEFAULT 1
      )
    ''');

    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_badges_user_id ON badges(user_id)
    ''');

    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_badges_type ON badges(type)
    ''');
  }

  /// Awards a badge based on a story choice
  Future<BadgeModel?> awardBadgeForChoice({
    required String userId,
    required StoryModel story,
    required String sceneId,
    required ChoiceModel choice,
  }) async {
    // Determine if this choice deserves a badge
    final badgeType = BadgeCriteria.determineBadgeType(choice.option);
    if (badgeType == null) {
      return null; // No badge for this choice
    }

    // Check if user already has this type of badge for this story
    final existingBadges = await getBadgesForUser(userId);
    final hasSimilarBadge = existingBadges.any((badge) => 
        badge.type == badgeType && badge.storyId == story.storyId);
    
    if (hasSimilarBadge) {
      return null; // Don't award duplicate badges for same story
    }

    // Create the badge
    final badge = _createBadge(
      userId: userId,
      story: story,
      sceneId: sceneId,
      choice: choice,
      badgeType: badgeType,
    );

    // Save to database
    await _saveBadge(badge, userId);

    // Update cache
    _badgeCache[userId] = [...(existingBadges), badge];

    return badge;
  }

  /// Creates a badge instance
  BadgeModel _createBadge({
    required String userId,
    required StoryModel story,
    required String sceneId,
    required ChoiceModel choice,
    required BadgeType badgeType,
  }) {
    final rarity = BadgeCriteria.determineBadgeRarity(story.difficulty, choice.option);
    final name = BadgeCriteria.createBadgeName(badgeType, story.title);
    final description = BadgeCriteria.createBadgeDescription(badgeType, choice.option);
    
    return BadgeModel(
      id: _generateBadgeId(userId, story.storyId, sceneId),
      type: badgeType,
      rarity: rarity,
      name: name,
      description: description,
      storyId: story.storyId,
      sceneId: sceneId,
      choiceText: choice.option,
      earnedAt: DateTime.now(),
      isNew: true,
    );
  }

  /// Generates a unique badge ID
  String _generateBadgeId(String userId, String storyId, String sceneId) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'badge_${userId}_${storyId}_${sceneId}_$timestamp';
  }

  /// Saves a badge to the database
  Future<void> _saveBadge(BadgeModel badge, String userId) async {
    final db = await _databaseService.database;
    
    await db.insert('badges', {
      'id': badge.id,
      'user_id': userId,
      'type': badge.type.name,
      'rarity': badge.rarity.name,
      'name': badge.name,
      'description': badge.description,
      'story_id': badge.storyId,
      'scene_id': badge.sceneId,
      'choice_text': badge.choiceText,
      'earned_at': badge.earnedAt.millisecondsSinceEpoch,
      'is_new': badge.isNew ? 1 : 0,
    });
  }

  /// Gets all badges for a user
  Future<List<BadgeModel>> getBadgesForUser(String userId) async {
    // Check cache first
    if (_badgeCache.containsKey(userId)) {
      return _badgeCache[userId]!;
    }

    final db = await _databaseService.database;
    final results = await db.query(
      'badges',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'earned_at DESC',
    );

    final badges = results.map((row) => _badgeFromDatabaseRow(row)).toList();
    
    // Cache the results
    _badgeCache[userId] = badges;
    
    return badges;
  }

  /// Gets badges by type for a user
  Future<List<BadgeModel>> getBadgesByType(String userId, BadgeType type) async {
    final allBadges = await getBadgesForUser(userId);
    return allBadges.where((badge) => badge.type == type).toList();
  }

  /// Gets new (unviewed) badges for a user
  Future<List<BadgeModel>> getNewBadges(String userId) async {
    final allBadges = await getBadgesForUser(userId);
    return allBadges.where((badge) => badge.isNew).toList();
  }

  /// Marks badges as viewed (no longer new)
  Future<void> markBadgesAsViewed(String userId, List<String> badgeIds) async {
    final db = await _databaseService.database;
    
    for (final badgeId in badgeIds) {
      await db.update(
        'badges',
        {'is_new': 0},
        where: 'id = ? AND user_id = ?',
        whereArgs: [badgeId, userId],
      );
    }

    // Update cache
    if (_badgeCache.containsKey(userId)) {
      _badgeCache[userId] = _badgeCache[userId]!.map((badge) {
        if (badgeIds.contains(badge.id)) {
          return badge.copyWith(isNew: false);
        }
        return badge;
      }).toList();
    }
  }

  /// Gets badge statistics for a user
  Future<Map<String, dynamic>> getBadgeStats(String userId) async {
    final badges = await getBadgesForUser(userId);
    
    final stats = <String, dynamic>{
      'total': badges.length,
      'new': badges.where((b) => b.isNew).length,
      'byType': <String, int>{},
      'byRarity': <String, int>{},
    };

    // Count by type
    for (final type in BadgeType.values) {
      final count = badges.where((b) => b.type == type).length;
      stats['byType'][type.displayName] = count;
    }

    // Count by rarity
    for (final rarity in BadgeRarity.values) {
      final count = badges.where((b) => b.rarity == rarity).length;
      stats['byRarity'][rarity.displayName] = count;
    }

    return stats;
  }

  /// Converts database row to BadgeModel
  BadgeModel _badgeFromDatabaseRow(Map<String, dynamic> row) {
    return BadgeModel(
      id: row['id'] as String,
      type: BadgeType.values.firstWhere(
        (type) => type.name == row['type'],
        orElse: () => BadgeType.kindness,
      ),
      rarity: BadgeRarity.values.firstWhere(
        (rarity) => rarity.name == row['rarity'],
        orElse: () => BadgeRarity.common,
      ),
      name: row['name'] as String,
      description: row['description'] as String,
      storyId: row['story_id'] as String,
      sceneId: row['scene_id'] as String,
      choiceText: row['choice_text'] as String,
      earnedAt: DateTime.fromMillisecondsSinceEpoch(row['earned_at'] as int),
      isNew: (row['is_new'] as int) == 1,
    );
  }

  /// Clears the badge cache for a user
  void clearCache(String userId) {
    _badgeCache.remove(userId);
  }

  /// Clears all badge caches
  void clearAllCaches() {
    _badgeCache.clear();
  }

  /// Deletes all badges for a user (for testing/reset purposes)
  Future<void> deleteAllBadgesForUser(String userId) async {
    final db = await _databaseService.database;
    await db.delete('badges', where: 'user_id = ?', whereArgs: [userId]);
    clearCache(userId);
  }

  /// Gets the total number of possible badges
  int get totalPossibleBadges => BadgeType.values.length * BadgeRarity.values.length;

  /// Calculates completion percentage for a user
  Future<double> getCompletionPercentage(String userId) async {
    final badges = await getBadgesForUser(userId);
    return badges.length / totalPossibleBadges;
  }
}
