/// Story Library screen for browsing available stories
/// 
/// Features a 3x3 grid layout with genre filters, search functionality,
/// and offline indicators. Responsive design for all device types.
library;

import 'package:flutter/material.dart';
import '../../models/story_model.dart';
import '../../utils/accessibility_helper.dart';
import '../../utils/device_utils.dart';

/// Genre filter options
enum StoryGenre {
  all('All Stories', Icons.library_books),
  adventure('Adventure', Icons.explore),
  fantasy('Fantasy', Icons.auto_awesome),
  friendship('Friendship', Icons.favorite),
  learning('Learning', Icons.school),
  bedtime('Bedtime', Icons.bedtime);

  const StoryGenre(this.label, this.icon);
  final String label;
  final IconData icon;
}

/// Story library screen widget
class StoryLibraryScreen extends StatefulWidget {
  /// List of available stories
  final List<StoryModel> stories;
  
  /// List of downloaded story IDs
  final Set<String> downloadedStories;
  
  /// Callback when a story is selected
  final Function(StoryModel)? onStorySelected;
  
  /// Callback when download is requested
  final Function(StoryModel)? onDownloadStory;
  
  /// Callback for back navigation
  final VoidCallback? onBack;

  const StoryLibraryScreen({
    super.key,
    this.stories = const [],
    this.downloadedStories = const {},
    this.onStorySelected,
    this.onDownloadStory,
    this.onBack,
  });

  @override
  State<StoryLibraryScreen> createState() => _StoryLibraryScreenState();
}

class _StoryLibraryScreenState extends State<StoryLibraryScreen>
    with TickerProviderStateMixin {
  StoryGenre _selectedGenre = StoryGenre.all;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _filterAnimationController;
  late Animation<double> _filterAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize filter animation
    _filterAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _filterAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _filterAnimationController,
      curve: Curves.easeInOut,
    ));

    _filterAnimationController.forward();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _filterAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveLayoutBuilder(
      builder: (context, deviceType, orientation) {
        final crossAxisCount = DeviceUtils.getResponsiveGridColumns(context);

        return Scaffold(
          backgroundColor: const Color(0xFF4CAF50), // Green background
          body: SafeArea(
            child: Column(
              children: [
                _buildHeader(),
                _buildFilters(),
                if (_shouldShowSearch()) _buildSearchBar(),
                Expanded(
                  child: _buildStoryGrid(crossAxisCount),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Builds the header with title and back button
  Widget _buildHeader() {
    return Padding(
      padding: DeviceUtils.getResponsivePadding(context),
      child: Row(
        children: [
          // Back button
          AccessibilityHelper.createAccessibleButton(
            child: Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: DeviceUtils.getResponsiveIconSize(context, 24),
            ),
            onPressed: widget.onBack,
            semanticLabel: 'Go back to homepage',
          ),

          SizedBox(width: DeviceUtils.getResponsiveSpacing(context, 16)),

          // Title
          Expanded(
            child: AccessibilityHelper.createAccessibleText(
              'Story Library',
              style: TextStyle(
                fontSize: DeviceUtils.getResponsiveFontSize(context, 28),
                fontWeight: FontWeight.bold,
                color: Colors.white,
                shadows: const [
                  Shadow(
                    color: Colors.black,
                    offset: Offset(1, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
              semanticLabel: 'Story Library - Browse and select stories',
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the genre filter tabs
  Widget _buildFilters() {
    return AnimatedBuilder(
      animation: _filterAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _filterAnimation.value,
          child: Container(
            height: DeviceUtils.getResponsiveButtonHeight(context) * 1.2,
            margin: EdgeInsets.symmetric(
              horizontal: DeviceUtils.getResponsiveSpacing(context, 16),
            ),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: StoryGenre.values.length,
              itemBuilder: (context, index) {
                final genre = StoryGenre.values[index];
                final isSelected = genre == _selectedGenre;

                return Padding(
                  padding: EdgeInsets.only(
                    right: DeviceUtils.getResponsiveSpacing(context, 8),
                  ),
                  child: AccessibilityHelper.createAccessibleButton(
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: DeviceUtils.getResponsiveSpacing(context, 16),
                        vertical: DeviceUtils.getResponsiveSpacing(context, 8),
                      ),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? const Color(0xFF1976D2) // Blue
                            : Colors.white.withValues(alpha: 0.9),
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            genre.icon,
                            color: isSelected ? Colors.white : const Color(0xFF1976D2),
                            size: DeviceUtils.getResponsiveIconSize(context, 20),
                          ),
                          SizedBox(width: DeviceUtils.getResponsiveSpacing(context, 8)),
                          Text(
                            genre.label,
                            style: TextStyle(
                              color: isSelected ? Colors.white : const Color(0xFF1976D2),
                              fontWeight: FontWeight.bold,
                              fontSize: DeviceUtils.getResponsiveFontSize(context, 14),
                            ),
                          ),
                        ],
                      ),
                    ),
                    onPressed: () => _selectGenre(genre),
                    semanticLabel: 'Filter by ${genre.label}',
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  /// Builds the search bar (shown for ages 9-12)
  Widget _buildSearchBar() {
    return Container(
      margin: DeviceUtils.getResponsivePadding(context),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: AccessibilityHelper.createAccessibleFormField(
        label: 'Search stories',
        hint: 'Type to search...',
        controller: _searchController,
        onChanged: (value) {
          setState(() {
            _searchQuery = value.toLowerCase();
          });
        },
      ),
    );
  }

  /// Builds the story grid
  Widget _buildStoryGrid(int crossAxisCount) {
    final filteredStories = _getFilteredStories();

    if (filteredStories.isEmpty) {
      return _buildEmptyState();
    }

    return Padding(
      padding: DeviceUtils.getResponsivePadding(context),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: DeviceUtils.getResponsiveSpacing(context, 16),
          mainAxisSpacing: DeviceUtils.getResponsiveSpacing(context, 16),
          childAspectRatio: 120 / 180, // Exact 120px x 180px ratio
        ),
        itemCount: filteredStories.length,
        itemBuilder: (context, index) {
          final story = filteredStories[index];
          final isDownloaded = widget.downloadedStories.contains(story.storyId);

          return _buildStoryCard(story, isDownloaded);
        },
      ),
    );
  }

  /// Builds individual story card
  Widget _buildStoryCard(StoryModel story, bool isDownloaded) {

    return AccessibilityHelper.createAccessibleButton(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _getDifficultyBorderColor(story.difficulty),
            width: 3,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Cover image (70% of card)
            Expanded(
              flex: 7,
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(10),
                    ),
                    child: SizedBox(
                      width: double.infinity,
                      child: _buildStoryCoverImage(story),
                    ),
                  ),
                  
                  // Offline indicator
                  if (isDownloaded)
                    Positioned(
                      top: DeviceUtils.getResponsiveSpacing(context, 8),
                      right: DeviceUtils.getResponsiveSpacing(context, 8),
                      child: Container(
                        padding: EdgeInsets.all(DeviceUtils.getResponsiveSpacing(context, 4)),
                        decoration: const BoxDecoration(
                          color: Color(0xFF4CAF50),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.check,
                          color: Colors.white,
                          size: DeviceUtils.getResponsiveIconSize(context, 16),
                        ),
                      ),
                    ),

                  // Age badge
                  Positioned(
                    top: DeviceUtils.getResponsiveSpacing(context, 8),
                    left: DeviceUtils.getResponsiveSpacing(context, 8),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: DeviceUtils.getResponsiveSpacing(context, 8),
                        vertical: DeviceUtils.getResponsiveSpacing(context, 4),
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        story.ageGroup,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: DeviceUtils.getResponsiveFontSize(context, 12),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Metadata (30% of card)
            Expanded(
              flex: 3,
              child: Padding(
                padding: EdgeInsets.all(DeviceUtils.getResponsiveSpacing(context, 8)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      story.title,
                      style: TextStyle(
                        fontSize: DeviceUtils.getResponsiveFontSize(context, 14),
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const Spacer(),

                    // Play/Download button
                    SizedBox(
                      width: double.infinity,
                      height: DeviceUtils.getResponsiveButtonHeight(context) * 0.8,
                      child: ElevatedButton(
                        onPressed: () => _handleStoryAction(story, isDownloaded),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF4CAF50),
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          elevation: 2,
                        ),
                        child: Text(
                          isDownloaded ? 'Play' : 'Download',
                          style: TextStyle(
                            fontSize: DeviceUtils.getResponsiveFontSize(context, 12),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      onPressed: () => widget.onStorySelected?.call(story),
      semanticLabel: '${story.title}, Age ${story.ageGroup}, ${isDownloaded ? 'Downloaded' : 'Available for download'}',
    );
  }

  /// Builds empty state when no stories match filters
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: DeviceUtils.getResponsiveIconSize(context, 64),
            color: Colors.white.withValues(alpha: 0.7),
          ),
          SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 20)),
          AccessibilityHelper.createAccessibleText(
            'No stories found',
            style: TextStyle(
              fontSize: DeviceUtils.getResponsiveFontSize(context, 24),
              fontWeight: FontWeight.bold,
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
          SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 12)),
          AccessibilityHelper.createAccessibleText(
            'Try adjusting your filters or search terms',
            style: TextStyle(
              fontSize: DeviceUtils.getResponsiveFontSize(context, 16),
              color: Colors.white.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// Selects a genre filter
  void _selectGenre(StoryGenre genre) {
    setState(() {
      _selectedGenre = genre;
    });
    
    AccessibilityHelper.announceToScreenReader('Filtered by ${genre.label}');
  }

  /// Handles story action (play or download)
  void _handleStoryAction(StoryModel story, bool isDownloaded) {
    if (isDownloaded) {
      widget.onStorySelected?.call(story);
    } else {
      widget.onDownloadStory?.call(story);
    }
  }

  /// Determines if search should be shown based on age group
  bool _shouldShowSearch() {
    // Show search for older children (9-12 age group)
    return true; // For now, always show search
  }

  /// Gets filtered stories based on genre and search
  List<StoryModel> _getFilteredStories() {
    var filtered = widget.stories;

    // Filter by genre
    if (_selectedGenre != StoryGenre.all) {
      // This would filter by actual genre field in the story model
      // For now, we'll show all stories
    }

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((story) {
        return story.title.toLowerCase().contains(_searchQuery) ||
               story.moral.toLowerCase().contains(_searchQuery);
      }).toList();
    }

    return filtered;
  }

  /// Gets border color based on story difficulty
  Color _getDifficultyBorderColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return const Color(0xFF4CAF50); // Green
      case 'medium':
        return const Color(0xFFFF9800); // Orange
      case 'hard':
        return const Color(0xFFF44336); // Red
      default:
        return const Color(0xFF2196F3); // Blue (fallback)
    }
  }

  /// Builds story cover image with fallback handling
  Widget _buildStoryCoverImage(StoryModel story) {
    // Construct the specific cover image path
    final coverImagePath = 'assets/${story.storyId}/images/story_cover.jpg';

    return Image.asset(
      coverImagePath,
      width: double.infinity,
      height: double.infinity,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        // Fallback to default image if story-specific cover not found
        return Image.asset(
          'assets/default/default_image.png',
          width: double.infinity,
          height: double.infinity,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            // Final fallback if even default image fails
            return Container(
              width: double.infinity,
              height: double.infinity,
              color: const Color(0xFFE3F2FD),
              child: Icon(
                Icons.auto_stories,
                size: 40,
                color: _getDifficultyBorderColor(story.difficulty),
              ),
            );
          },
        );
      },
    );
  }
}
