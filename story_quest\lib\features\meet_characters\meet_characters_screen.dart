/// Meet Characters screen
/// 
/// Interactive character carousel with audio previews, swipe gestures,
/// and navigation controls. Introduces story characters before playback.
library;

import 'package:flutter/material.dart';
import '../../models/story_model.dart';
import '../../models/character_model.dart';
import '../../utils/accessibility_helper.dart';
import '../../services/service_locator.dart';

/// Meet Characters screen widget
class MeetCharactersScreen extends StatefulWidget {
  /// The story containing characters to display
  final StoryModel story;
  
  /// Callback when Play Story is pressed
  final VoidCallback? onPlayStory;
  
  /// Callback for back navigation
  final VoidCallback? onBack;

  const MeetCharactersScreen({
    super.key,
    required this.story,
    this.onPlayStory,
    this.onBack,
  });

  @override
  State<MeetCharactersScreen> createState() => _MeetCharactersScreenState();
}

class _MeetCharactersScreenState extends State<MeetCharactersScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;
  int _currentIndex = 0;
  bool _isPlayingAudio = false;

  @override
  void initState() {
    super.initState();
    
    _pageController = PageController(
      viewportFraction: 0.8,
      initialPage: 0,
    );

    // Initialize slide animation
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _slideController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    
    return Scaffold(
      body: Container(
        decoration: _buildBrightBackground(),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              const SizedBox(height: 20),
              _buildTitle(),
              const SizedBox(height: 30),
              Expanded(
                child: _buildCharacterCarousel(isTablet),
              ),
              _buildNavigationControls(),
              _buildActionButtons(),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds bright background decoration
  BoxDecoration _buildBrightBackground() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFFFFE0B2), // Light orange
          Color(0xFFFFF3E0), // Very light orange
          Color(0xFFE8F5E8), // Light green
        ],
      ),
    );
  }

  /// Builds the header with back button
  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          AccessibilityHelper.createAccessibleButton(
            child: const Icon(
              Icons.arrow_back,
              color: Color(0xFF2E7D32),
              size: 28,
            ),
            onPressed: widget.onBack,
            semanticLabel: 'Go back to story introduction',
          ),
        ],
      ),
    );
  }

  /// Builds the title section
  Widget _buildTitle() {
    return SlideTransition(
      position: _slideAnimation,
      child: Column(
        children: [
          AccessibilityHelper.createAccessibleText(
            'Meet the Characters',
            style: const TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2E7D32),
            ),
            semanticLabel: 'Meet the Characters from ${widget.story.title}',
          ),
          const SizedBox(height: 8),
          AccessibilityHelper.createAccessibleText(
            'Get to know who you\'ll meet in the story',
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF4CAF50),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the character carousel
  Widget _buildCharacterCarousel(bool isTablet) {
    if (widget.story.characters.isEmpty) {
      return _buildNoCharactersState();
    }

    final cardHeight = isTablet ? 300.0 : 250.0;
    
    return SizedBox(
      height: cardHeight,
      child: PageView.builder(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
          AccessibilityHelper.announceToScreenReader(
            'Character ${index + 1} of ${widget.story.characters.length}: ${widget.story.characters[index].name}'
          );
        },
        itemCount: widget.story.characters.length,
        itemBuilder: (context, index) {
          final character = widget.story.characters[index];
          final isActive = index == _currentIndex;
          
          return AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            margin: EdgeInsets.symmetric(
              horizontal: 8,
              vertical: isActive ? 0 : 20,
            ),
            child: _buildCharacterCard(character, isActive, isTablet),
          );
        },
      ),
    );
  }

  /// Builds individual character card
  Widget _buildCharacterCard(CharacterModel character, bool isActive, bool isTablet) {
    final cardWidth = isTablet ? 200.0 : 150.0;
    
    return AccessibilityHelper.createAccessibleButton(
      child: Container(
        width: cardWidth,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isActive ? const Color(0xFF4CAF50) : Colors.transparent,
            width: 3,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: isActive ? 0.3 : 0.1),
              blurRadius: isActive ? 15 : 8,
              offset: Offset(0, isActive ? 8 : 4),
            ),
          ],
        ),
        child: Column(
          children: [
            // Character image
            Expanded(
              flex: 3,
              child: Container(
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: character.voice.name.contains('female') 
                      ? const Color(0xFFE91E63).withValues(alpha: 0.1)
                      : const Color(0xFF2196F3).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Center(
                  child: Icon(
                    _getCharacterIcon(character),
                    size: isTablet ? 80 : 60,
                    color: character.voice.name.contains('female')
                        ? const Color(0xFFE91E63)
                        : const Color(0xFF2196F3),
                  ),
                ),
              ),
            ),
            
            // Character name
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: AccessibilityHelper.createAccessibleText(
                character.name,
                style: TextStyle(
                  fontSize: isTablet ? 20 : 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2E7D32),
                ),
                semanticLabel: 'Character name: ${character.name}',
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Character description
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: AccessibilityHelper.createAccessibleText(
                  character.description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF757575),
                  ),
                  maxLines: 3,
                  semanticLabel: 'Character description: ${character.description}',
                ),
              ),
            ),
            
            // Play Audio button
            Padding(
              padding: const EdgeInsets.all(16),
              child: _buildPlayAudioButton(character),
            ),
          ],
        ),
      ),
      onPressed: () => _selectCharacter(_currentIndex),
      semanticLabel: 'Character card for ${character.name}, ${character.role}',
      width: cardWidth,
    );
  }

  /// Builds play audio button for character
  Widget _buildPlayAudioButton(CharacterModel character) {
    return SizedBox(
      width: double.infinity,
      height: 40,
      child: ElevatedButton.icon(
        onPressed: () => _playCharacterAudio(character),
        icon: Icon(
          _isPlayingAudio ? Icons.stop : Icons.play_arrow,
          size: 20,
        ),
        label: Text(
          _isPlayingAudio ? 'Stop' : 'Play Audio',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFFCA28), // Yellow
          foregroundColor: Colors.black87,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 2,
        ),
      ),
    );
  }

  /// Builds navigation controls
  Widget _buildNavigationControls() {
    if (widget.story.characters.length <= 1) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 40),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Previous button
          AccessibilityHelper.createAccessibleButton(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _currentIndex > 0 
                    ? const Color(0xFFB0BEC5) 
                    : const Color(0xFFE0E0E0),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.arrow_back_ios,
                color: _currentIndex > 0 ? Colors.white : Colors.grey,
                size: 20,
              ),
            ),
            onPressed: _currentIndex > 0 ? _previousCharacter : null,
            semanticLabel: 'Previous character',
          ),
          
          // Page indicators
          Row(
            children: List.generate(
              widget.story.characters.length,
              (index) => Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: index == _currentIndex 
                      ? const Color(0xFF4CAF50)
                      : const Color(0xFFE0E0E0),
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),
          
          // Next button
          AccessibilityHelper.createAccessibleButton(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _currentIndex < widget.story.characters.length - 1
                    ? const Color(0xFFB0BEC5)
                    : const Color(0xFFE0E0E0),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.arrow_forward_ios,
                color: _currentIndex < widget.story.characters.length - 1 
                    ? Colors.white 
                    : Colors.grey,
                size: 20,
              ),
            ),
            onPressed: _currentIndex < widget.story.characters.length - 1 
                ? _nextCharacter 
                : null,
            semanticLabel: 'Next character',
          ),
        ],
      ),
    );
  }

  /// Builds action buttons
  Widget _buildActionButtons() {
    return SlideTransition(
      position: _slideAnimation,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 40),
        child: Column(
          children: [
            // Play Story button
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton.icon(
                onPressed: widget.onPlayStory,
                icon: const Icon(Icons.play_arrow, size: 28),
                label: const Text(
                  'Play Story',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4CAF50), // Green
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(28),
                  ),
                  elevation: 4,
                  shadowColor: const Color(0xFF4CAF50).withValues(alpha: 0.4),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Back button
            SizedBox(
              width: double.infinity,
              height: 48,
              child: OutlinedButton(
                onPressed: widget.onBack,
                style: OutlinedButton.styleFrom(
                  foregroundColor: const Color(0xFFB0BEC5),
                  side: const BorderSide(color: Color(0xFFB0BEC5), width: 2),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                ),
                child: const Text(
                  'Back',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds no characters state
  Widget _buildNoCharactersState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 80,
            color: const Color(0xFF4CAF50).withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          AccessibilityHelper.createAccessibleText(
            'No characters to meet',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4CAF50),
            ),
          ),
          const SizedBox(height: 8),
          AccessibilityHelper.createAccessibleText(
            'This story doesn\'t have character introductions',
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF757575),
            ),
          ),
        ],
      ),
    );
  }

  /// Gets appropriate icon for character
  IconData _getCharacterIcon(CharacterModel character) {
    final role = character.role.toLowerCase();
    if (role.contains('protagonist') || role.contains('main')) {
      return Icons.star;
    } else if (role.contains('friend')) {
      return Icons.favorite;
    } else if (role.contains('animal')) {
      return Icons.pets;
    } else if (role.contains('parent') || role.contains('adult')) {
      return Icons.person;
    }
    return Icons.face;
  }

  /// Navigates to previous character
  void _previousCharacter() {
    if (_currentIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// Navigates to next character
  void _nextCharacter() {
    if (_currentIndex < widget.story.characters.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// Selects a character
  void _selectCharacter(int index) {
    if (index != _currentIndex) {
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// Plays character audio preview
  void _playCharacterAudio(CharacterModel character) async {
    if (_isPlayingAudio) {
      // Stop current audio
      await serviceLocator.tts.stop();
      setState(() {
        _isPlayingAudio = false;
      });
    } else {
      // Play character introduction
      setState(() {
        _isPlayingAudio = true;
      });
      
      try {
        await serviceLocator.tts.setVoice(character.voice);
        await serviceLocator.tts.speak(
          'Hello! I\'m ${character.name}. ${character.description}',
          voiceConfig: character.voice,
        );
        
        // Reset state after speaking
        setState(() {
          _isPlayingAudio = false;
        });
      } catch (e) {
        setState(() {
          _isPlayingAudio = false;
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Unable to play character audio'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    }
  }
}
