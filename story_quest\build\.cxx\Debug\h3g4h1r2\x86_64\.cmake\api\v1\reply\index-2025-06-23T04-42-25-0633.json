{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/Install/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/Install/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/Install/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/Install/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-e454b6925a0521131015.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-05fe99461134aeab212f.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-0e70f6f60f51784af765.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-05fe99461134aeab212f.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-0e70f6f60f51784af765.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-e454b6925a0521131015.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}