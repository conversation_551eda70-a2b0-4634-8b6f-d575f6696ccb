/// Error handling utility for the Story Quest app
/// 
/// Provides centralized error handling, logging, and user-friendly
/// error messages with fallback mechanisms.
library;

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

/// Types of errors that can occur in the app
enum ErrorType {
  network,
  storage,
  tts,
  parsing,
  permission,
  unknown,
}

/// Error severity levels
enum ErrorSeverity {
  low,    // Minor issues, app continues normally
  medium, // Some functionality affected
  high,   // Major functionality broken
  critical, // App cannot continue
}

/// Custom exception class for Story Quest errors
class StoryQuestException implements Exception {
  final String message;
  final ErrorType type;
  final ErrorSeverity severity;
  final dynamic originalError;
  final StackTrace? stackTrace;

  const StoryQuestException({
    required this.message,
    required this.type,
    this.severity = ErrorSeverity.medium,
    this.originalError,
    this.stackTrace,
  });

  @override
  String toString() {
    return 'StoryQuestException: $message (Type: $type, Severity: $severity)';
  }
}

/// Centralized error handler for the application
class ErrorHandler {
  static final List<String> _errorLog = [];
  static const int _maxLogEntries = 100;

  /// Handles an error and shows appropriate user feedback
  static Future<void> handleError(
    BuildContext? context,
    dynamic error, {
    ErrorType? type,
    ErrorSeverity? severity,
    String? userMessage,
    bool showSnackBar = true,
  }) async {
    // Determine error type and severity
    final errorType = type ?? _determineErrorType(error);
    final errorSeverity = severity ?? _determineErrorSeverity(error);
    
    // Log the error
    _logError(error, errorType, errorSeverity);

    // Get user-friendly message
    final message = userMessage ?? _getUserFriendlyMessage(errorType, error);

    // Show user feedback if context is available
    if (context != null && showSnackBar) {
      _showErrorSnackBar(context, message, errorSeverity);
    }

    // Log to console in debug mode
    if (kDebugMode) {
      debugPrint('Error handled: $error');
      if (error is StoryQuestException && error.stackTrace != null) {
        debugPrint('Stack trace: ${error.stackTrace}');
      }
    }
  }

  /// Determines the error type from the error object
  static ErrorType _determineErrorType(dynamic error) {
    if (error is StoryQuestException) {
      return error.type;
    }

    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('network') || 
        errorString.contains('connection') ||
        errorString.contains('timeout')) {
      return ErrorType.network;
    }
    
    if (errorString.contains('storage') || 
        errorString.contains('file') ||
        errorString.contains('directory')) {
      return ErrorType.storage;
    }
    
    if (errorString.contains('tts') || 
        errorString.contains('speech') ||
        errorString.contains('voice')) {
      return ErrorType.tts;
    }
    
    if (errorString.contains('json') || 
        errorString.contains('parse') ||
        errorString.contains('format')) {
      return ErrorType.parsing;
    }
    
    if (errorString.contains('permission') || 
        errorString.contains('access')) {
      return ErrorType.permission;
    }

    return ErrorType.unknown;
  }

  /// Determines the error severity from the error object
  static ErrorSeverity _determineErrorSeverity(dynamic error) {
    if (error is StoryQuestException) {
      return error.severity;
    }

    final errorString = error.toString().toLowerCase();
    
    // Critical errors that prevent app from functioning
    if (errorString.contains('fatal') || 
        errorString.contains('critical') ||
        errorString.contains('out of memory')) {
      return ErrorSeverity.critical;
    }
    
    // High severity errors that break major functionality
    if (errorString.contains('database') || 
        errorString.contains('initialization') ||
        errorString.contains('authentication')) {
      return ErrorSeverity.high;
    }
    
    // Low severity errors that don't significantly impact functionality
    if (errorString.contains('asset not found') || 
        errorString.contains('cache') ||
        errorString.contains('preference')) {
      return ErrorSeverity.low;
    }

    return ErrorSeverity.medium;
  }

  /// Gets a user-friendly error message
  static String _getUserFriendlyMessage(ErrorType type, dynamic error) {
    switch (type) {
      case ErrorType.network:
        return 'Unable to connect to the internet. Please check your connection and try again.';
      
      case ErrorType.storage:
        return 'There was a problem accessing your device storage. Please ensure the app has proper permissions.';
      
      case ErrorType.tts:
        return 'Voice playback is currently unavailable. You can still read the story text.';
      
      case ErrorType.parsing:
        return 'There was a problem loading the story content. Please try again.';
      
      case ErrorType.permission:
        return 'The app needs certain permissions to work properly. Please check your device settings.';
      
      case ErrorType.unknown:
      default:
        return 'Something went wrong. Please try again, and if the problem persists, restart the app.';
    }
  }

  /// Shows an error snack bar to the user
  static void _showErrorSnackBar(
    BuildContext context,
    String message,
    ErrorSeverity severity,
  ) {
    final color = _getErrorColor(severity);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: color,
        duration: _getSnackBarDuration(severity),
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// Gets the appropriate color for the error severity
  static Color _getErrorColor(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return Colors.orange;
      case ErrorSeverity.medium:
        return Colors.deepOrange;
      case ErrorSeverity.high:
        return Colors.red;
      case ErrorSeverity.critical:
        return Colors.red[900]!;
    }
  }

  /// Gets the appropriate duration for the snack bar
  static Duration _getSnackBarDuration(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return const Duration(seconds: 3);
      case ErrorSeverity.medium:
        return const Duration(seconds: 5);
      case ErrorSeverity.high:
        return const Duration(seconds: 7);
      case ErrorSeverity.critical:
        return const Duration(seconds: 10);
    }
  }

  /// Logs an error to the internal log
  static void _logError(dynamic error, ErrorType type, ErrorSeverity severity) {
    final timestamp = DateTime.now().toIso8601String();
    final logEntry = '$timestamp - $type - $severity - $error';
    
    _errorLog.add(logEntry);
    
    // Keep log size manageable
    if (_errorLog.length > _maxLogEntries) {
      _errorLog.removeAt(0);
    }
  }

  /// Gets the current error log
  static List<String> getErrorLog() {
    return List.unmodifiable(_errorLog);
  }

  /// Clears the error log
  static void clearErrorLog() {
    _errorLog.clear();
  }

  /// Creates a network error
  static StoryQuestException networkError(String message, [dynamic originalError]) {
    return StoryQuestException(
      message: message,
      type: ErrorType.network,
      severity: ErrorSeverity.medium,
      originalError: originalError,
    );
  }

  /// Creates a storage error
  static StoryQuestException storageError(String message, [dynamic originalError]) {
    return StoryQuestException(
      message: message,
      type: ErrorType.storage,
      severity: ErrorSeverity.medium,
      originalError: originalError,
    );
  }

  /// Creates a TTS error
  static StoryQuestException ttsError(String message, [dynamic originalError]) {
    return StoryQuestException(
      message: message,
      type: ErrorType.tts,
      severity: ErrorSeverity.low,
      originalError: originalError,
    );
  }

  /// Creates a parsing error
  static StoryQuestException parsingError(String message, [dynamic originalError]) {
    return StoryQuestException(
      message: message,
      type: ErrorType.parsing,
      severity: ErrorSeverity.high,
      originalError: originalError,
    );
  }

  /// Wraps a function with error handling
  static Future<T?> withErrorHandling<T>(
    Future<T> Function() operation, {
    BuildContext? context,
    String? userMessage,
    T? fallbackValue,
  }) async {
    try {
      return await operation();
    } catch (error, stackTrace) {
      await handleError(
        context,
        error,
        userMessage: userMessage,
      );
      
      if (kDebugMode) {
        debugPrint('Error in withErrorHandling: $error');
        debugPrint('Stack trace: $stackTrace');
      }
      
      return fallbackValue;
    }
  }
}
