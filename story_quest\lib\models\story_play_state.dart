/// Story playback state management for Story Quest app
///
/// Defines all possible states during story playback and provides
/// state transition logic with error handling and offline support.
library;

import 'package:flutter/foundation.dart';
import '../models/story_model.dart';
import '../models/scene_model.dart';
import '../models/choice_model.dart';

/// Enumeration of all possible story playback states
enum StoryPlaybackState {
  /// Initial state - waiting for user to select a story
  idle,
  
  /// Loading story data from local storage or Firebase
  loading,
  
  /// Actively playing/narrating the story
  playing,
  
  /// Presenting user with story choices
  choicePrompt,
  
  /// Displaying moral reflection moment
  moralMoment,
  
  /// Story has completed successfully
  completed,
  
  /// Error occurred during playback
  error,
  
  /// Story playback is temporarily paused
  paused,
}

/// Story playback error types
enum StoryPlaybackError {
  /// Failed to load story data
  loadingFailed,
  
  /// Network connectivity issues
  networkError,
  
  /// Audio/TTS playback failed
  audioError,
  
  /// Invalid story data or corruption
  dataCorruption,
  
  /// Choice selection failed
  choiceError,
  
  /// Resume from pause failed
  resumeError,
  
  /// Unknown/unexpected error
  unknown,
}

/// Story playback state data container
class StoryPlaybackStateData {
  /// Current playback state
  final StoryPlaybackState state;
  
  /// Currently loaded story (null if not loaded)
  final Story? story;
  
  /// Current scene being played (null if not playing)
  final Scene? currentScene;
  
  /// Current scene index in the story
  final int currentSceneIndex;
  
  /// Error information (null if no error)
  final StoryPlaybackError? error;
  
  /// Error message for user display
  final String? errorMessage;
  
  /// Whether the story is being narrated via TTS
  final bool isNarrating;
  
  /// Whether offline mode is active
  final bool isOffline;
  
  /// Story progress percentage (0.0 to 1.0)
  final double progress;
  
  /// Available choices for current scene (empty if none)
  final List<Choice> availableChoices;
  
  /// Moral reflection text (null if not in moral moment)
  final String? moralReflection;
  
  /// Whether the story can be resumed
  final bool canResume;
  
  /// Timestamp of last state change
  final DateTime lastStateChange;

  const StoryPlaybackStateData({
    required this.state,
    this.story,
    this.currentScene,
    this.currentSceneIndex = 0,
    this.error,
    this.errorMessage,
    this.isNarrating = false,
    this.isOffline = false,
    this.progress = 0.0,
    this.availableChoices = const [],
    this.moralReflection,
    this.canResume = false,
    DateTime? lastStateChange,
  }) : lastStateChange = lastStateChange ?? DateTime.now();

  /// Creates a copy with updated values
  StoryPlaybackStateData copyWith({
    StoryPlaybackState? state,
    Story? story,
    Scene? currentScene,
    int? currentSceneIndex,
    StoryPlaybackError? error,
    String? errorMessage,
    bool? isNarrating,
    bool? isOffline,
    double? progress,
    List<Choice>? availableChoices,
    String? moralReflection,
    bool? canResume,
    DateTime? lastStateChange,
  }) {
    return StoryPlaybackStateData(
      state: state ?? this.state,
      story: story ?? this.story,
      currentScene: currentScene ?? this.currentScene,
      currentSceneIndex: currentSceneIndex ?? this.currentSceneIndex,
      error: error,
      errorMessage: errorMessage,
      isNarrating: isNarrating ?? this.isNarrating,
      isOffline: isOffline ?? this.isOffline,
      progress: progress ?? this.progress,
      availableChoices: availableChoices ?? this.availableChoices,
      moralReflection: moralReflection,
      canResume: canResume ?? this.canResume,
      lastStateChange: lastStateChange ?? DateTime.now(),
    );
  }

  /// Clears error state
  StoryPlaybackStateData clearError() {
    return copyWith(
      error: null,
      errorMessage: null,
    );
  }

  /// Sets error state
  StoryPlaybackStateData setError(StoryPlaybackError error, String message) {
    return copyWith(
      state: StoryPlaybackState.error,
      error: error,
      errorMessage: message,
    );
  }

  /// Checks if the state allows user interaction
  bool get allowsInteraction {
    switch (state) {
      case StoryPlaybackState.idle:
      case StoryPlaybackState.choicePrompt:
      case StoryPlaybackState.moralMoment:
      case StoryPlaybackState.completed:
      case StoryPlaybackState.error:
      case StoryPlaybackState.paused:
        return true;
      case StoryPlaybackState.loading:
      case StoryPlaybackState.playing:
        return false;
    }
  }

  /// Checks if the story is actively playing
  bool get isActivelyPlaying {
    return state == StoryPlaybackState.playing && isNarrating;
  }

  /// Checks if the state represents an error condition
  bool get hasError {
    return state == StoryPlaybackState.error && error != null;
  }

  /// Gets user-friendly state description
  String get stateDescription {
    switch (state) {
      case StoryPlaybackState.idle:
        return 'Ready to start a story';
      case StoryPlaybackState.loading:
        return 'Loading story...';
      case StoryPlaybackState.playing:
        return isNarrating ? 'Playing story' : 'Story ready';
      case StoryPlaybackState.choicePrompt:
        return 'Choose your path';
      case StoryPlaybackState.moralMoment:
        return 'Time to reflect';
      case StoryPlaybackState.completed:
        return 'Story completed!';
      case StoryPlaybackState.error:
        return errorMessage ?? 'An error occurred';
      case StoryPlaybackState.paused:
        return 'Story paused';
    }
  }

  /// Gets progress as percentage string
  String get progressText {
    return '${(progress * 100).round()}%';
  }

  @override
  String toString() {
    return 'StoryPlaybackStateData(state: $state, progress: $progressText, '
           'scene: ${currentSceneIndex + 1}/${story?.scenes.length ?? 0})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StoryPlaybackStateData &&
           other.state == state &&
           other.story == story &&
           other.currentScene == currentScene &&
           other.currentSceneIndex == currentSceneIndex &&
           other.error == error &&
           other.errorMessage == errorMessage &&
           other.isNarrating == isNarrating &&
           other.isOffline == isOffline &&
           other.progress == progress &&
           listEquals(other.availableChoices, availableChoices) &&
           other.moralReflection == moralReflection &&
           other.canResume == canResume;
  }

  @override
  int get hashCode {
    return Object.hash(
      state,
      story,
      currentScene,
      currentSceneIndex,
      error,
      errorMessage,
      isNarrating,
      isOffline,
      progress,
      availableChoices,
      moralReflection,
      canResume,
    );
  }
}

/// Initial state for story playback
const kInitialStoryPlaybackState = StoryPlaybackStateData(
  state: StoryPlaybackState.idle,
);
