/// Parental notification popup widgets for achievements and milestones
///
/// Provides customizable notification popups for parents to stay
/// informed about their child's progress and achievements.
library;

import 'package:flutter/material.dart';
import '../models/badge_model.dart';
import '../utils/accessibility_helper.dart';

/// Types of parental notifications
enum NotificationType {
  badgeEarned('Badge Earned', Icons.emoji_events, Color(0xFFFFCA28)),
  storyCompleted('Story Completed', Icons.book, Color(0xFF4CAF50)),
  milestone('Milestone Reached', Icons.star, Color(0xFF9C27B0)),
  timeLimit('Time Limit', Icons.access_time, Color(0xFFFF9800));

  const NotificationType(this.displayName, this.icon, this.color);
  final String displayName;
  final IconData icon;
  final Color color;
}

/// Parental notification popup widget
class ParentalNotificationPopup extends StatefulWidget {
  /// Type of notification
  final NotificationType type;
  
  /// Child's name
  final String childName;
  
  /// Notification title
  final String title;
  
  /// Notification message
  final String message;
  
  /// Optional badge for badge notifications
  final BadgeModel? badge;
  
  /// Optional story title for story notifications
  final String? storyTitle;
  
  /// Callback when notification is dismissed
  final VoidCallback? onDismissed;
  
  /// Whether to show action buttons
  final bool showActions;

  const ParentalNotificationPopup({
    super.key,
    required this.type,
    required this.childName,
    required this.title,
    required this.message,
    this.badge,
    this.storyTitle,
    this.onDismissed,
    this.showActions = true,
  });

  @override
  State<ParentalNotificationPopup> createState() => _ParentalNotificationPopupState();
}

class _ParentalNotificationPopupState extends State<ParentalNotificationPopup>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _bounceController;
  late AnimationController _sparkleController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _bounceAnimation;
  late Animation<double> _sparkleAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize slide animation
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // Initialize bounce animation
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticOut,
    ));

    // Initialize sparkle animation for special notifications
    _sparkleController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _sparkleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _sparkleController,
      curve: Curves.easeInOut,
    ));

    // Start animations
    _slideController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _bounceController.forward();
        if (widget.type == NotificationType.badgeEarned || 
            widget.type == NotificationType.milestone) {
          _sparkleController.repeat(reverse: true);
        }
      }
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _bounceController.dispose();
    _sparkleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black.withValues(alpha: 0.7),
      child: Center(
        child: SlideTransition(
          position: _slideAnimation,
          child: _buildNotificationContent(),
        ),
      ),
    );
  }

  /// Builds the main notification content
  Widget _buildNotificationContent() {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    
    return Container(
      margin: EdgeInsets.all(isTablet ? 40 : 20),
      constraints: BoxConstraints(
        maxWidth: isTablet ? 400 : 320,
        maxHeight: screenSize.height * 0.7,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          _buildContent(),
          if (widget.showActions) _buildActions(),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  /// Builds the notification header
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: widget.type.color,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Icon with sparkle effects
          Stack(
            alignment: Alignment.center,
            children: [
              // Sparkle effects for special notifications
              if (widget.type == NotificationType.badgeEarned || 
                  widget.type == NotificationType.milestone)
                AnimatedBuilder(
                  animation: _sparkleAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _sparkleAnimation.value * 0.7,
                      child: Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.white.withValues(alpha: 0.6),
                              blurRadius: 20,
                              spreadRadius: 5,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              
              // Main icon
              AnimatedBuilder(
                animation: _bounceAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _bounceAnimation.value,
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        widget.type.icon,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Title
          AccessibilityHelper.createAccessibleText(
            widget.title,
            style: const TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          
          // Child name
          AccessibilityHelper.createAccessibleText(
            widget.childName,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Builds the notification content
  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Main message
          AccessibilityHelper.createAccessibleText(
            widget.message,
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF2E7D32),
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 20),
          
          // Badge display for badge notifications
          if (widget.badge != null) _buildBadgeDisplay(),
          
          // Story display for story notifications
          if (widget.storyTitle != null) _buildStoryDisplay(),
        ],
      ),
    );
  }

  /// Builds badge display
  Widget _buildBadgeDisplay() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(widget.badge!.rarityColor).withValues(alpha: 0.1),
            Color(widget.badge!.rarityColor).withValues(alpha: 0.2),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Color(widget.badge!.rarityColor),
          width: 2,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            widget.badge!.displayEmoji,
            style: const TextStyle(fontSize: 32),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.badge!.name,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(widget.badge!.rarityColor),
                ),
              ),
              Text(
                widget.badge!.rarity.displayName,
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF757575),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds story display
  Widget _buildStoryDisplay() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.type.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: widget.type.color,
          width: 2,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.book,
            color: widget.type.color,
            size: 32,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.storyTitle!,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: widget.type.color,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds action buttons
  Widget _buildActions() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Row(
        children: [
          // View Details button (if applicable)
          if (widget.badge != null || widget.storyTitle != null)
            Expanded(
              child: OutlinedButton(
                onPressed: _viewDetails,
                style: OutlinedButton.styleFrom(
                  foregroundColor: widget.type.color,
                  side: BorderSide(color: widget.type.color),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text('View Details'),
              ),
            ),
          
          if (widget.badge != null || widget.storyTitle != null)
            const SizedBox(width: 12),
          
          // Dismiss button
          Expanded(
            child: ElevatedButton(
              onPressed: _dismiss,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.type.color,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Got It!'),
            ),
          ),
        ],
      ),
    );
  }

  /// Handles view details action
  void _viewDetails() {
    AccessibilityHelper.provideHapticFeedback();
    
    // TODO: Navigate to appropriate detail screen
    // For now, just dismiss
    _dismiss();
  }

  /// Handles dismiss action
  void _dismiss() {
    AccessibilityHelper.provideHapticFeedback();
    Navigator.of(context).pop();
    widget.onDismissed?.call();
  }
}

/// Shows a parental notification popup
Future<void> showParentalNotification({
  required BuildContext context,
  required NotificationType type,
  required String childName,
  required String title,
  required String message,
  BadgeModel? badge,
  String? storyTitle,
  VoidCallback? onDismissed,
  bool showActions = true,
}) {
  return showDialog<void>(
    context: context,
    barrierDismissible: false,
    builder: (context) => ParentalNotificationPopup(
      type: type,
      childName: childName,
      title: title,
      message: message,
      badge: badge,
      storyTitle: storyTitle,
      onDismissed: onDismissed,
      showActions: showActions,
    ),
  );
}

/// Convenience methods for specific notification types
class ParentalNotifications {
  /// Shows badge earned notification
  static Future<void> showBadgeEarned({
    required BuildContext context,
    required String childName,
    required BadgeModel badge,
    VoidCallback? onDismissed,
  }) {
    return showParentalNotification(
      context: context,
      type: NotificationType.badgeEarned,
      childName: childName,
      title: 'New Badge Earned!',
      message: '$childName just earned the "${badge.name}" badge for showing ${badge.type.displayName.toLowerCase()}!',
      badge: badge,
      onDismissed: onDismissed,
    );
  }

  /// Shows story completed notification
  static Future<void> showStoryCompleted({
    required BuildContext context,
    required String childName,
    required String storyTitle,
    VoidCallback? onDismissed,
  }) {
    return showParentalNotification(
      context: context,
      type: NotificationType.storyCompleted,
      childName: childName,
      title: 'Story Completed!',
      message: '$childName just finished reading "$storyTitle". Great job!',
      storyTitle: storyTitle,
      onDismissed: onDismissed,
    );
  }

  /// Shows milestone notification
  static Future<void> showMilestone({
    required BuildContext context,
    required String childName,
    required String milestone,
    VoidCallback? onDismissed,
  }) {
    return showParentalNotification(
      context: context,
      type: NotificationType.milestone,
      childName: childName,
      title: 'Milestone Reached!',
      message: '$childName has reached a new milestone: $milestone',
      onDismissed: onDismissed,
    );
  }

  /// Shows time limit notification
  static Future<void> showTimeLimit({
    required BuildContext context,
    required String childName,
    required int minutesRemaining,
    VoidCallback? onDismissed,
  }) {
    return showParentalNotification(
      context: context,
      type: NotificationType.timeLimit,
      childName: childName,
      title: 'Time Limit Reminder',
      message: '$childName has $minutesRemaining minutes of reading time remaining today.',
      onDismissed: onDismissed,
    );
  }
}
