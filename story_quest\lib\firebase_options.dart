// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAjJhXGEUI74on-7xJVfzVNVijct4FNTtE',
    appId: '1:64639788428:web:7a122424613bfd39a5e2a9',
    messagingSenderId: '64639788428',
    projectId: 'interactive-tales-de6d4',
    authDomain: 'interactive-tales-de6d4.firebaseapp.com',
    storageBucket: 'interactive-tales-de6d4.firebasestorage.app',
    measurementId: 'G-F1GYXVZ2H4',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCPCgvdVD35YbqYJzbrrD8RO0XCWU2VZNw',
    appId: '1:64639788428:android:4bddf88dc314dfcba5e2a9',
    messagingSenderId: '64639788428',
    projectId: 'interactive-tales-de6d4',
    storageBucket: 'interactive-tales-de6d4.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC4mXEYQwa8GIA6aknK6UlSRKPpc1JHGCo',
    appId: '1:64639788428:ios:61199c5a611e0189a5e2a9',
    messagingSenderId: '64639788428',
    projectId: 'interactive-tales-de6d4',
    storageBucket: 'interactive-tales-de6d4.firebasestorage.app',
    iosBundleId: 'com.example.storyQuest',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyC4mXEYQwa8GIA6aknK6UlSRKPpc1JHGCo',
    appId: '1:64639788428:ios:61199c5a611e0189a5e2a9',
    messagingSenderId: '64639788428',
    projectId: 'interactive-tales-de6d4',
    storageBucket: 'interactive-tales-de6d4.firebasestorage.app',
    iosBundleId: 'com.example.storyQuest',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAjJhXGEUI74on-7xJVfzVNVijct4FNTtE',
    appId: '1:64639788428:web:3fcb33ae4cd3bb52a5e2a9',
    messagingSenderId: '64639788428',
    projectId: 'interactive-tales-de6d4',
    authDomain: 'interactive-tales-de6d4.firebaseapp.com',
    storageBucket: 'interactive-tales-de6d4.firebasestorage.app',
    measurementId: 'G-EEJL6Z51TX',
  );
}
