/// Navigation service for Story Quest app
/// 
/// Manages app navigation, routing, and screen transitions
/// with proper back navigation handling.
library;

import 'package:flutter/material.dart';
import '../features/splash/splash_screen.dart';
import '../features/ftue/ftue_screen.dart';
import '../features/profile_selection/child_profile_selection_screen.dart';
import '../features/homepage/homepage_screen.dart';
import '../features/story_library/story_library_screen.dart';
import '../features/story_introduction/story_introduction_screen.dart';
import '../features/meet_characters/meet_characters_screen.dart';
import '../features/story_play/story_play_screen.dart';
import '../models/index.dart';
import 'service_locator.dart';
import 'guest_service.dart';

/// Route names for the application
class AppRoutes {
  static const String splash = '/';
  static const String ftue = '/ftue';
  static const String profileSelection = '/profile-selection';
  static const String homepage = '/homepage';
  static const String storyLibrary = '/story-library';
  static const String storyIntroduction = '/story-introduction';
  static const String meetCharacters = '/meet-characters';
  static const String storyPlay = '/story-play';
}

/// Navigation service for managing app navigation
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Gets the current context
  static BuildContext? get currentContext => navigatorKey.currentContext;

  /// Navigates to a named route
  static Future<T?> navigateTo<T>(String routeName, {Object? arguments}) {
    return navigatorKey.currentState!.pushNamed<T>(routeName, arguments: arguments);
  }

  /// Navigates to a route and removes all previous routes
  static Future<T?> navigateAndClearStack<T>(String routeName, {Object? arguments}) {
    return navigatorKey.currentState!.pushNamedAndRemoveUntil<T>(
      routeName,
      (route) => false,
      arguments: arguments,
    );
  }

  /// Replaces the current route
  static Future<T?> navigateAndReplace<T>(String routeName, {Object? arguments}) {
    return navigatorKey.currentState!.pushReplacementNamed<T, T>(routeName, arguments: arguments);
  }

  /// Goes back to the previous screen
  static void goBack<T>([T? result]) {
    if (navigatorKey.currentState!.canPop()) {
      navigatorKey.currentState!.pop<T>(result);
    }
  }

  /// Checks if we can go back
  static bool canGoBack() {
    return navigatorKey.currentState?.canPop() ?? false;
  }

  /// Shows a dialog
  static Future<T?> showDialogRoute<T>(Widget dialog) {
    return showDialog<T>(
      context: currentContext!,
      builder: (context) => dialog,
    );
  }

  /// Shows a bottom sheet
  static Future<T?> showBottomSheetRoute<T>(Widget bottomSheet) {
    return showModalBottomSheet<T>(
      context: currentContext!,
      builder: (context) => bottomSheet,
    );
  }
}

/// Route generator for the application
class AppRouteGenerator {
  /// Generates routes based on route settings
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRoutes.splash:
        return _createRoute(
          SplashScreen(
            onContinue: () => NavigationService.navigateAndReplace(AppRoutes.ftue),
          ),
        );

      case AppRoutes.ftue:
        return _createRoute(
          FTUEScreen(
            onAuthSuccess: () async {
              // Check if user is guest
              final guestService = GuestService();
              await guestService.initialize();

              if (guestService.isGuestUser) {
                // Skip profile selection for guests
                NavigationService.navigateAndReplace(AppRoutes.homepage);
              } else {
                // Regular users go to profile selection
                NavigationService.navigateTo(AppRoutes.profileSelection);
              }
            },
            onBack: () => NavigationService.goBack(),
          ),
        );

      case AppRoutes.profileSelection:
        return _createRoute(
          ChildProfileSelectionScreen(
            onProfileSelected: (profile) {
              NavigationService.navigateAndReplace(AppRoutes.homepage);
            },
            onBack: () => NavigationService.goBack(),
          ),
        );

      case AppRoutes.homepage:
        return _createRoute(
          HomepageScreen(
            onStoryTime: () {
              NavigationService.navigateTo(AppRoutes.storyLibrary);
            },
            onMyStuff: () {
              // TODO: Navigate to my stuff
              _showComingSoonDialog('My Stuff');
            },
            onResumeStories: () {
              // TODO: Navigate to resume stories
              _showComingSoonDialog('Resume Stories');
            },
            onMyRewards: () {
              // TODO: Navigate to rewards
              _showComingSoonDialog('My Rewards');
            },
            onParentZone: () {
              // TODO: Navigate to parent zone
              _showComingSoonDialog('Parent Zone');
            },
            onTutorials: () {
              // TODO: Navigate to tutorials
              _showComingSoonDialog('Tutorials');
            },
            onThemeChanged: (theme) {
              // Theme switching is handled by the theme service
              print('Theme changed to: ${theme.label}');
            },
          ),
        );

      case AppRoutes.storyLibrary:
        return _createRoute(
          FutureBuilder<List<StoryModel>>(
            future: _loadStories(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Scaffold(
                  body: Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              final stories = snapshot.data ?? [];
              final downloadedStories = serviceLocator.storyService.getDownloadedStoryIds();

              return StoryLibraryScreen(
                stories: stories,
                downloadedStories: downloadedStories,
                onStorySelected: (story) {
                  NavigationService.navigateTo(
                    AppRoutes.storyIntroduction,
                    arguments: story,
                  );
                },
                onDownloadStory: (story) {
                  _downloadStory(story);
                },
                onBack: () => NavigationService.goBack(),
              );
            },
          ),
        );

      case AppRoutes.storyIntroduction:
        final story = settings.arguments as StoryModel?;
        if (story == null) {
          return _createRoute(const Scaffold(
            body: Center(child: Text('Story not found')),
          ));
        }

        return _createRoute(
          StoryIntroductionScreen(
            story: story,
            isDownloaded: false, // Check actual status
            onMeetCharacters: () {
              NavigationService.navigateTo(
                AppRoutes.meetCharacters,
                arguments: story,
              );
            },
            onPlayDownload: () {
              NavigationService.navigateTo(
                AppRoutes.storyPlay,
                arguments: story,
              );
            },
            onBack: () => NavigationService.goBack(),
          ),
        );

      case AppRoutes.meetCharacters:
        final story = settings.arguments as StoryModel?;
        if (story == null) {
          return _createRoute(const Scaffold(
            body: Center(child: Text('Story not found')),
          ));
        }

        return _createRoute(
          MeetCharactersScreen(
            story: story,
            onPlayStory: () {
              NavigationService.navigateTo(
                AppRoutes.storyPlay,
                arguments: story,
              );
            },
            onBack: () => NavigationService.goBack(),
          ),
        );

      case AppRoutes.storyPlay:
        final story = settings.arguments as StoryModel?;
        if (story == null) {
          return _createRoute(const Scaffold(
            body: Center(child: Text('Story not found')),
          ));
        }

        return _createRoute(
          StoryPlayScreen(
            story: story,
            onStoryComplete: () {
              // Show completion dialog and return to library
              _showStoryCompletionDialog(story);
            },
            onBack: () => NavigationService.goBack(),
          ),
        );

      default:
        return _createRoute(
          const Scaffold(
            body: Center(
              child: Text('Route not found'),
            ),
          ),
        );
    }
  }

  /// Creates a page route with slide transition
  static PageRoute _createRoute(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.ease;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }

  /// Shows a coming soon dialog
  static void _showComingSoonDialog(String feature) {
    NavigationService.showDialogRoute(
      AlertDialog(
        title: Text('$feature Coming Soon!'),
        content: Text('The $feature feature will be available in a future update.'),
        actions: [
          TextButton(
            onPressed: () => NavigationService.goBack(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Shows add profile dialog
  static void _showAddProfileDialog() {
    NavigationService.showDialogRoute(
      AlertDialog(
        title: const Text('Add New Profile'),
        content: const Text('Profile creation will be available in a future update.'),
        actions: [
          TextButton(
            onPressed: () => NavigationService.goBack(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => NavigationService.goBack(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Gets demo stories for testing
  static List<StoryModel> _getDemoStories() {
    return [
      StoryModel(
        storyId: 'demo_story_1',
        ageGroup: '3-5',
        difficulty: 'easy',
        title: 'The Friendly Forest',
        moral: 'Friendship and kindness matter',
        coverImage: 'forest_cover.jpg',
        estimatedTime: '5 minutes',
        setup: const StorySetupModel(
          setting: 'A magical forest',
          tone: 'cheerful',
          context: 'adventure',
          briefIntro: 'Join Mia on a magical adventure in the friendly forest!',
          backgroundMusic: 'forest_music.mp3',
        ),
        narratorProfile: NarratorProfileModel(
          name: 'Story Narrator',
          voice: const VoiceModel(
            name: 'en-US-Standard-A',
            pitch: 1.0,
            rate: 1.0,
            volume: 1.0,
          ),
          defaultVoice: true,
        ),
        characters: [
          CharacterModel(
            name: 'Mia',
            description: 'A curious and kind little girl',
            role: 'Protagonist',
            voice: const VoiceModel(
              name: 'en-US-Wavenet-F',
              pitch: 1.2,
              rate: 1.0,
              volume: 1.0,
            ),
          ),
        ],
        scenes: [
          const SceneModel(
            id: 'scene_1',
            text: 'Once upon a time, in a magical forest, lived a little girl named Mia.',
            speaker: 'narrator',
            emotion: 'happy',
            image: 'forest_scene.jpg',
            pauseDuration: 2000,
            next: 'scene_2',
            progressWeight: 1,
          ),
        ],
        vocabulary: [],
        postStory: PostStoryModel(
          discussion: DiscussionModel(
            text: 'What did you learn about friendship?',
            vocabularyDiscussion: [],
            emotion: 'curious',
          ),
          replayPrompt: const ReplayPromptModel(
            text: 'Would you like to read this story again?',
            emotion: 'excited',
          ),
          parentalDiscussionPrompts: ['Talk about friendship'],
          feedbackSection: const FeedbackSectionModel(
            rating: '5-star',
            comments: 'optional',
          ),
        ),
      ),
    ];
  }

  /// Loads stories from the story service
  static Future<List<StoryModel>> _loadStories() async {
    try {
      await serviceLocator.storyService.initialize();
      return serviceLocator.storyService.getAllStoryModels();
    } catch (e) {
      print('Error loading stories: $e');
      return _getDemoStories(); // Fallback to demo stories
    }
  }

  /// Downloads a story
  static void _downloadStory(StoryModel story) {
    NavigationService.showDialogRoute(
      AlertDialog(
        title: const Text('Download Story'),
        content: Text('Download "${story.title}" for offline reading?'),
        actions: [
          TextButton(
            onPressed: () => NavigationService.goBack(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              NavigationService.goBack(); // Close dialog

              // Show download progress
              _showDownloadProgress(story);
            },
            child: const Text('Download'),
          ),
        ],
      ),
    );
  }

  /// Shows download progress dialog
  static void _showDownloadProgress(StoryModel story) {
    double progress = 0.0;
    bool isCancelled = false;

    NavigationService.showDialogRoute(
      StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: const Text('Downloading Story'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Downloading "${story.title}"...'),
                const SizedBox(height: 16),
                LinearProgressIndicator(value: progress),
                const SizedBox(height: 8),
                Text('${(progress * 100).toInt()}%'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  isCancelled = true;
                  NavigationService.goBack();
                },
                child: const Text('Cancel'),
              ),
            ],
          );
        },
      ),
    );

    // Start download
    serviceLocator.storyService.downloadStory(
      story.storyId,
      onProgress: (newProgress) {
        if (!isCancelled) {
          progress = newProgress;
          // Note: In a real app, you'd use a proper state management solution
          // to update the dialog progress
        }
      },
    ).then((success) {
      if (!isCancelled) {
        NavigationService.goBack(); // Close progress dialog

        if (success) {
          _showDownloadSuccessSnackBar(story);
        } else {
          _showDownloadErrorSnackBar(story);
        }
      }
    }).catchError((error) {
      if (!isCancelled) {
        NavigationService.goBack(); // Close progress dialog
        _showDownloadErrorSnackBar(story, error: error.toString());
      }
    });
  }

  /// Shows download success SnackBar
  static void _showDownloadSuccessSnackBar(StoryModel story) {
    final context = NavigationService.navigatorKey.currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '"${story.title}" downloaded successfully! Now available offline.',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          backgroundColor: const Color(0xFF4CAF50), // Green
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: 'Play Now',
            textColor: Colors.white,
            onPressed: () {
              NavigationService.navigateTo(
                AppRoutes.storyIntroduction,
                arguments: story,
              );
            },
          ),
        ),
      );
    }
  }

  /// Shows download error SnackBar with retry option
  static void _showDownloadErrorSnackBar(StoryModel story, {String? error}) {
    final context = NavigationService.navigatorKey.currentContext;
    if (context != null) {
      String errorMessage = 'Failed to download "${story.title}".';

      // Determine specific error message based on error type
      if (error != null) {
        if (error.contains('network') || error.contains('connection')) {
          errorMessage += ' Please check your internet connection and try again.';
        } else if (error.contains('storage') || error.contains('space')) {
          errorMessage += ' Insufficient storage space. Please free up space and try again.';
        } else if (error.contains('timeout')) {
          errorMessage += ' Download timed out. Please try again.';
        } else if (error.contains('corrupted') || error.contains('invalid')) {
          errorMessage += ' Story file is corrupted. Please try again.';
        } else {
          errorMessage += ' Please try again later.';
        }
      } else {
        errorMessage += ' Please check your internet connection and try again.';
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error_outline, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  errorMessage,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          backgroundColor: const Color(0xFFF44336), // Red
          duration: const Duration(seconds: 6),
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: () {
              _downloadStory(story); // Retry download
            },
          ),
        ),
      );
    }
  }

  /// Shows story completion dialog
  static void _showStoryCompletionDialog(StoryModel story) {
    NavigationService.showDialogRoute(
      AlertDialog(
        title: const Text('Story Complete!'),
        content: Text('You finished "${story.title}"! Great job reading!'),
        actions: [
          TextButton(
            onPressed: () {
              NavigationService.goBack(); // Close dialog
              NavigationService.navigateAndClearStack(AppRoutes.storyLibrary);
            },
            child: const Text('Read Another Story'),
          ),
          TextButton(
            onPressed: () {
              NavigationService.goBack(); // Close dialog
              NavigationService.navigateAndClearStack(AppRoutes.homepage);
            },
            child: const Text('Go Home'),
          ),
        ],
      ),
    );
  }
}

/// Custom page route with fade transition
class FadePageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;

  FadePageRoute({required this.child})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 500),
        );
}

/// Custom page route with scale transition
class ScalePageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;

  ScalePageRoute({required this.child})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return ScaleTransition(
              scale: Tween<double>(
                begin: 0.0,
                end: 1.0,
              ).animate(
                CurvedAnimation(
                  parent: animation,
                  curve: Curves.elasticOut,
                ),
              ),
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 800),
        );
}
