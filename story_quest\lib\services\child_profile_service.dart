/// Child profile management service
///
/// Handles CRUD operations for child profiles, including creation, retrieval,
/// updates, and deletion. Integrates with Firebase Firestore and local storage.
library;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/child_profile_model.dart';
import 'auth_service.dart';

/// Result types for profile operations
enum ProfileResult {
  success,
  notFound,
  unauthorized,
  networkError,
  validationError,
  unknown,
}

/// Child profile management service
class ChildProfileService {
  static final ChildProfileService _instance = ChildProfileService._internal();
  factory ChildProfileService() => _instance;
  ChildProfileService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthService _authService = AuthService();
  
  // Local storage keys
  static const String _activeProfileKey = 'active_child_profile_id';
  static const String _profilesCacheKey = 'cached_child_profiles';

  /// Gets all profiles for the current user
  Future<List<ChildProfileModel>> getProfiles() async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        return [];
      }

      final querySnapshot = await _firestore
          .collection('child_profiles')
          .where('parent_id', isEqualTo: currentUser.uid)
          .orderBy('created_at', descending: false)
          .get();

      final profiles = querySnapshot.docs
          .map((doc) => ChildProfileModel.fromJson({
                'id': doc.id,
                ...doc.data(),
              }))
          .toList();

      // Cache profiles locally
      await _cacheProfiles(profiles);

      return profiles;
    } catch (e) {
      // Return cached profiles if available
      return await _getCachedProfiles();
    }
  }

  /// Gets a specific profile by ID
  Future<ChildProfileModel?> getProfile(String profileId) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        return null;
      }

      final doc = await _firestore
          .collection('child_profiles')
          .doc(profileId)
          .get();

      if (!doc.exists) {
        return null;
      }

      final data = doc.data()!;
      
      // Verify ownership
      if (data['parent_id'] != currentUser.uid) {
        return null;
      }

      return ChildProfileModel.fromJson({
        'id': doc.id,
        ...data,
      });
    } catch (e) {
      return null;
    }
  }

  /// Creates a new child profile
  Future<ProfileResult> createProfile(ChildProfileModel profile) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        return ProfileResult.unauthorized;
      }

      // Validate profile
      if (!profile.isValid) {
        return ProfileResult.validationError;
      }

      // Check profile limit (max 5 profiles per parent)
      final existingProfiles = await getProfiles();
      if (existingProfiles.length >= 5) {
        return ProfileResult.validationError;
      }

      // Create profile with parent ID
      final profileData = profile.copyWith(
        parentId: currentUser.uid,
        createdAt: DateTime.now(),
        lastActiveAt: DateTime.now(),
      );

      await _firestore
          .collection('child_profiles')
          .add(profileData.toJson());

      return ProfileResult.success;
    } catch (e) {
      return ProfileResult.networkError;
    }
  }

  /// Updates an existing profile
  Future<ProfileResult> updateProfile(ChildProfileModel profile) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        return ProfileResult.unauthorized;
      }

      // Validate profile
      if (!profile.isValid) {
        return ProfileResult.validationError;
      }

      // Verify ownership
      if (profile.parentId != currentUser.uid) {
        return ProfileResult.unauthorized;
      }

      await _firestore
          .collection('child_profiles')
          .doc(profile.id)
          .update(profile.toJson());

      return ProfileResult.success;
    } catch (e) {
      return ProfileResult.networkError;
    }
  }

  /// Deletes a profile
  Future<ProfileResult> deleteProfile(String profileId) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        return ProfileResult.unauthorized;
      }

      // Verify ownership before deletion
      final profile = await getProfile(profileId);
      if (profile == null) {
        return ProfileResult.notFound;
      }

      if (profile.parentId != currentUser.uid) {
        return ProfileResult.unauthorized;
      }

      await _firestore
          .collection('child_profiles')
          .doc(profileId)
          .delete();

      // Clear from active profile if it was active
      final activeProfileId = await getActiveProfileId();
      if (activeProfileId == profileId) {
        await clearActiveProfile();
      }

      return ProfileResult.success;
    } catch (e) {
      return ProfileResult.networkError;
    }
  }

  /// Sets the active profile
  Future<void> setActiveProfile(String profileId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_activeProfileKey, profileId);
    } catch (e) {
      // Handle error silently
    }
  }

  /// Gets the active profile ID
  Future<String?> getActiveProfileId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_activeProfileKey);
    } catch (e) {
      return null;
    }
  }

  /// Gets the active profile
  Future<ChildProfileModel?> getActiveProfile() async {
    final activeProfileId = await getActiveProfileId();
    if (activeProfileId == null) {
      return null;
    }
    return await getProfile(activeProfileId);
  }

  /// Clears the active profile
  Future<void> clearActiveProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_activeProfileKey);
    } catch (e) {
      // Handle error silently
    }
  }

  /// Updates profile progress
  Future<ProfileResult> updateProgress({
    required String profileId,
    required String storyId,
    required Map<String, dynamic> progressData,
  }) async {
    try {
      final profile = await getProfile(profileId);
      if (profile == null) {
        return ProfileResult.notFound;
      }

      final updatedProgress = Map<String, dynamic>.from(profile.currentProgress);
      updatedProgress[storyId] = progressData;

      final updatedProfile = profile.copyWith(
        currentProgress: updatedProgress,
        lastActiveAt: DateTime.now(),
      );

      return await updateProfile(updatedProfile);
    } catch (e) {
      return ProfileResult.networkError;
    }
  }

  /// Marks story as completed
  Future<ProfileResult> completeStory({
    required String profileId,
    required String storyId,
  }) async {
    try {
      final profile = await getProfile(profileId);
      if (profile == null) {
        return ProfileResult.notFound;
      }

      final completedStories = List<String>.from(profile.completedStories);
      if (!completedStories.contains(storyId)) {
        completedStories.add(storyId);
      }

      final updatedProfile = profile.copyWith(
        completedStories: completedStories,
        storiesCompleted: completedStories.length,
        lastActiveAt: DateTime.now(),
      );

      return await updateProfile(updatedProfile);
    } catch (e) {
      return ProfileResult.networkError;
    }
  }

  /// Adds badge to profile
  Future<ProfileResult> addBadge({
    required String profileId,
    required String badgeId,
    required String badgeType,
  }) async {
    try {
      final profile = await getProfile(profileId);
      if (profile == null) {
        return ProfileResult.notFound;
      }

      final earnedBadges = List<String>.from(profile.earnedBadges);
      if (!earnedBadges.contains(badgeId)) {
        earnedBadges.add(badgeId);
      }

      final badgesByType = Map<String, int>.from(profile.badgesByType);
      badgesByType[badgeType] = (badgesByType[badgeType] ?? 0) + 1;

      final updatedProfile = profile.copyWith(
        earnedBadges: earnedBadges,
        totalBadges: earnedBadges.length,
        badgesByType: badgesByType,
        lastActiveAt: DateTime.now(),
      );

      return await updateProfile(updatedProfile);
    } catch (e) {
      return ProfileResult.networkError;
    }
  }

  /// Caches profiles locally
  Future<void> _cacheProfiles(List<ChildProfileModel> profiles) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profilesJson = profiles.map((p) => p.toJson()).toList();
      await prefs.setString(_profilesCacheKey, profilesJson.toString());
    } catch (e) {
      // Handle error silently
    }
  }

  /// Gets cached profiles
  Future<List<ChildProfileModel>> _getCachedProfiles() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_profilesCacheKey);
      if (cachedData == null) {
        return [];
      }

      // This is a simplified implementation
      // In a real app, you'd properly parse the JSON
      return [];
    } catch (e) {
      return [];
    }
  }

  /// Gets user-friendly error message
  static String getErrorMessage(ProfileResult result) {
    switch (result) {
      case ProfileResult.success:
        return 'Success';
      case ProfileResult.notFound:
        return 'Profile not found.';
      case ProfileResult.unauthorized:
        return 'You are not authorized to perform this action.';
      case ProfileResult.networkError:
        return 'Network error. Please check your connection and try again.';
      case ProfileResult.validationError:
        return 'Please check that all information is correct.';
      case ProfileResult.unknown:
        return 'An unexpected error occurred. Please try again.';
    }
  }
}
