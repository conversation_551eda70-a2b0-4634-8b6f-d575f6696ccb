import 'choice_model.dart';
import 'reflection_model.dart';

/// Scene model representing individual story scenes
/// 
/// Contains all scene data including text, speaker, emotion, assets,
/// choices, and progression information.

class SceneModel {
  /// Unique identifier for this scene
  final String id;
  
  /// The text content of the scene
  final String text;
  
  /// Who is speaking (e.g., "narrator", character name)
  final String speaker;
  
  /// Emotional tone of the scene
  final String emotion;
  
  /// Image filename for this scene
  final String image;
  
  /// Pause duration in milliseconds before continuing
  final int pauseDuration;
  
  /// Next scene ID (null for choice scenes)
  final String? next;
  
  /// List of choices for interactive scenes (empty for linear scenes)
  final List<ChoiceModel> choices;
  
  /// Weight for progress calculation
  final int progressWeight;
  
  /// List of interactive elements (currently unused but reserved)
  final List<dynamic> interactiveElements;
  
  /// Sound effects filename for emotional enhancement
  final String? emotionalSoundEffects;
  
  /// Outcome type for this scene ("good", "bad", or null)
  final String? outcome;
  
  /// Rewards earned from this scene
  final Map<String, int>? rewards;
  
  /// Reflection content for this scene
  final ReflectionModel? reflection;

  const SceneModel({
    required this.id,
    required this.text,
    required this.speaker,
    required this.emotion,
    required this.image,
    required this.pauseDuration,
    this.next,
    this.choices = const [],
    required this.progressWeight,
    this.interactiveElements = const [],
    this.emotionalSoundEffects,
    this.outcome,
    this.rewards,
    this.reflection,
  });

  /// Creates a SceneModel from JSON data
  factory SceneModel.fromJson(Map<String, dynamic> json) {
    return SceneModel(
      id: json['id'] as String,
      text: json['text'] as String,
      speaker: json['speaker'] as String,
      emotion: json['emotion'] as String,
      image: json['image'] as String,
      pauseDuration: json['pause_duration'] as int,
      next: json['next'] as String?,
      choices: (json['choices'] as List<dynamic>?)
          ?.map((choice) => ChoiceModel.fromJson(choice as Map<String, dynamic>))
          .toList() ?? [],
      progressWeight: json['progress_weight'] as int,
      interactiveElements: json['interactive_elements'] as List<dynamic>? ?? [],
      emotionalSoundEffects: json['emotional_sound_effects'] as String?,
      outcome: json['outcome'] as String?,
      rewards: (json['rewards'] as Map<String, dynamic>?)?.cast<String, int>(),
      reflection: json['reflection'] != null
          ? ReflectionModel.fromJson(json['reflection'] as Map<String, dynamic>)
          : null,
    );
  }

  /// Converts SceneModel to JSON
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'id': id,
      'text': text,
      'speaker': speaker,
      'emotion': emotion,
      'image': image,
      'pause_duration': pauseDuration,
      'progress_weight': progressWeight,
      'interactive_elements': interactiveElements,
    };

    if (next != null) json['next'] = next;
    if (choices.isNotEmpty) {
      json['choices'] = choices.map((choice) => choice.toJson()).toList();
    }
    if (emotionalSoundEffects != null) {
      json['emotional_sound_effects'] = emotionalSoundEffects;
    }
    if (outcome != null) json['outcome'] = outcome;
    if (rewards != null) json['rewards'] = rewards;
    if (reflection != null) json['reflection'] = reflection!.toJson();

    return json;
  }

  /// Checks if this scene has choices (is interactive)
  bool get hasChoices => choices.isNotEmpty;

  /// Checks if this scene is a terminal scene (no next scene or choices)
  bool get isTerminal => next == null && choices.isEmpty;

  /// Validates that the scene has required fields and logical consistency
  bool isValid() {
    // Basic field validation
    if (id.isEmpty || text.isEmpty || speaker.isEmpty || 
        emotion.isEmpty || image.isEmpty || progressWeight < 0) {
      return false;
    }

    // Logic validation: scene should have either next OR choices, not both
    if (next != null && choices.isNotEmpty) {
      return false;
    }

    // If scene has choices, validate each choice
    if (choices.isNotEmpty) {
      return choices.every((choice) => choice.isValid());
    }

    // If scene has reflection, validate it
    if (reflection != null && !reflection!.isValid()) {
      return false;
    }

    return true;
  }

  @override
  String toString() {
    return 'SceneModel(id: $id, speaker: $speaker, hasChoices: $hasChoices)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SceneModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
