{"roots": ["story_quest"], "packages": [{"name": "story_quest", "version": "1.0.0+1", "dependencies": ["archive", "carousel_slider", "cloud_firestore", "crypto", "cupertino_icons", "firebase_auth", "firebase_core", "firebase_storage", "flutter", "flutter_tts", "get_it", "google_sign_in", "http", "js", "path", "path_provider", "provider", "shared_preferences", "sqflite"], "devDependencies": ["build_runner", "flutter_lints", "flutter_test", "<PERSON><PERSON>"]}, {"name": "build_runner", "version": "2.5.3", "dependencies": ["analyzer", "args", "async", "build", "build_config", "build_daemon", "build_resolvers", "build_runner_core", "code_builder", "collection", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http", "http_multi_server", "io", "js", "logging", "meta", "mime", "package_config", "path", "pool", "pub_semver", "pubspec_parse", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "timing", "watcher", "web", "web_socket_channel", "yaml"]}, {"name": "<PERSON><PERSON>", "version": "5.4.6", "dependencies": ["analyzer", "build", "code_builder", "collection", "dart_style", "matcher", "meta", "path", "source_gen", "test_api"]}, {"name": "flutter_lints", "version": "5.0.0", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "carousel_slider", "version": "4.2.1", "dependencies": ["flutter"]}, {"name": "js", "version": "0.7.2", "dependencies": []}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "provider", "version": "6.1.5", "dependencies": ["collection", "flutter", "nested"]}, {"name": "flutter_tts", "version": "3.8.5", "dependencies": ["flutter", "flutter_web_plugins"]}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "get_it", "version": "7.7.0", "dependencies": ["async", "collection", "meta"]}, {"name": "archive", "version": "3.6.1", "dependencies": ["crypto", "path"]}, {"name": "google_sign_in", "version": "6.3.0", "dependencies": ["flutter", "google_sign_in_android", "google_sign_in_ios", "google_sign_in_platform_interface", "google_sign_in_web"]}, {"name": "cloud_firestore", "version": "5.6.9", "dependencies": ["cloud_firestore_platform_interface", "cloud_firestore_web", "collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "firebase_auth", "version": "5.6.0", "dependencies": ["firebase_auth_platform_interface", "firebase_auth_web", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "firebase_core", "version": "3.14.0", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "firebase_storage", "version": "12.4.7", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_storage_platform_interface", "firebase_storage_web", "flutter"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "web_socket_channel", "version": "3.0.3", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "watcher", "version": "1.1.2", "dependencies": ["async", "path"]}, {"name": "timing", "version": "1.0.2", "dependencies": ["json_annotation"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "shelf_web_socket", "version": "3.0.0", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "dart_style", "version": "3.1.0", "dependencies": ["analyzer", "args", "collection", "package_config", "path", "pub_semver", "source_span", "yaml"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "code_builder", "version": "4.10.1", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "build_runner_core", "version": "9.1.1", "dependencies": ["analyzer", "async", "build", "build_config", "build_resolvers", "build_runner", "built_collection", "built_value", "collection", "convert", "crypto", "glob", "graphs", "json_annotation", "logging", "meta", "package_config", "path", "pool", "timing", "watcher", "yaml"]}, {"name": "build_resolvers", "version": "2.5.3", "dependencies": ["analyzer", "async", "build", "build_runner_core", "collection", "convert", "crypto", "graphs", "logging", "package_config", "path", "pool", "pub_semver", "stream_transform"]}, {"name": "build_daemon", "version": "4.0.4", "dependencies": ["built_collection", "built_value", "crypto", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "build_config", "version": "1.1.2", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse", "yaml"]}, {"name": "build", "version": "2.5.3", "dependencies": ["analyzer", "async", "build_runner_core", "built_collection", "built_value", "convert", "crypto", "glob", "graphs", "logging", "meta", "package_config", "path", "pool"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "analyzer", "version": "7.4.5", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "source_gen", "version": "2.0.0", "dependencies": ["analyzer", "async", "build", "dart_style", "glob", "path", "pub_semver", "source_span", "yaml"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.10", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "sqflite_common", "version": "2.5.5", "dependencies": ["meta", "path", "synchronized"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_android", "version": "2.4.1", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "google_sign_in_web", "version": "0.12.4+4", "dependencies": ["flutter", "flutter_web_plugins", "google_identity_services_web", "google_sign_in_platform_interface", "http", "web"]}, {"name": "google_sign_in_platform_interface", "version": "2.5.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "google_sign_in_ios", "version": "5.9.0", "dependencies": ["flutter", "google_sign_in_platform_interface"]}, {"name": "google_sign_in_android", "version": "6.2.1", "dependencies": ["flutter", "google_sign_in_platform_interface"]}, {"name": "firebase_core_platform_interface", "version": "5.4.0", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "cloud_firestore_web", "version": "4.4.9", "dependencies": ["_flutterfire_internals", "cloud_firestore_platform_interface", "collection", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins"]}, {"name": "cloud_firestore_platform_interface", "version": "6.6.9", "dependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_auth_web", "version": "5.15.0", "dependencies": ["firebase_auth_platform_interface", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins", "http_parser", "meta", "web"]}, {"name": "firebase_auth_platform_interface", "version": "7.7.0", "dependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_web", "version": "2.23.0", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "firebase_storage_web", "version": "3.10.14", "dependencies": ["_flutterfire_internals", "async", "firebase_core", "firebase_core_web", "firebase_storage_platform_interface", "flutter", "flutter_web_plugins", "http", "meta", "web"]}, {"name": "firebase_storage_platform_interface", "version": "5.2.7", "dependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "web_socket", "version": "1.0.1", "dependencies": ["web"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "built_value", "version": "8.10.1", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "_fe_analyzer_shared", "version": "82.0.0", "dependencies": ["meta"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "synchronized", "version": "3.3.1", "dependencies": []}, {"name": "google_identity_services_web", "version": "0.3.3+1", "dependencies": ["meta", "web"]}, {"name": "_flutterfire_internals", "version": "1.3.56", "dependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}], "configVersion": 1}