/// Widget tests for story-related screens
/// 
/// Tests individual story screen widgets including story library,
/// story introduction, meet characters, and story play screens.
library;

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:story_quest/features/story_library/story_library_screen.dart';
import 'package:story_quest/features/story_introduction/story_introduction_screen.dart';
import 'package:story_quest/features/meet_characters/meet_characters_screen.dart';
import 'package:story_quest/features/story_play/story_play_screen.dart';
import 'package:story_quest/models/index.dart';

void main() {
  group('Story Library Screen Tests', () {
    testWidgets('displays story grid correctly', (tester) async {
      final testStories = _createTestStories();
      
      await tester.pumpWidget(
        MaterialApp(
          home: StoryLibraryScreen(
            stories: testStories,
            downloadedStories: const {'story_1'},
          ),
        ),
      );

      // Verify header
      expect(find.text('Story Library'), findsOneWidget);
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);

      // Verify genre filters
      expect(find.text('All Stories'), findsOneWidget);
      expect(find.text('Adventure'), findsOneWidget);
      expect(find.text('Fantasy'), findsOneWidget);

      // Verify story cards
      expect(find.text('Test Story 1'), findsOneWidget);
      expect(find.text('Test Story 2'), findsOneWidget);

      // Verify offline indicator
      expect(find.byIcon(Icons.check), findsOneWidget);
    });

    testWidgets('filters stories by genre', (tester) async {
      final testStories = _createTestStories();
      
      await tester.pumpWidget(
        MaterialApp(
          home: StoryLibraryScreen(stories: testStories),
        ),
      );

      // Tap Adventure filter
      await tester.tap(find.text('Adventure'));
      await tester.pumpAndSettle();

      // Verify filter is selected (visual indication)
      final adventureFilter = tester.widget<Container>(
        find.ancestor(
          of: find.text('Adventure'),
          matching: find.byType(Container),
        ).first,
      );
      
      // The selected filter should have different styling
      expect(adventureFilter.decoration, isA<BoxDecoration>());
    });

    testWidgets('handles empty story list', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: StoryLibraryScreen(stories: []),
        ),
      );

      // Should show empty state
      expect(find.text('No stories found'), findsOneWidget);
      expect(find.byIcon(Icons.search_off), findsOneWidget);
    });

    testWidgets('search functionality works', (tester) async {
      final testStories = _createTestStories();
      
      await tester.pumpWidget(
        MaterialApp(
          home: StoryLibraryScreen(stories: testStories),
        ),
      );

      // Find and use search field
      final searchField = find.byType(TextField);
      if (searchField.evaluate().isNotEmpty) {
        await tester.enterText(searchField, 'Story 1');
        await tester.pumpAndSettle();

        // Should show only matching story
        expect(find.text('Test Story 1'), findsOneWidget);
        expect(find.text('Test Story 2'), findsNothing);
      }
    });
  });

  group('Story Introduction Screen Tests', () {
    testWidgets('displays story information correctly', (tester) async {
      final testStory = _createTestStories().first;
      
      await tester.pumpWidget(
        MaterialApp(
          home: StoryIntroductionScreen(
            story: testStory,
            isDownloaded: true,
          ),
        ),
      );

      // Verify story information
      expect(find.text('Story Preview'), findsOneWidget);
      expect(find.text('Test Story 1'), findsOneWidget);
      expect(find.text('Test brief intro'), findsOneWidget);
      expect(find.text('Moral: Test moral'), findsOneWidget);

      // Verify action buttons
      expect(find.text('Meet Characters'), findsOneWidget);
      expect(find.text('Play Story'), findsOneWidget);

      // Verify metadata
      expect(find.text('3-5'), findsOneWidget);
      expect(find.text('5 minutes'), findsOneWidget);
      expect(find.text('easy'), findsOneWidget);
    });

    testWidgets('shows correct button text for downloaded story', (tester) async {
      final testStory = _createTestStories().first;
      
      await tester.pumpWidget(
        MaterialApp(
          home: StoryIntroductionScreen(
            story: testStory,
            isDownloaded: true,
          ),
        ),
      );

      expect(find.text('Play Story'), findsOneWidget);
    });

    testWidgets('shows correct button text for non-downloaded story', (tester) async {
      final testStory = _createTestStories().first;
      
      await tester.pumpWidget(
        MaterialApp(
          home: StoryIntroductionScreen(
            story: testStory,
            isDownloaded: false,
          ),
        ),
      );

      expect(find.text('Download & Play'), findsOneWidget);
    });

    testWidgets('handles button callbacks', (tester) async {
      final testStory = _createTestStories().first;
      bool meetCharactersCalled = false;
      bool playDownloadCalled = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: StoryIntroductionScreen(
            story: testStory,
            onMeetCharacters: () => meetCharactersCalled = true,
            onPlayDownload: () => playDownloadCalled = true,
          ),
        ),
      );

      // Test Meet Characters button
      await tester.tap(find.text('Meet Characters'));
      expect(meetCharactersCalled, isTrue);

      // Test Play/Download button
      await tester.tap(find.text('Download & Play'));
      expect(playDownloadCalled, isTrue);
    });
  });

  group('Meet Characters Screen Tests', () {
    testWidgets('displays characters correctly', (tester) async {
      final testStory = _createTestStories().first;
      
      await tester.pumpWidget(
        MaterialApp(
          home: MeetCharactersScreen(story: testStory),
        ),
      );

      // Verify header
      expect(find.text('Meet the Characters'), findsOneWidget);

      // Verify character information
      expect(find.text('Test Character'), findsOneWidget);
      expect(find.text('Test character description'), findsOneWidget);

      // Verify action buttons
      expect(find.text('Play Story'), findsOneWidget);
      expect(find.text('Play Audio'), findsOneWidget);
    });

    testWidgets('handles no characters gracefully', (tester) async {
      final storyWithoutCharacters = _createTestStories().first.copyWith(
        characters: [],
      );
      
      await tester.pumpWidget(
        MaterialApp(
          home: MeetCharactersScreen(story: storyWithoutCharacters),
        ),
      );

      // Should show no characters state
      expect(find.text('No characters to meet'), findsOneWidget);
      expect(find.byIcon(Icons.people_outline), findsOneWidget);
    });

    testWidgets('character carousel navigation works', (tester) async {
      final testStory = _createStoryWithMultipleCharacters();
      
      await tester.pumpWidget(
        MaterialApp(
          home: MeetCharactersScreen(story: testStory),
        ),
      );

      // Should show navigation controls for multiple characters
      expect(find.byIcon(Icons.arrow_back_ios), findsOneWidget);
      expect(find.byIcon(Icons.arrow_forward_ios), findsOneWidget);

      // Test next button
      await tester.tap(find.byIcon(Icons.arrow_forward_ios));
      await tester.pumpAndSettle();

      // Should navigate to next character
      // (This would require more complex testing to verify the actual character change)
    });
  });

  group('Story Play Screen Tests', () {
    testWidgets('displays playback controls correctly', (tester) async {
      final testStory = _createTestStories().first;
      
      await tester.pumpWidget(
        MaterialApp(
          home: StoryPlayScreen(story: testStory),
        ),
      );

      // Verify control bar elements
      expect(find.byIcon(Icons.play_arrow), findsOneWidget);
      expect(find.byIcon(Icons.replay_10), findsOneWidget);
      expect(find.byIcon(Icons.settings), findsOneWidget);

      // Verify progress bar
      expect(find.byType(LinearProgressIndicator), findsOneWidget);

      // Verify back button
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);
    });

    testWidgets('settings dialog works', (tester) async {
      final testStory = _createTestStories().first;
      
      await tester.pumpWidget(
        MaterialApp(
          home: StoryPlayScreen(story: testStory),
        ),
      );

      // Open settings
      await tester.tap(find.byIcon(Icons.settings));
      await tester.pumpAndSettle();

      // Verify settings dialog
      expect(find.text('Story Settings'), findsOneWidget);
      expect(find.text('Show Subtitles'), findsOneWidget);

      // Test subtitle toggle
      await tester.tap(find.byType(Switch));
      await tester.pumpAndSettle();

      // Close settings
      await tester.tap(find.text('Close'));
      await tester.pumpAndSettle();

      // Dialog should be closed
      expect(find.text('Story Settings'), findsNothing);
    });

    testWidgets('back navigation shows confirmation', (tester) async {
      final testStory = _createTestStories().first;
      
      await tester.pumpWidget(
        MaterialApp(
          home: StoryPlayScreen(story: testStory),
        ),
      );

      // Tap back button
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Should show exit confirmation
      expect(find.text('Exit Story?'), findsOneWidget);
      expect(find.text('Continue Reading'), findsOneWidget);
      expect(find.text('Exit'), findsOneWidget);
    });
  });
}

/// Creates test stories for widget testing
List<StoryModel> _createTestStories() {
  return [
    StoryModel(
      storyId: 'story_1',
      ageGroup: '3-5',
      difficulty: 'easy',
      title: 'Test Story 1',
      moral: 'Test moral',
      coverImage: 'test_cover.jpg',
      estimatedTime: '5 minutes',
      setup: const StorySetupModel(
        setting: 'Test setting',
        tone: 'happy',
        context: 'adventure',
        briefIntro: 'Test brief intro',
        backgroundMusic: 'test_music.mp3',
      ),
      narratorProfile: NarratorProfileModel(
        name: 'Test Narrator',
        voice: const VoiceModel(
          name: 'test-voice',
          pitch: 1.0,
          rate: 1.0,
          volume: 1.0,
        ),
        defaultVoice: true,
      ),
      characters: [
        CharacterModel(
          name: 'Test Character',
          description: 'Test character description',
          role: 'Protagonist',
          voice: const VoiceModel(
            name: 'test-character-voice',
            pitch: 1.2,
            rate: 1.0,
            volume: 1.0,
          ),
        ),
      ],
      scenes: [
        const SceneModel(
          id: 'test_scene_1',
          text: 'Test scene text',
          speaker: 'narrator',
          emotion: 'happy',
          image: 'test_scene.jpg',
          pauseDuration: 1000,
          progressWeight: 1,
        ),
      ],
      vocabulary: [],
      postStory: PostStoryModel(
        discussion: DiscussionModel(
          text: 'Test discussion',
          vocabularyDiscussion: [],
          emotion: 'curious',
        ),
        replayPrompt: const ReplayPromptModel(
          text: 'Test replay prompt',
          emotion: 'excited',
        ),
        parentalDiscussionPrompts: ['Test prompt'],
        feedbackSection: const FeedbackSectionModel(
          rating: '5-star',
          comments: 'optional',
        ),
      ),
    ),
    StoryModel(
      storyId: 'story_2',
      ageGroup: '6-8',
      difficulty: 'medium',
      title: 'Test Story 2',
      moral: 'Test moral 2',
      coverImage: 'test_cover_2.jpg',
      estimatedTime: '7 minutes',
      setup: const StorySetupModel(
        setting: 'Test setting 2',
        tone: 'exciting',
        context: 'fantasy',
        briefIntro: 'Test brief intro 2',
        backgroundMusic: 'test_music_2.mp3',
      ),
      narratorProfile: NarratorProfileModel(
        name: 'Test Narrator 2',
        voice: const VoiceModel(
          name: 'test-voice-2',
          pitch: 1.0,
          rate: 1.0,
          volume: 1.0,
        ),
        defaultVoice: true,
      ),
      characters: [],
      scenes: [],
      vocabulary: [],
      postStory: PostStoryModel(
        discussion: DiscussionModel(
          text: 'Test discussion 2',
          vocabularyDiscussion: [],
          emotion: 'curious',
        ),
        replayPrompt: const ReplayPromptModel(
          text: 'Test replay prompt 2',
          emotion: 'excited',
        ),
        parentalDiscussionPrompts: ['Test prompt 2'],
        feedbackSection: const FeedbackSectionModel(
          rating: '5-star',
          comments: 'optional',
        ),
      ),
    ),
  ];
}

/// Creates a story with multiple characters for testing
StoryModel _createStoryWithMultipleCharacters() {
  final baseStory = _createTestStories().first;
  return baseStory.copyWith(
    characters: [
      CharacterModel(
        name: 'Character 1',
        description: 'First character',
        role: 'Protagonist',
        voice: const VoiceModel(
          name: 'voice-1',
          pitch: 1.0,
          rate: 1.0,
          volume: 1.0,
        ),
      ),
      CharacterModel(
        name: 'Character 2',
        description: 'Second character',
        role: 'Friend',
        voice: const VoiceModel(
          name: 'voice-2',
          pitch: 1.2,
          rate: 1.0,
          volume: 1.0,
        ),
      ),
      CharacterModel(
        name: 'Character 3',
        description: 'Third character',
        role: 'Helper',
        voice: const VoiceModel(
          name: 'voice-3',
          pitch: 0.9,
          rate: 1.0,
          volume: 1.0,
        ),
      ),
    ],
  );
}
