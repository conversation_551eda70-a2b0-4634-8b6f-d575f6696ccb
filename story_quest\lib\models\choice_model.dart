/// Choice model for interactive story decisions
///
/// Represents a choice option that users can select during story scenes.
/// Contains the choice text, visual representation, and next scene reference.
library;

class ChoiceModel {
  /// The text displayed for this choice option
  final String option;
  
  /// Visual asset filename for this choice (e.g., icon or image)
  final String visual;
  
  /// The ID of the next scene to navigate to when this choice is selected
  final String next;

  const ChoiceModel({
    required this.option,
    required this.visual,
    required this.next,
  });

  /// Creates a ChoiceModel from JSON data
  factory ChoiceModel.fromJson(Map<String, dynamic> json) {
    return ChoiceModel(
      option: json['option'] as String,
      visual: json['visual'] as String,
      next: json['next'] as String,
    );
  }

  /// Converts ChoiceModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'option': option,
      'visual': visual,
      'next': next,
    };
  }

  /// Creates a copy of this ChoiceModel with optional parameter overrides
  ChoiceModel copyWith({
    String? option,
    String? visual,
    String? next,
  }) {
    return ChoiceModel(
      option: option ?? this.option,
      visual: visual ?? this.visual,
      next: next ?? this.next,
    );
  }

  /// Validates that all required fields are present
  bool isValid() {
    return option.isNotEmpty &&
           visual.isNotEmpty &&
           next.isNotEmpty;
  }

  @override
  String toString() {
    return 'ChoiceModel(option: $option, visual: $visual, next: $next)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChoiceModel &&
           other.option == option &&
           other.visual == visual &&
           other.next == next;
  }

  @override
  int get hashCode {
    return Object.hash(option, visual, next);
  }
}
