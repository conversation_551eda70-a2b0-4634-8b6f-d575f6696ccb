/// Database service for local data persistence
/// 
/// Manages SQLite database operations for caching story data,
/// user progress, and app settings locally.
library;

import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/story_model.dart';

/// Database service for local data management
class DatabaseService {
  static Database? _database;
  static const String _databaseName = 'story_quest.db';
  static const int _databaseVersion = 1;

  /// Gets the database instance (singleton pattern)
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// Initializes the database with required tables
  Future<Database> _initDatabase() async {
    final databasePath = await getDatabasesPath();
    final path = join(databasePath, _databaseName);

    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _createTables,
      onUpgrade: _upgradeDatabase,
    );
  }

  /// Creates database tables
  Future<void> _createTables(Database db, int version) async {
    // Stories table for caching story metadata
    await db.execute('''
      CREATE TABLE stories (
        story_id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        age_group TEXT NOT NULL,
        difficulty TEXT NOT NULL,
        moral TEXT NOT NULL,
        cover_image TEXT NOT NULL,
        estimated_time TEXT NOT NULL,
        data TEXT NOT NULL,
        downloaded_at INTEGER NOT NULL,
        last_accessed INTEGER NOT NULL
      )
    ''');

    // User progress table
    await db.execute('''
      CREATE TABLE user_progress (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        story_id TEXT NOT NULL,
        scene_id TEXT NOT NULL,
        completed_at INTEGER NOT NULL,
        choices_made TEXT,
        FOREIGN KEY (story_id) REFERENCES stories (story_id)
      )
    ''');

    // Assets table for tracking downloaded assets
    await db.execute('''
      CREATE TABLE assets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        story_id TEXT NOT NULL,
        asset_path TEXT NOT NULL,
        asset_type TEXT NOT NULL,
        local_path TEXT NOT NULL,
        downloaded_at INTEGER NOT NULL,
        FOREIGN KEY (story_id) REFERENCES stories (story_id)
      )
    ''');

    // App settings table
    await db.execute('''
      CREATE TABLE app_settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    print('Database tables created successfully');
  }

  /// Handles database upgrades
  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    // Handle database migrations here when version changes
    print('Upgrading database from version $oldVersion to $newVersion');
  }

  /// Saves a story to the database
  Future<void> saveStory(StoryModel story) async {
    final db = await database;
    final now = DateTime.now().millisecondsSinceEpoch;

    try {
      await db.insert(
        'stories',
        {
          'story_id': story.storyId,
          'title': story.title,
          'age_group': story.ageGroup,
          'difficulty': story.difficulty,
          'moral': story.moral,
          'cover_image': story.coverImage,
          'estimated_time': story.estimatedTime,
          'data': jsonEncode(story.toJson()),
          'downloaded_at': now,
          'last_accessed': now,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      print('Story ${story.storyId} saved to database');
    } catch (e) {
      print('Failed to save story: $e');
      rethrow;
    }
  }

  /// Retrieves a story from the database
  Future<StoryModel?> getStory(String storyId) async {
    final db = await database;

    try {
      final results = await db.query(
        'stories',
        where: 'story_id = ?',
        whereArgs: [storyId],
        limit: 1,
      );

      if (results.isNotEmpty) {
        final storyData = results.first;
        
        // Update last accessed time
        await _updateLastAccessed(storyId);
        
        // Parse story data
        final jsonData = jsonDecode(storyData['data'] as String) as Map<String, dynamic>;
        return StoryModel.fromJson(jsonData);
      }
      return null;
    } catch (e) {
      print('Failed to get story: $e');
      return null;
    }
  }

  /// Updates the last accessed time for a story
  Future<void> _updateLastAccessed(String storyId) async {
    final db = await database;
    final now = DateTime.now().millisecondsSinceEpoch;

    await db.update(
      'stories',
      {'last_accessed': now},
      where: 'story_id = ?',
      whereArgs: [storyId],
    );
  }

  /// Gets all cached stories
  Future<List<Map<String, dynamic>>> getAllStories() async {
    final db = await database;

    try {
      return await db.query(
        'stories',
        orderBy: 'last_accessed DESC',
      );
    } catch (e) {
      print('Failed to get all stories: $e');
      return [];
    }
  }

  /// Deletes a story from the database
  Future<void> deleteStory(String storyId) async {
    final db = await database;

    try {
      await db.transaction((txn) async {
        // Delete story
        await txn.delete(
          'stories',
          where: 'story_id = ?',
          whereArgs: [storyId],
        );

        // Delete related progress
        await txn.delete(
          'user_progress',
          where: 'story_id = ?',
          whereArgs: [storyId],
        );

        // Delete related assets
        await txn.delete(
          'assets',
          where: 'story_id = ?',
          whereArgs: [storyId],
        );
      });
      print('Story $storyId deleted from database');
    } catch (e) {
      print('Failed to delete story: $e');
      rethrow;
    }
  }

  /// Saves user progress for a story scene
  Future<void> saveProgress(
    String storyId,
    String sceneId,
    List<String>? choicesMade,
  ) async {
    final db = await database;
    final now = DateTime.now().millisecondsSinceEpoch;

    try {
      await db.insert(
        'user_progress',
        {
          'story_id': storyId,
          'scene_id': sceneId,
          'completed_at': now,
          'choices_made': choicesMade != null ? jsonEncode(choicesMade) : null,
        },
      );
    } catch (e) {
      print('Failed to save progress: $e');
      rethrow;
    }
  }

  /// Gets user progress for a story
  Future<List<Map<String, dynamic>>> getProgress(String storyId) async {
    final db = await database;

    try {
      return await db.query(
        'user_progress',
        where: 'story_id = ?',
        whereArgs: [storyId],
        orderBy: 'completed_at ASC',
      );
    } catch (e) {
      print('Failed to get progress: $e');
      return [];
    }
  }

  /// Saves app setting
  Future<void> saveSetting(String key, String value) async {
    final db = await database;
    final now = DateTime.now().millisecondsSinceEpoch;

    try {
      await db.insert(
        'app_settings',
        {
          'key': key,
          'value': value,
          'updated_at': now,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      print('Failed to save setting: $e');
      rethrow;
    }
  }

  /// Gets app setting
  Future<String?> getSetting(String key) async {
    final db = await database;

    try {
      final results = await db.query(
        'app_settings',
        where: 'key = ?',
        whereArgs: [key],
        limit: 1,
      );

      if (results.isNotEmpty) {
        return results.first['value'] as String;
      }
      return null;
    } catch (e) {
      print('Failed to get setting: $e');
      return null;
    }
  }

  /// Clears all data from the database
  Future<void> clearAllData() async {
    final db = await database;

    try {
      await db.transaction((txn) async {
        await txn.delete('stories');
        await txn.delete('user_progress');
        await txn.delete('assets');
        await txn.delete('app_settings');
      });
      print('All database data cleared');
    } catch (e) {
      print('Failed to clear database: $e');
      rethrow;
    }
  }

  /// Closes the database connection
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }
}
