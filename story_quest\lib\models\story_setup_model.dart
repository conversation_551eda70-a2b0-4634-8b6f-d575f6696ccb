/// Story setup model containing initial story configuration
///
/// Contains setting information, tone, context, and background elements
/// that establish the story environment.
library;

class StorySetupModel {
  /// The story's setting description
  final String setting;
  
  /// The emotional tone of the story
  final String tone;
  
  /// Context or theme of the story
  final String context;
  
  /// Brief introduction text for the story
  final String briefIntro;
  
  /// Background music filename
  final String backgroundMusic;

  const StorySetupModel({
    required this.setting,
    required this.tone,
    required this.context,
    required this.briefIntro,
    required this.backgroundMusic,
  });

  /// Creates a StorySetupModel from JSON data
  factory StorySetupModel.fromJson(Map<String, dynamic> json) {
    return StorySetupModel(
      setting: json['setting'] as String,
      tone: json['tone'] as String,
      context: json['context'] as String,
      briefIntro: json['brief_intro'] as String,
      backgroundMusic: json['background_music'] as String,
    );
  }

  /// Converts StorySetupModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'setting': setting,
      'tone': tone,
      'context': context,
      'brief_intro': briefIntro,
      'background_music': backgroundMusic,
    };
  }

  /// Creates a copy of this StorySetupModel with optional parameter overrides
  StorySetupModel copyWith({
    String? setting,
    String? tone,
    String? context,
    String? briefIntro,
    String? backgroundMusic,
  }) {
    return StorySetupModel(
      setting: setting ?? this.setting,
      tone: tone ?? this.tone,
      context: context ?? this.context,
      briefIntro: briefIntro ?? this.briefIntro,
      backgroundMusic: backgroundMusic ?? this.backgroundMusic,
    );
  }

  /// Validates that all required fields are present
  bool isValid() {
    return setting.isNotEmpty &&
           tone.isNotEmpty &&
           context.isNotEmpty &&
           briefIntro.isNotEmpty &&
           backgroundMusic.isNotEmpty;
  }

  @override
  String toString() {
    return 'StorySetupModel(setting: $setting, tone: $tone, context: $context)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StorySetupModel &&
           other.setting == setting &&
           other.tone == tone &&
           other.context == context &&
           other.briefIntro == briefIntro &&
           other.backgroundMusic == backgroundMusic;
  }

  @override
  int get hashCode {
    return Object.hash(setting, tone, context, briefIntro, backgroundMusic);
  }
}
