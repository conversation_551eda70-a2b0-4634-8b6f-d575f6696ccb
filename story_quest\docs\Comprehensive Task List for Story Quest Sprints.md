# Comprehensive Task List for Story Quest Sprints

This document outlines the tasks for each sprint in the development of the *Story Quest* app. Tasks are categorized into **macro tasks** (broad steps) and **nano tasks** (specific, actionable sub-steps) to ensure a manageable and detailed roadmap.

---

## Sprint 1: Project Setup and Initial Infrastructure


### Macro Task 3: Implement local caching with `sqflite` for offline story access
- **Nano Task 3.1**: Add `sqflite` dependency to `pubspec.yaml` and run `flutter pub get`.
- **Nano Task 3.2**: Create a `DatabaseHelper` class in `lib/services` to manage SQLite operations.
- **Nano Task 3.3**: Define a schema with tables for stories, scenes, and choices (e.g., `id`, `title`, `json_data`).
- **Nano Task 3.4**: Write CRUD methods (create, read, update, delete) for the database in `DatabaseHelper`.

### Macro Task 4: Create data models (e.g., `Story`, `Scene`, `Choice`) based on `story.json`
- **Nano Task 4.1**: Review `story.json` to identify fields like `title`, `scenes`, `choices`.
- **Nano Task 4.2**: Define Dart classes (`Story`, `Scene`, `Choice`) in `lib/models` with relevant properties.
- **Nano Task 4.3**: Add `fromJson` and `toJson` methods to each class for serialization/deserialization.

### Macro Task 5: Develop an API service to fetch stories from Firebase and cache them locally
- **Nano Task 5.1**: Set up Firestore with a `stories` collection to store story JSON data.
- **Nano Task 5.2**: Create an `ApiService` class in `lib/services` to fetch data from Firestore.
- **Nano Task 5.3**: Add a method in `ApiService` to save fetched stories to `sqflite` using `DatabaseHelper`.
- **Nano Task 5.4**: Implement sync logic to update local cache when online and use cached data offline.

### Macro Task 6: Implement a flexible TTS (Text-to-Speech) system using `flutter_tts`
- **Nano Task 6.1**: Add `flutter_tts` to `pubspec.yaml` and run `flutter pub get`.
- **Nano Task 6.2**: Create a `TtsService` class in `lib/services` to encapsulate TTS functionality.
- **Nano Task 6.3**: Implement methods in `TtsService`: `initTts()`, `setLanguage()`, `speak()`, `stop()`.
- **Nano Task 6.4**: Link `TtsService` to story playback to read scene text aloud.

---

## Sprint 2: Core UI and Navigation

### Macro Task 1: Build the Splash Screen
- **Nano Task 1.1**: Design the UI in `lib/screens/splash_screen.dart` with a gradient background and mascot image.
- **Nano Task 1.2**: Add a "Continue" button with a glowing animation using `AnimatedContainer`.
- **Nano Task 1.3**: Use a timer or button press to navigate to the FTUE screen with `Navigator.push()`.

### Macro Task 2: Develop the FTUE (First Time User Experience) Screen
- **Nano Task 2.1**: Create `lib/screens/ftue_screen.dart` with specified colors and layout from the design.
- **Nano Task 2.2**: Add "Create Account" and "Login" buttons with `ElevatedButton`.
- **Nano Task 2.3**: Include a privacy notice `Text` widget and a consent `Checkbox`.
- **Nano Task 2.4**: Implement account creation/login logic with Firebase Auth.

### Macro Task 3: Create the Child Profile Selection Screen
- **Nano Task 3.1**: Design `lib/screens/profile_selection.dart` with pastel colors and circular `CircleAvatar` widgets.
- **Nano Task 3.2**: Add a button to create new profiles, saving to Firestore via `ApiService`.
- **Nano Task 3.3**: Enable profile selection to navigate to the Homepage with `Navigator.push()`.

### Macro Task 4: Build the Homepage
- **Nano Task 4.1**: Create `lib/screens/home_screen.dart` with a gradient background and app logo.
- **Nano Task 4.2**: Add navigation buttons ("Story Time", "My Stuff", "Parent Zone") using `ElevatedButton`.
- **Nano Task 4.3**: Implement a `DropdownButton` for theme selection (Light, Dark, High Contrast).

### Macro Task 5: Implement responsive UI elements
- **Nano Task 5.1**: Use `MediaQuery` in widgets to adapt to different screen sizes.
- **Nano Task 5.2**: Apply gradients with `LinearGradient` and animations with `AnimationController`.
- **Nano Task 5.3**: Test and adjust UI for portrait and landscape orientations.

### Macro Task 6: Set up navigation
- **Nano Task 6.1**: Use `Navigator` in `main.dart` to manage screen transitions.
- **Nano Task 6.2**: Define named routes in `MaterialApp` (e.g., `/splash`, `/home`).
- **Nano Task 6.3**: Handle back navigation with `WillPopScope` where needed.

---

## Sprint 3: Story Browsing and Playback

### Macro Task 1: Develop the Story Library
- **Nano Task 1.1**: Create `lib/screens/story_library.dart` with a `GridView` for story cards.
- **Nano Task 1.2**: Fetch story metadata from `ApiService` (online) or `DatabaseHelper` (offline).
- **Nano Task 1.3**: Display story details (cover image, title, age badge) in each grid item.
- **Nano Task 1.4**: Add filter and search options using `TextField` and dropdowns.

### Macro Task 2: Create Story Cards
- **Nano Task 2.1**: Design a reusable `StoryCard` widget in `lib/widgets` with specified styles.
- **Nano Task 2.2**: Add a "Play/Download" button with hover effects using `GestureDetector`.
- **Nano Task 2.3**: Include a short description `Text` below the card.

### Macro Task 3: Implement the Story Introduction Screen
- **Nano Task 3.1**: Create `lib/screens/story_intro.dart` with a themed background from story data.
- **Nano Task 3.2**: Display cover image and details (title, description) using `Image` and `Text`.
- **Nano Task 3.3**: Add "Meet Characters" and "Play/Download" buttons with navigation.

### Macro Task 4: Build the Meet Characters Screen
- **Nano Task 4.1**: Create `lib/screens/meet_characters.dart` with a `CarouselSlider` for character cards.
- **Nano Task 4.2**: Add "Play Audio" buttons on each card, linked to `TtsService`.
- **Nano Task 4.3**: Implement navigation with buttons and swipe gestures via `CarouselController`.

### Macro Task 5: Develop the Story Play Screen
- **Nano Task 5.1**: Create `lib/screens/story_play.dart` with a themed overlay using `Stack`.
- **Nano Task 5.2**: Add a control bar with play/pause, progress (`Slider`), and volume controls.
- **Nano Task 5.3**: Display subtitles with `Text` synced to TTS timing.
- **Nano Task 5.4**: Enable tapping characters for sound effects using `GestureDetector`.

### Macro Task 6: Ensure offline playback
- **Nano Task 6.1**: Check story download status in `ApiService` before playback.
- **Nano Task 6.2**: Load story data from `DatabaseHelper` if no internet is detected.
- **Nano Task 6.3**: Use default assets (e.g., placeholder images) if specific assets are missing.

---

## Sprint 4: Interactive Choices and Moral Outcomes

### Macro Task 1: Add interactivity with branching choices
- **Nano Task 1.1**: Update `story.json` and `Story` model to include `choices` at scene endpoints.
- **Nano Task 1.2**: Implement branching logic in `StoryPlayScreen` to follow user-selected paths.
- **Nano Task 1.3**: Update UI to show choice options at key story points.

### Macro Task 2: Implement Choice Pop-Ups and Moral Moment Pop-Ups
- **Nano Task 2.1**: Design a `ChoicePopup` widget with buttons for each option.
- **Nano Task 2.2**: Create a `MoralPopup` widget to display reflections post-choice.
- **Nano Task 2.3**: Show pop-ups using `showDialog` at appropriate story moments.

### Macro Task 3: Develop a badge system
- **Nano Task 3.1**: Define badge types (e.g., "Kindness", "Bravery") and earning criteria.
- **Nano Task 3.2**: Add logic in `StoryPlayScreen` to award badges based on choices.
- **Nano Task 3.3**: Save badges to Firestore or `sqflite` under the child’s profile.

### Macro Task 4: Create the My Rewards Screen
- **Nano Task 4.1**: Design `lib/screens/my_rewards.dart` to showcase badges with `GridView`.
- **Nano Task 4.2**: Fetch and display badges from the child’s profile via `ApiService`.
- **Nano Task 4.3**: Add badge animations (e.g., scale-up) using `AnimatedContainer`.

---

## Sprint 5: Parental Controls and Feedback

### Macro Task 1: Implement the Parent Zone
- **Nano Task 1.1**: Add a PIN or biometric verification step in `lib/screens/parent_zone.dart`.
- **Nano Task 1.2**: Design the UI with settings tiles (e.g., `ListTile`) for control options.
- **Nano Task 1.3**: Implement content filters and progress tracking with Firestore queries.

### Macro Task 2: Develop the Parent Settings Screen
- **Nano Task 2.1**: Add filter options (e.g., age range) in `lib/screens/parent_settings.dart`.
- **Nano Task 2.2**: Show child progress (stories completed, badges) with `ListView`.
- **Nano Task 2.3**: Include a feedback button linking to the Feedback Screen.

### Macro Task 3: Add the Feedback Screen
- **Nano Task 3.1**: Create `lib/screens/feedback_screen.dart` with a `TextField` for input.
- **Nano Task 3.2**: Send feedback to Firestore or store locally if offline.
- **Nano Task 3.3**: Show a confirmation dialog using `showDialog` after submission.

### Macro Task 4: Include Parental Notification Pop-Ups
- **Nano Task 4.1**: Define triggers (e.g., new badge earned) in `ApiService`.
- **Nano Task 4.2**: Show notifications with `showDialog` in the Parent Zone.
- **Nano Task 4.3**: Add customization options for notifications in `ParentSettings`.

---

## Sprint 6: TTS Flexibility and Offline Enhancements

### Macro Task 1: Abstract the TTS functionality
- **Nano Task 1.1**: Define a `TtsService` interface with abstract methods in `lib/services`.
- **Nano Task 1.2**: Implement the interface with `flutter_tts` in a concrete class.
- **Nano Task 1.3**: Refactor all TTS calls to use the interface instead of direct `flutter_tts`.

### Macro Task 2: Enhance offline mode
- **Nano Task 2.1**: Optimize `DatabaseHelper` queries for faster data retrieval.
- **Nano Task 2.2**: Add a sync check in `ApiService` to update local data when online.
- **Nano Task 2.3**: Resolve data conflicts by prioritizing newer timestamps.

### Macro Task 3: Add integrity checks for downloaded assets
- **Nano Task 3.1**: Generate checksums for assets in `ApiService` during download.
- **Nano Task 3.2**: Verify asset integrity before playback in `StoryPlayScreen`.
- **Nano Task 3.3**: Prompt re-download with a dialog if verification fails.

---

## Sprint 7: Polish and Launch Preparation

### Macro Task 1: Polish the UI
- **Nano Task 1.1**: Add screen transition animations with `PageRouteBuilder`.
- **Nano Task 1.2**: Ensure consistent fonts, colors, and spacing across all screens.
- **Nano Task 1.3**: Optimize app performance with `flutter analyze` and `flutter run --release`.

### Macro Task 2: Document the codebase and APIs
- **Nano Task 2.1**: Add inline comments to all major classes and methods.
- **Nano Task 2.2**: Write API docs for `ApiService` using Dart doc comments.
- **Nano Task 2.3**: Create a `README.md` with setup and dev instructions.

### Macro Task 3: Prepare app store assets
- **Nano Task 3.1**: Design app icons and screenshots in `assets`.
- **Nano Task 3.2**: Draft app descriptions and release notes for stores.
- **Nano Task 3.3**: Configure app listings in Google Play Console and App Store Connect.

### Macro Task 4: Submit the app for launch
- **Nano Task 4.1**: Run full tests (unit, integration) with `flutter test`.
- **Nano Task 4.2**: Build and submit APKs/IPAs to app stores.
- **Nano Task 4.3**: Monitor submission status and address reviewer feedback.

---

## Sprint 8: Post-Launch Support and Bug Fixes

### Macro Task 1: Monitor the app for bugs and user feedback
- **Nano Task 1.1**: Integrate Firebase Crashlytics for crash reporting.
- **Nano Task 1.2**: Collect feedback via the in-app Feedback Screen.
- **Nano Task 1.3**: Analyze usage with Firebase Analytics to spot issues.

### Macro Task 2: Implement fixes and improvements
- **Nano Task 2.1**: Prioritize bugs/feedback by impact and frequency.
- **Nano Task 2.2**: Develop fixes and test them in a staging environment.
- **Nano Task 2.3**: Release updates via app store submissions.

### Macro Task 3: Provide ongoing support
- **Nano Task 3.1**: Respond to user queries via email or in-app support.
- **Nano Task 3.2**: Update FAQs in `README.md` based on common issues.
- **Nano Task 3.3**: Plan enhancements based on user suggestions.

---

## Sprint 9: Advanced Parental Dashboard and Cultural Diversity

### Macro Task 1: Expand the parental dashboard
- **Nano Task 1.1**: Add detailed progress reports (e.g., time spent, choices made).
- **Nano Task 1.2**: Implement charts with a package like `fl_chart`.
- **Nano Task 1.3**: Allow export of reports as PDFs via `pdf` package.

### Macro Task 2: Add culturally diverse stories
- **Nano Task 2.1**: Source stories reflecting various cultures and languages.
- **Nano Task 2.2**: Update `story.json` and Firestore with new content.
- **Nano Task 2.3**: Test stories for cultural sensitivity and accuracy.

---

## Sprint 10: Educational Alignment and Testing

### Macro Task 1: Align stories with Social-Emotional Learning (SEL) standards
- **Nano Task 1.1**: Map story morals to SEL competencies (e.g., empathy).
- **Nano Task 1.2**: Update `MoralPopup` to highlight SEL lessons.
- **Nano Task 1.3**: Validate alignment with educators or SEL experts.

### Macro Task 2: Conduct end-to-end testing
- **Nano Task 2.1**: Write integration tests for all user flows in `test`.
- **Nano Task 2.2**: Test offline mode, TTS, and interactivity thoroughly.
- **Nano Task 2.3**: Fix any bugs or performance issues found.

---

## Sprint 11: AI-Driven Personalization

### Macro Task 1: Implement AI-driven story customization
- **Nano Task 1.1**: Use a simple rule-based system to suggest stories based on past choices.
- **Nano Task 1.2**: Store user preferences in Firestore under profiles.
- **Nano Task 1.3**: Update `StoryLibrary` to prioritize personalized recommendations.

### Macro Task 2: Ensure safety and privacy
- **Nano Task 2.1**: Avoid collecting sensitive data beyond preferences.
- **Nano Task 2.2**: Add parental consent for personalization in `ParentSettings`.
- **Nano Task 2.3**: Test for data leaks or privacy issues.

---

## Sprint 12: Community-Driven Content and Multi-Language Support

### Macro Task 1: Introduce a moderated platform for user-submitted stories
- **Nano Task 1.1**: Create a submission form in `lib/screens/community.dart`.
- **Nano Task 1.2**: Store submissions in Firestore with a “pending” status.
- **Nano Task 1.3**: Add an admin tool to review and approve stories.

### Macro Task 2: Add multi-language support
- **Nano Task 2.1**: Use `intl` package for localization in Flutter.
- **Nano Task 2.2**: Translate UI strings and sample stories into 2+ languages.
- **Nano Task 2.3**: Update `TtsService` to support multiple languages.

---

This list provides a detailed, actionable plan for developing *Story Quest*, covering all sprints from setup to advanced features. Each task is designed to be clear and executable by the development team.