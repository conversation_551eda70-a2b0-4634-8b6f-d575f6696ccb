 D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\default\\default_image.png D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\default\\happy-outro-8110.mp3 D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story013\\story.json D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story013\\images\\alex_alone.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story013\\images\\alex_sad.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story013\\images\\choice_moment.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story013\\images\\finding_toy.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story013\\images\\helping_hand_icon.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story013\\images\\mia_determined.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story013\\images\\park_intro.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story013\\images\\playing_icon.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story013\\images\\story_cover.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story013\\assets\\calm_piano.mp3 D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story014\\story.json D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story014\\images\\choice_moment.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story014\\images\\keeping_icon.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story014\\images\\ruby_sees_acorn.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story014\\images\\sammy_finds_acorn.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story014\\images\\sammy_keeps_acorn.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story014\\images\\sharing_acorn.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story014\\images\\sharing_icon.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\story014\\images\\story_cover.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts\\MaterialIcons-Regular.otf D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders\\ink_sparkle.frag D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json:  D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\pubspec.yaml D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\default\\default_image.png D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\default\\happy-outro-8110.mp3 D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story013\\story.json D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story013\\images\\alex_alone.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story013\\images\\alex_sad.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story013\\images\\choice_moment.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story013\\images\\finding_toy.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story013\\images\\helping_hand_icon.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story013\\images\\mia_determined.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story013\\images\\park_intro.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story013\\images\\playing_icon.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story013\\images\\story_cover.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story013\\assets\\calm_piano.mp3 D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story014\\story.json D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story014\\images\\choice_moment.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story014\\images\\keeping_icon.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story014\\images\\ruby_sees_acorn.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story014\\images\\sammy_finds_acorn.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story014\\images\\sammy_keeps_acorn.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story014\\images\\sharing_acorn.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story014\\images\\sharing_icon.jpg D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\assets\\story014\\images\\story_cover.jpg C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf C:\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf C:\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\.dart_tool\\flutter_build\\58d697a83bf0bc035268655306042f78\\native_assets.json C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\_fe_analyzer_shared-82.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\_flutterfire_internals-1.3.56\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\analyzer-7.4.5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\archive-3.6.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\args-2.7.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build-2.5.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_config-1.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_daemon-4.0.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_resolvers-2.5.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_runner-2.5.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_runner_core-9.1.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\built_collection-5.1.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\built_value-8.10.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\carousel_slider-4.2.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\checked_yaml-2.0.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cloud_firestore-5.6.9\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cloud_firestore_platform_interface-6.6.9\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cloud_firestore_web-4.4.9\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\code_builder-4.10.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dart_style-3.1.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fake_async-1.3.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_auth-5.6.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_auth_platform_interface-7.7.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_auth_web-5.15.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_core-3.14.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_core_platform_interface-5.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_core_web-2.23.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_storage-12.4.7\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_storage_platform_interface-5.2.7\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_storage_web-3.10.14\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_lints-5.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_tts-3.8.5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\frontend_server_client-4.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get_it-7.7.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\glob-2.1.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_identity_services_web-0.3.3+1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_sign_in-6.3.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_sign_in_android-6.2.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_sign_in_ios-5.9.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_sign_in_platform_interface-2.5.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_sign_in_web-0.12.4+4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\graphs-2.3.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_multi_server-3.2.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\io-1.0.5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\js-0.7.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker-10.0.9\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lints-5.1.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logging-1.3.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-2.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mockito-5.4.6\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\nested-1.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_config-2.2.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.17\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pool-1.5.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pub_semver-2.2.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pubspec_parse-1.5.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shelf-1.4.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shelf_web_socket-3.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_gen-2.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_android-2.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_darwin-2.4.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_transform-2.1.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.3.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\timing-1.0.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vm_service-15.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\watcher-1.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket-1.0.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket_channel-3.0.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\yaml-3.1.3\\LICENSE C:\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE C:\\flutter\\packages\\flutter\\LICENSE D:\\AliM\ Studio\\App\ 01\\App\\story_quest\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD349182646