{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Install\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\AliM Studio\\App 01\\App\\story_quest\\build\\.cxx\\Debug\\2x18z2m4\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\Install\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\AliM Studio\\App 01\\App\\story_quest\\build\\.cxx\\Debug\\2x18z2m4\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\Install\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\Install\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}