{"logs": [{"outputFile": "com.example.story_quest.app-mergeDebugResources-47:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8411a046f9616658209ad8ea4a5d985f\\transformed\\browser-1.4.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,397", "endColumns": "109,106,124,109", "endOffsets": "160,267,392,502"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6304,6501,6608,6733", "endColumns": "109,106,124,109", "endOffsets": "6409,6603,6728,6838"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6250b576436e3aba2e4229fd698d3169\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-el\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4934", "endColumns": "163", "endOffsets": "5093"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\05840cd26434a51e94c2f67f7d2b7d17\\transformed\\preference-1.2.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,265,348,490,659,744", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "173,260,343,485,654,739,819"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6231,6414,6843,6926,7254,7423,7508", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "6299,6496,6921,7063,7418,7503,7583"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\61d3d4fe2dd837d40b39785170a0ad2d\\transformed\\jetified-play-services-base-18.1.0\\res\\values-el\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,482,610,717,894,1015,1129,1230,1415,1519,1685,1810,1984,2125,2190,2248", "endColumns": "106,181,127,106,176,120,113,100,184,103,165,124,173,140,64,57,78", "endOffsets": "299,481,609,716,893,1014,1128,1229,1414,1518,1684,1809,1983,2124,2189,2247,2326"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3865,3976,4162,4294,4405,4586,4711,4829,5098,5287,5395,5565,5694,5872,6017,6086,6148", "endColumns": "110,185,131,110,180,124,117,104,188,107,169,128,177,144,68,61,82", "endOffsets": "3971,4157,4289,4400,4581,4706,4824,4929,5282,5390,5560,5689,5867,6012,6081,6143,6226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3cdebf5eb7fc2e0836e09b33315aab36\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,176", "endColumns": "120,122", "endOffsets": "171,294"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2886,3007", "endColumns": "120,122", "endOffsets": "3002,3125"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\62e42733fc48d7e4200114b93a9c82fb\\transformed\\core-1.13.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3130,3228,3331,3431,3534,3642,3748,7153", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "3223,3326,3426,3529,3637,3743,3860,7249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2ef2678f1ebb7c2f233ed01d9a85f9da\\transformed\\appcompat-1.1.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,940,1031,1123,1218,1312,1413,1506,1601,1698,1789,1880,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,85,90,91,94,93,100,92,94,96,90,90,83,110,108,101,110,109,107,170,99,84", "endOffsets": "218,329,446,531,637,760,849,935,1026,1118,1213,1307,1408,1501,1596,1693,1784,1875,1959,2070,2179,2281,2392,2502,2610,2781,2881,2966"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,940,1031,1123,1218,1312,1413,1506,1601,1698,1789,1880,1964,2075,2184,2286,2397,2507,2615,2786,7068", "endColumns": "117,110,116,84,105,122,88,85,90,91,94,93,100,92,94,96,90,90,83,110,108,101,110,109,107,170,99,84", "endOffsets": "218,329,446,531,637,760,849,935,1026,1118,1213,1307,1408,1501,1596,1693,1784,1875,1959,2070,2179,2281,2392,2502,2610,2781,2881,7148"}}]}]}