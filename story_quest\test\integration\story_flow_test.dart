/// Integration tests for story browsing and playback flow
/// 
/// Tests the complete user journey from story library to story completion,
/// including offline functionality and error handling.
library;

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:story_quest/main.dart' as app;
import 'package:story_quest/services/navigation_service.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Story Flow Integration Tests', () {
    testWidgets('Complete story browsing and playback flow', (tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate through splash screen
      await tester.tap(find.text('Continue'));
      await tester.pumpAndSettle();

      // Skip FTUE for testing
      await tester.tap(find.text('Create Account'));
      await tester.pumpAndSettle();

      // Select a profile
      await tester.tap(find.text('Emma').first);
      await tester.pumpAndSettle();

      // Navigate to Story Library from homepage
      await tester.tap(find.text('Story Time'));
      await tester.pumpAndSettle();

      // Verify story library loaded
      expect(find.text('Story Library'), findsOneWidget);
      expect(find.text('The Friendly Forest'), findsOneWidget);

      // Tap on a story card
      await tester.tap(find.text('The Friendly Forest'));
      await tester.pumpAndSettle();

      // Verify story introduction screen
      expect(find.text('Story Preview'), findsOneWidget);
      expect(find.text('Meet Characters'), findsOneWidget);
      expect(find.text('Play Story'), findsOneWidget);

      // Navigate to Meet Characters
      await tester.tap(find.text('Meet Characters'));
      await tester.pumpAndSettle();

      // Verify meet characters screen
      expect(find.text('Meet the Characters'), findsOneWidget);
      expect(find.text('Mia'), findsOneWidget);

      // Navigate to story play
      await tester.tap(find.text('Play Story'));
      await tester.pumpAndSettle();

      // Verify story play screen loaded
      expect(find.byIcon(Icons.play_arrow), findsOneWidget);
      expect(find.byIcon(Icons.settings), findsOneWidget);

      // Test play button
      await tester.tap(find.byIcon(Icons.play_arrow));
      await tester.pump(const Duration(seconds: 1));

      // Verify playback started (button should change to pause)
      expect(find.byIcon(Icons.pause), findsOneWidget);

      // Test settings
      await tester.tap(find.byIcon(Icons.settings));
      await tester.pumpAndSettle();

      expect(find.text('Story Settings'), findsOneWidget);
      expect(find.text('Show Subtitles'), findsOneWidget);

      // Close settings
      await tester.tap(find.text('Close'));
      await tester.pumpAndSettle();

      // Test back navigation
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Should show exit confirmation
      expect(find.text('Exit Story?'), findsOneWidget);
      
      // Confirm exit
      await tester.tap(find.text('Exit'));
      await tester.pumpAndSettle();

      // Should be back at previous screen
      expect(find.text('Meet the Characters'), findsOneWidget);
    });

    testWidgets('Story library filtering and search', (tester) async {
      // Navigate to story library (assuming we're already in the app)
      await _navigateToStoryLibrary(tester);

      // Test genre filtering
      await tester.tap(find.text('Adventure'));
      await tester.pumpAndSettle();

      // Verify filter applied (this would depend on actual story data)
      expect(find.text('Adventure'), findsWidgets);

      // Test search functionality (if available for age group)
      final searchField = find.byType(TextField);
      if (searchField.evaluate().isNotEmpty) {
        await tester.enterText(searchField, 'Forest');
        await tester.pumpAndSettle();

        // Verify search results
        expect(find.text('The Friendly Forest'), findsOneWidget);
      }

      // Clear search
      if (searchField.evaluate().isNotEmpty) {
        await tester.enterText(searchField, '');
        await tester.pumpAndSettle();
      }

      // Reset filter to All
      await tester.tap(find.text('All Stories'));
      await tester.pumpAndSettle();
    });

    testWidgets('Offline story functionality', (tester) async {
      await _navigateToStoryLibrary(tester);

      // Find a story card with download button
      final downloadButton = find.text('Download');
      if (downloadButton.evaluate().isNotEmpty) {
        // Test download functionality
        await tester.tap(downloadButton.first);
        await tester.pumpAndSettle();

        // This would show a coming soon dialog in the current implementation
        expect(find.text('Download functionality'), findsOneWidget);
        
        // Close dialog
        await tester.tap(find.text('OK'));
        await tester.pumpAndSettle();
      }

      // Test offline indicator (if any stories are marked as downloaded)
      final offlineIndicator = find.byIcon(Icons.offline_pin);
      if (offlineIndicator.evaluate().isNotEmpty) {
        // Verify offline stories are marked
        expect(offlineIndicator, findsWidgets);
      }
    });

    testWidgets('Accessibility features', (tester) async {
      await _navigateToStoryLibrary(tester);

      // Test semantic labels
      final storyCard = find.text('The Friendly Forest').first;
      await tester.tap(storyCard);
      await tester.pumpAndSettle();

      // Verify accessibility widgets are present
      expect(find.byType(Semantics), findsWidgets);

      // Test large touch targets (buttons should be at least 48x48)
      final buttons = find.byType(ElevatedButton);
      for (final button in buttons.evaluate()) {
        final renderBox = button.renderObject as RenderBox?;
        if (renderBox != null) {
          expect(renderBox.size.width, greaterThanOrEqualTo(48.0));
          expect(renderBox.size.height, greaterThanOrEqualTo(48.0));
        }
      }
    });

    testWidgets('Error handling and fallbacks', (tester) async {
      await _navigateToStoryLibrary(tester);

      // Test navigation with invalid story data
      // This would require mocking or injecting invalid data

      // Test asset loading failures
      // This would require mocking asset loading to fail

      // For now, verify error handling UI elements exist
      expect(find.byType(SnackBar), findsNothing); // No errors initially
    });

    testWidgets('Responsive design on different screen sizes', (tester) async {
      // Test tablet layout
      await tester.binding.setSurfaceSize(const Size(800, 600));
      await tester.pumpAndSettle();

      await _navigateToStoryLibrary(tester);

      // Verify grid layout adapts to larger screen
      final gridView = find.byType(GridView);
      expect(gridView, findsOneWidget);

      // Test mobile layout
      await tester.binding.setSurfaceSize(const Size(400, 800));
      await tester.pumpAndSettle();

      // Verify layout adapts to smaller screen
      expect(gridView, findsOneWidget);

      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });
  });
}

/// Helper function to navigate to story library
Future<void> _navigateToStoryLibrary(WidgetTester tester) async {
  // This assumes we start from homepage
  final storyTimeButton = find.text('Story Time');
  if (storyTimeButton.evaluate().isNotEmpty) {
    await tester.tap(storyTimeButton);
    await tester.pumpAndSettle();
  }
  
  // Verify we're in the story library
  expect(find.text('Story Library'), findsOneWidget);
}

/// Helper function to simulate story completion
Future<void> _completeStory(WidgetTester tester) async {
  // This would simulate the story completion flow
  // For now, it's a placeholder
}

/// Helper function to test offline mode
Future<void> _testOfflineMode(WidgetTester tester) async {
  // This would test offline functionality
  // Requires mocking network connectivity
}

/// Helper function to test different device orientations
Future<void> _testOrientations(WidgetTester tester) async {
  // Test portrait
  await tester.binding.setSurfaceSize(const Size(400, 800));
  await tester.pumpAndSettle();
  
  // Test landscape
  await tester.binding.setSurfaceSize(const Size(800, 400));
  await tester.pumpAndSettle();
  
  // Reset
  await tester.binding.setSurfaceSize(null);
}
