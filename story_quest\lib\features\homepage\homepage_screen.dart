/// Homepage screen for Story Quest app
/// 
/// Main navigation hub with colorful buttons, theme selection,
/// and responsive design for all device types.
library;

import 'package:flutter/material.dart';

/// Theme options for the app
enum AppTheme {
  light('Light', Icons.light_mode),
  dark('Dark', Icons.dark_mode),
  highContrast('High Contrast', Icons.contrast);

  const AppTheme(this.label, this.icon);
  final String label;
  final IconData icon;
}

/// Homepage screen widget
class HomepageScreen extends StatefulWidget {
  /// Current selected theme
  final AppTheme currentTheme;
  
  /// Callback when Story Time is tapped
  final VoidCallback? onStoryTime;
  
  /// Callback when My Stuff is tapped
  final VoidCallback? onMyStuff;
  
  /// Callback when Resume Stories is tapped
  final VoidCallback? onResumeStories;
  
  /// Callback when My Rewards is tapped
  final VoidCallback? onMyRewards;
  
  /// Callback when Parent Zone is tapped
  final VoidCallback? onParentZone;
  
  /// Callback when <PERSON><PERSON><PERSON> is tapped
  final VoidCallback? onTutorials;
  
  /// Callback when theme is changed
  final Function(AppTheme)? onThemeChanged;

  const HomepageScreen({
    super.key,
    this.currentTheme = AppTheme.light,
    this.onStoryTime,
    this.onMyStuff,
    this.onResumeStories,
    this.onMyRewards,
    this.onParentZone,
    this.onTutorials,
    this.onThemeChanged,
  });

  @override
  State<HomepageScreen> createState() => _HomepageScreenState();
}

class _HomepageScreenState extends State<HomepageScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late Animation<double> _logoAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize logo animation
    _logoController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    
    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    _logoController.forward();
  }

  @override
  void dispose() {
    _logoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFFF9800), // Orange
              Color(0xFFFFCA28), // Yellow
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: _buildMainContent(isTablet),
              ),
              _buildThemeSelector(),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the header with logo and parent zone
  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // App logo
          AnimatedBuilder(
            animation: _logoAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _logoAnimation.value,
                child: Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.menu_book,
                        size: 32,
                        color: Color(0xFFFF9800),
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'Story Quest',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            color: Colors.black,
                            offset: Offset(1, 1),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          
          // Parent Zone button
          Semantics(
            button: true,
            label: 'Parent Zone - Access parental controls and settings',
            child: GestureDetector(
              onTap: widget.onParentZone,
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: const Color(0xFFB0BEC5),
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.lock,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the main content area with navigation buttons
  Widget _buildMainContent(bool isTablet) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        children: [
          // Main navigation buttons
          Expanded(
            flex: 3,
            child: _buildNavigationButtons(isTablet),
          ),
          
          // Tutorials button
          _buildTutorialsButton(),
          
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  /// Builds the main navigation buttons grid
  Widget _buildNavigationButtons(bool isTablet) {
    final buttonWidth = isTablet ? 200.0 : 150.0;
    final buttonHeight = isTablet ? 80.0 : 60.0;
    
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Story Time button
        _buildMainButton(
          'Story Time',
          const Color(0xFFF44336), // Bright red
          Icons.auto_stories,
          buttonWidth,
          buttonHeight,
          widget.onStoryTime,
          'Start reading interactive stories',
        ),
        
        const SizedBox(height: 20),
        
        // My Stuff button with sub-options
        _buildMyStuffButton(buttonWidth, buttonHeight),
      ],
    );
  }

  /// Builds a main navigation button
  Widget _buildMainButton(
    String title,
    Color color,
    IconData icon,
    double width,
    double height,
    VoidCallback? onTap,
    String semanticLabel,
  ) {
    return Semantics(
      button: true,
      label: semanticLabel,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.4),
                blurRadius: 8,
                spreadRadius: 2,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the My Stuff button with sub-options
  Widget _buildMyStuffButton(double width, double height) {
    return Column(
      children: [
        // Main My Stuff button
        _buildMainButton(
          'My Stuff',
          const Color(0xFFAB47BC), // Purple
          Icons.folder,
          width,
          height,
          widget.onMyStuff,
          'Access your saved stories and rewards',
        ),
        
        const SizedBox(height: 12),
        
        // Sub-options
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildSubButton(
              'Resume Stories',
              Icons.play_circle_fill,
              widget.onResumeStories,
              'Continue reading saved stories',
            ),
            const SizedBox(width: 16),
            _buildSubButton(
              'My Rewards',
              Icons.star,
              widget.onMyRewards,
              'View earned rewards and achievements',
            ),
          ],
        ),
      ],
    );
  }

  /// Builds a sub-button for My Stuff options
  Widget _buildSubButton(
    String title,
    IconData icon,
    VoidCallback? onTap,
    String semanticLabel,
  ) {
    return Semantics(
      button: true,
      label: semanticLabel,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: const Color(0xFFAB47BC),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFAB47BC),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the tutorials button
  Widget _buildTutorialsButton() {
    return Semantics(
      button: true,
      label: 'Tutorials - Learn how to use the app',
      child: GestureDetector(
        onTap: widget.onTutorials,
        child: Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            color: const Color(0xFF1976D2), // Blue
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF1976D2).withValues(alpha: 0.4),
                blurRadius: 8,
                spreadRadius: 2,
              ),
            ],
          ),
          child: const Icon(
            Icons.help,
            color: Colors.white,
            size: 28,
          ),
        ),
      ),
    );
  }

  /// Builds the theme selector at the bottom
  Widget _buildThemeSelector() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFFB0BEC5).withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Theme:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 12),
          DropdownButton<AppTheme>(
            value: widget.currentTheme,
            onChanged: (AppTheme? value) => widget.onThemeChanged?.call(value!),
            dropdownColor: const Color(0xFFB0BEC5),
            underline: const SizedBox(),
            icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
            items: AppTheme.values.map((theme) {
              return DropdownMenuItem<AppTheme>(
                value: theme,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      theme.icon,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      theme.label,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
