/// Homepage screen for Story Quest app
/// 
/// Main navigation hub with colorful buttons, theme selection,
/// and responsive design for all device types.
library;

import 'package:flutter/material.dart';
import '../../utils/device_utils.dart';
import '../../services/theme_service.dart';

/// Homepage screen widget
class HomepageScreen extends StatefulWidget {
  /// Callback when Story Time is tapped
  final VoidCallback? onStoryTime;

  /// Callback when My Stuff is tapped
  final VoidCallback? onMyStuff;

  /// Callback when Resume Stories is tapped
  final VoidCallback? onResumeStories;

  /// Callback when My Rewards is tapped
  final VoidCallback? onMyRewards;

  /// Callback when Parent Zone is tapped
  final VoidCallback? onParentZone;

  /// Callback when Tutorials is tapped
  final VoidCallback? onTutorials;

  /// Callback when theme is changed
  final Function(AppThemeType)? onThemeChanged;

  const HomepageScreen({
    super.key,
    this.onStoryTime,
    this.onMyStuff,
    this.onResumeStories,
    this.onMyRewards,
    this.onParentZone,
    this.onTutorials,
    this.onThemeChanged,
  });

  @override
  State<HomepageScreen> createState() => _HomepageScreenState();
}

class _HomepageScreenState extends State<HomepageScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late Animation<double> _logoAnimation;
  final ThemeService _themeService = ThemeService();

  @override
  void initState() {
    super.initState();
    
    // Initialize logo animation
    _logoController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    
    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    _logoController.forward();
  }

  @override
  void dispose() {
    _logoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveLayoutBuilder(
      builder: (context, deviceType, orientation) {
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) async {
            if (didPop) return;
            _showCalmExitDialog();
          },
          child: Scaffold(
            body: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFFFF9800), // Orange
                  Color(0xFFFFCA28), // Yellow
                ],
              ),
            ),
            child: SafeArea(
              child: _buildAdaptiveLayout(deviceType, orientation),
            ),
          ),
        ),
        );
      },
    );
  }

  /// Builds adaptive layout based on device type and orientation
  Widget _buildAdaptiveLayout(DeviceType deviceType, ScreenOrientation orientation) {
    switch (deviceType) {
      case DeviceType.mobile:
        return _buildMobileLayout();
      case DeviceType.tablet:
        return orientation == ScreenOrientation.portrait
            ? _buildTabletPortraitLayout()
            : _buildTabletLandscapeLayout();
      case DeviceType.desktop:
        return _buildDesktopLayout();
      case DeviceType.tv:
        return _buildTVLayout();
    }
  }

  /// Builds mobile layout
  Widget _buildMobileLayout() {
    return Column(
      children: [
        _buildHeader(),
        Expanded(
          child: _buildMainContent(),
        ),
        _buildThemeSelector(),
      ],
    );
  }

  /// Builds tablet portrait layout
  Widget _buildTabletPortraitLayout() {
    return Column(
      children: [
        _buildHeader(),
        Expanded(
          child: _buildMainContent(),
        ),
        _buildThemeSelector(),
      ],
    );
  }

  /// Builds tablet landscape layout
  Widget _buildTabletLandscapeLayout() {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: _buildMainContent(),
              ),
            ],
          ),
        ),
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildTutorialsButton(),
              SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 20)),
              _buildThemeSelector(),
            ],
          ),
        ),
      ],
    );
  }

  /// Builds desktop layout
  Widget _buildDesktopLayout() {
    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: DeviceUtils.getMaxContentWidth(context),
        ),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Column(
                children: [
                  _buildHeader(),
                  Expanded(
                    child: _buildMainContent(),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildTutorialsButton(),
                  SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 30)),
                  _buildThemeSelector(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds TV layout
  Widget _buildTVLayout() {
    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: DeviceUtils.getMaxContentWidth(context),
        ),
        child: Row(
          children: [
            Expanded(
              flex: 3,
              child: Column(
                children: [
                  _buildHeader(),
                  Expanded(
                    child: _buildMainContent(),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 2,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildTutorialsButton(),
                  SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 40)),
                  _buildThemeSelector(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the header with logo and parent zone
  Widget _buildHeader() {
    return Padding(
      padding: DeviceUtils.getResponsivePadding(context),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // App logo
          AnimatedBuilder(
            animation: _logoAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _logoAnimation.value,
                child: Row(
                  children: [
                    Container(
                      width: DeviceUtils.getResponsiveIconSize(context, 60),
                      height: DeviceUtils.getResponsiveIconSize(context, 60),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.menu_book,
                        size: DeviceUtils.getResponsiveIconSize(context, 32),
                        color: const Color(0xFFFF9800),
                      ),
                    ),
                    SizedBox(width: DeviceUtils.getResponsiveSpacing(context, 12)),
                    Text(
                      'Story Quest',
                      style: TextStyle(
                        fontSize: DeviceUtils.getResponsiveFontSize(context, 24),
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            color: Colors.black,
                            offset: Offset(1, 1),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          
          // Parent Zone button
          Semantics(
            button: true,
            label: 'Parent Zone - Access parental controls and settings',
            child: GestureDetector(
              onTap: widget.onParentZone,
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: const Color(0xFFB0BEC5),
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.lock,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the main content area with navigation buttons
  Widget _buildMainContent() {
    return Padding(
      padding: DeviceUtils.getResponsivePadding(context),
      child: Column(
        children: [
          // Main navigation buttons
          Expanded(
            flex: 3,
            child: _buildNavigationButtons(),
          ),

          // Tutorials button (only show in mobile/tablet portrait)
          if (context.isMobile || (context.isTablet && context.isPortrait))
            _buildTutorialsButton(),

          SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 20)),
        ],
      ),
    );
  }

  /// Builds the main navigation buttons grid
  Widget _buildNavigationButtons() {
    final buttonWidth = DeviceUtils.getResponsiveCardWidth(context);
    final buttonHeight = DeviceUtils.getResponsiveButtonHeight(context);
    
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Story Time button
        _buildMainButton(
          'Story Time',
          const Color(0xFFF44336), // Bright red
          Icons.auto_stories,
          buttonWidth,
          buttonHeight,
          widget.onStoryTime,
          'Start reading interactive stories',
        ),
        
        const SizedBox(height: 20),
        
        // My Stuff button with sub-options
        _buildMyStuffButton(buttonWidth, buttonHeight),
      ],
    );
  }

  /// Builds a main navigation button
  Widget _buildMainButton(
    String title,
    Color color,
    IconData icon,
    double width,
    double height,
    VoidCallback? onTap,
    String semanticLabel,
  ) {
    return Semantics(
      button: true,
      label: semanticLabel,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.4),
                blurRadius: 8,
                spreadRadius: 2,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the My Stuff button with sub-options
  Widget _buildMyStuffButton(double width, double height) {
    return Column(
      children: [
        // Main My Stuff button
        _buildMainButton(
          'My Stuff',
          const Color(0xFFAB47BC), // Purple
          Icons.folder,
          width,
          height,
          widget.onMyStuff,
          'Access your saved stories and rewards',
        ),
        
        const SizedBox(height: 12),
        
        // Sub-options
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildSubButton(
              'Resume Stories',
              Icons.play_circle_fill,
              widget.onResumeStories,
              'Continue reading saved stories',
            ),
            const SizedBox(width: 16),
            _buildSubButton(
              'My Rewards',
              Icons.star,
              widget.onMyRewards,
              'View earned rewards and achievements',
            ),
          ],
        ),
      ],
    );
  }

  /// Builds a sub-button for My Stuff options
  Widget _buildSubButton(
    String title,
    IconData icon,
    VoidCallback? onTap,
    String semanticLabel,
  ) {
    return Semantics(
      button: true,
      label: semanticLabel,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: const Color(0xFFAB47BC),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFAB47BC),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the tutorials button
  Widget _buildTutorialsButton() {
    return Semantics(
      button: true,
      label: 'Tutorials - Learn how to use the app',
      child: GestureDetector(
        onTap: widget.onTutorials,
        child: Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            color: const Color(0xFF1976D2), // Blue
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF1976D2).withValues(alpha: 0.4),
                blurRadius: 8,
                spreadRadius: 2,
              ),
            ],
          ),
          child: const Icon(
            Icons.help,
            color: Colors.white,
            size: 28,
          ),
        ),
      ),
    );
  }

  /// Builds the theme selector at the bottom
  Widget _buildThemeSelector() {
    return ListenableBuilder(
      listenable: _themeService,
      builder: (context, child) {
        return Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: const Color(0xFFB0BEC5).withValues(alpha: 0.8),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Theme:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 12),
              DropdownButton<AppThemeType>(
                value: _themeService.currentTheme,
                onChanged: (AppThemeType? value) {
                  if (value != null) {
                    _themeService.setTheme(value);
                    widget.onThemeChanged?.call(value);
                  }
                },
                dropdownColor: const Color(0xFFB0BEC5),
                underline: const SizedBox(),
                icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
                items: AppThemeType.values.map((theme) {
                  return DropdownMenuItem<AppThemeType>(
                    value: theme,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          theme.icon,
                          color: Colors.white,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          theme.label,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Shows calm exit dialog
  void _showCalmExitDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Icon(
              Icons.waving_hand,
              color: Colors.orange,
              size: DeviceUtils.getResponsiveIconSize(context, 28),
            ),
            SizedBox(width: DeviceUtils.getResponsiveSpacing(context, 12)),
            Text(
              'Time to go?',
              style: TextStyle(
                fontSize: DeviceUtils.getResponsiveFontSize(context, 22),
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2E7D32),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Are you sure you want to leave Story Quest?',
              style: TextStyle(
                fontSize: DeviceUtils.getResponsiveFontSize(context, 16),
                color: const Color(0xFF2E7D32),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 16)),
            Text(
              'Your stories will be waiting for you when you come back!',
              style: TextStyle(
                fontSize: DeviceUtils.getResponsiveFontSize(context, 14),
                color: const Color(0xFF2E7D32).withValues(alpha: 0.7),
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF2E7D32),
              padding: EdgeInsets.symmetric(
                horizontal: DeviceUtils.getResponsiveSpacing(context, 20),
                vertical: DeviceUtils.getResponsiveSpacing(context, 12),
              ),
            ),
            child: Text(
              'Stay & Play',
              style: TextStyle(
                fontSize: DeviceUtils.getResponsiveFontSize(context, 16),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Exit the app gracefully
              if (mounted) {
                Navigator.of(context).pop();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFF9800),
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                horizontal: DeviceUtils.getResponsiveSpacing(context, 20),
                vertical: DeviceUtils.getResponsiveSpacing(context, 12),
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: Text(
              'Goodbye',
              style: TextStyle(
                fontSize: DeviceUtils.getResponsiveFontSize(context, 16),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
