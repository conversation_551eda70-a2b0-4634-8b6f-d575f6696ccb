/// Text-to-Speech service interface
///
/// Abstract interface defining TTS functionality for the story app.
/// Allows for different TTS implementations while maintaining consistent API.
library;

import 'package:flutter/material.dart';
import '../models/voice_model.dart';

/// Abstract TTS service interface
abstract class TTSService {
  /// Speaks the given text using the current voice configuration
  /// 
  /// [text] - The text to be spoken
  /// [voiceConfig] - Optional voice configuration to override current settings
  /// Returns a Future that completes when speech starts
  Future<void> speak(String text, {VoiceModel? voiceConfig});

  /// Stops any currently playing speech
  /// 
  /// Returns a Future that completes when speech is stopped
  Future<void> stop();

  /// Sets the language for speech synthesis
  /// 
  /// [language] - Language code (e.g., "en-US", "en-GB")
  /// Returns a Future that completes when language is set
  Future<void> setLanguage(String language);

  /// Sets the voice configuration
  /// 
  /// [voice] - Voice configuration including name, pitch, rate, and volume
  /// Returns a Future that completes when voice is configured
  Future<void> setVoice(VoiceModel voice);

  /// Gets the current speaking state
  /// 
  /// Returns true if <PERSON><PERSON> is currently speaking
  Future<bool> get isSpeaking;

  /// Gets available voices for the current language
  /// 
  /// Returns a list of available voice names
  Future<List<String>> getAvailableVoices();

  /// Gets available languages
  /// 
  /// Returns a list of available language codes
  Future<List<String>> getAvailableLanguages();

  /// Sets the speech rate (speed)
  /// 
  /// [rate] - Speech rate (0.1 to 3.0, where 1.0 is normal)
  /// Returns a Future that completes when rate is set
  Future<void> setSpeechRate(double rate);

  /// Sets the speech pitch
  /// 
  /// [pitch] - Speech pitch (0.5 to 2.0, where 1.0 is normal)
  /// Returns a Future that completes when pitch is set
  Future<void> setPitch(double pitch);

  /// Sets the speech volume
  /// 
  /// [volume] - Speech volume (0.0 to 1.0, where 1.0 is maximum)
  /// Returns a Future that completes when volume is set
  Future<void> setVolume(double volume);

  /// Pauses the current speech (if supported by implementation)
  /// 
  /// Returns a Future that completes when speech is paused
  Future<void> pause();

  /// Resumes paused speech (if supported by implementation)
  /// 
  /// Returns a Future that completes when speech is resumed
  Future<void> resume();

  /// Disposes of the TTS service and releases resources
  ///
  /// Should be called when the service is no longer needed
  Future<void> dispose();

  /// Sets up speech completion callback
  ///
  /// [callback] - Function to call when speech completes
  void setCompletionCallback(VoidCallback? callback);

  /// Sets up speech start callback
  ///
  /// [callback] - Function to call when speech starts
  void setStartCallback(VoidCallback? callback);

  /// Sets up speech error callback
  ///
  /// [callback] - Function to call when speech encounters an error
  void setErrorCallback(Function(String error)? callback);

  /// Gets the current voice configuration
  ///
  /// Returns the currently active voice model
  VoiceModel? get currentVoice;

  /// Gets the current language
  ///
  /// Returns the currently set language code
  String? get currentLanguage;

  /// Gets the current speech rate
  ///
  /// Returns the current speech rate value
  double get currentSpeechRate;

  /// Gets the current pitch
  ///
  /// Returns the current pitch value
  double get currentPitch;

  /// Gets the current volume
  ///
  /// Returns the current volume value
  double get currentVolume;

  /// Checks if the TTS service is initialized and ready
  ///
  /// Returns true if the service is ready to use
  bool get isInitialized;

  /// Initializes the TTS service
  ///
  /// Should be called before using other methods
  Future<void> initialize();

  /// Speaks text with SSML markup support
  ///
  /// [ssml] - SSML formatted text for advanced speech control
  Future<void> speakSSML(String ssml);

  /// Gets estimated speech duration for given text
  ///
  /// [text] - Text to estimate duration for
  /// Returns estimated duration in milliseconds
  Future<int> getEstimatedDuration(String text);
}
