/// Story Introduction screen
/// 
/// Displays story details with themed background, animated preview,
/// and navigation to character introduction or story playback.
library;

import 'package:flutter/material.dart';
import '../../models/story_model.dart';
import '../../utils/accessibility_helper.dart';

/// Story introduction screen widget
class StoryIntroductionScreen extends StatefulWidget {
  /// The story to introduce
  final StoryModel story;
  
  /// Whether the story is downloaded
  final bool isDownloaded;
  
  /// Callback when Meet Characters is pressed
  final VoidCallback? onMeetCharacters;
  
  /// Callback when Play/Download is pressed
  final VoidCallback? onPlayDownload;
  
  /// Callback for back navigation
  final VoidCallback? onBack;

  const StoryIntroductionScreen({
    super.key,
    required this.story,
    this.isDownloaded = false,
    this.onMeetCharacters,
    this.onPlayDownload,
    this.onBack,
  });

  @override
  State<StoryIntroductionScreen> createState() => _StoryIntroductionScreenState();
}

class _StoryIntroductionScreenState extends State<StoryIntroductionScreen>
    with TickerProviderStateMixin {
  late AnimationController _bounceController;
  late AnimationController _fadeController;
  late Animation<double> _bounceAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize bounce animation for preview
    _bounceController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 10.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticInOut,
    ));

    // Initialize fade animation for content
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOutCubic,
    ));

    // Start animations
    _fadeController.forward();
    _bounceController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _bounceController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    
    return Scaffold(
      body: Container(
        decoration: _buildThemedBackground(),
        child: SafeArea(
          child: isTablet ? _buildTabletLayout() : _buildMobileLayout(),
        ),
      ),
    );
  }

  /// Builds themed background based on story context
  BoxDecoration _buildThemedBackground() {
    // Default forest theme - could be customized based on story.setup.setting
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          Color(0xFF388E3C), // Forest green
          Color(0xFF2E7D32), // Darker green
        ],
      ),
    );
  }

  /// Builds layout for mobile devices
  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            _buildHeader(),
            const SizedBox(height: 30),
            _buildCoverImage(),
            const SizedBox(height: 30),
            _buildStoryDescription(),
            const SizedBox(height: 20),
            _buildMetadata(),
            const SizedBox(height: 40),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// Builds layout for tablets
  Widget _buildTabletLayout() {
    return Padding(
      padding: const EdgeInsets.all(40),
      child: Row(
        children: [
          // Left side - Cover and metadata
          Expanded(
            flex: 2,
            child: Column(
              children: [
                _buildHeader(),
                const SizedBox(height: 40),
                _buildCoverImage(),
                const SizedBox(height: 30),
                _buildMetadata(),
              ],
            ),
          ),
          
          const SizedBox(width: 40),
          
          // Right side - Description and actions
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Spacer(),
                _buildStoryDescription(),
                const SizedBox(height: 40),
                _buildActionButtons(),
                const Spacer(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the header with back button and title
  Widget _buildHeader() {
    return Row(
      children: [
        AccessibilityHelper.createAccessibleButton(
          child: const Icon(
            Icons.arrow_back,
            color: Colors.white,
            size: 28,
          ),
          onPressed: widget.onBack,
          semanticLabel: 'Go back to story library',
        ),
        
        const SizedBox(width: 16),
        
        Expanded(
          child: AccessibilityHelper.createAccessibleText(
            'Story Preview',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              shadows: [
                Shadow(
                  color: Colors.black,
                  offset: Offset(1, 1),
                  blurRadius: 2,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the animated cover image
  Widget _buildCoverImage() {
    return AnimatedBuilder(
      animation: _bounceAnimation,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Transform.translate(
            offset: Offset(0, _bounceAnimation.value),
            child: Container(
              width: 200,
              height: 300,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(0xFFE3F2FD),
                        Color(0xFFBBDEFB),
                      ],
                    ),
                  ),
                  child: AccessibilityHelper.createAccessibleImage(
                    image: const AssetImage('assets/default/default_image.png'),
                    semanticLabel: 'Cover image for ${widget.story.title}',
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Builds the story description section
  Widget _buildStoryDescription() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title
              AccessibilityHelper.createAccessibleText(
                widget.story.title,
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                semanticLabel: 'Story title: ${widget.story.title}',
              ),
              
              const SizedBox(height: 16),
              
              // Brief intro
              AccessibilityHelper.createAccessibleText(
                widget.story.setup.briefIntro,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  height: 1.5,
                ),
                semanticLabel: 'Story description: ${widget.story.setup.briefIntro}',
              ),
              
              const SizedBox(height: 16),
              
              // Moral
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.lightbulb,
                      color: Color(0xFFFFCA28),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: AccessibilityHelper.createAccessibleText(
                        'Moral: ${widget.story.moral}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white,
                          fontStyle: FontStyle.italic,
                        ),
                        semanticLabel: 'Story moral: ${widget.story.moral}',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the metadata section
  Widget _buildMetadata() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildMetadataItem(
              Icons.child_care,
              'Age',
              widget.story.ageGroup,
              'Suitable for ages ${widget.story.ageGroup}',
            ),
            _buildMetadataItem(
              Icons.access_time,
              'Duration',
              widget.story.estimatedTime,
              'Estimated reading time ${widget.story.estimatedTime}',
            ),
            _buildMetadataItem(
              Icons.trending_up,
              'Level',
              widget.story.difficulty,
              'Difficulty level ${widget.story.difficulty}',
            ),
          ],
        ),
      ),
    );
  }

  /// Builds individual metadata item
  Widget _buildMetadataItem(
    IconData icon,
    String label,
    String value,
    String semanticLabel,
  ) {
    return Semantics(
      label: semanticLabel,
      child: Column(
        children: [
          Icon(
            icon,
            color: const Color(0xFF757575),
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF757575),
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the action buttons
  Widget _buildActionButtons() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Meet Characters button
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton.icon(
                onPressed: widget.onMeetCharacters,
                icon: const Icon(Icons.people),
                label: const Text(
                  'Meet Characters',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1976D2), // Blue
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(28),
                  ),
                  elevation: 4,
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Play/Download button
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton.icon(
                onPressed: widget.onPlayDownload,
                icon: Icon(widget.isDownloaded ? Icons.play_arrow : Icons.download),
                label: Text(
                  widget.isDownloaded ? 'Play Story' : 'Download & Play',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4CAF50), // Green
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(28),
                  ),
                  elevation: 4,
                  shadowColor: const Color(0xFF4CAF50).withValues(alpha: 0.4),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
