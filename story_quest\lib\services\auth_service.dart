/// Authentication service for Firebase Auth integration
///
/// Provides comprehensive authentication functionality including email/password,
/// Google Sign-In, password reset, and user session management.
library;

import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Authentication result types
enum AuthResult {
  success,
  userNotFound,
  wrongPassword,
  emailAlreadyInUse,
  weakPassword,
  invalidEmail,
  userDisabled,
  tooManyRequests,
  networkError,
  unknown,
}

/// User authentication data
class AuthUser {
  final String uid;
  final String? email;
  final String? displayName;
  final String? photoURL;
  final bool isEmailVerified;

  const AuthUser({
    required this.uid,
    this.email,
    this.displayName,
    this.photoURL,
    required this.isEmailVerified,
  });

  factory AuthUser.fromFirebaseUser(User user) {
    return AuthUser(
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL,
      isEmailVerified: user.emailVerified,
    );
  }
}

/// Authentication service
class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  
  // Persistence settings
  static const String _persistenceKey = 'auth_persistence';
  static const String _persistenceExpiryKey = 'auth_persistence_expiry';

  /// Gets the current authenticated user
  AuthUser? get currentUser {
    final user = _auth.currentUser;
    return user != null ? AuthUser.fromFirebaseUser(user) : null;
  }

  /// Stream of authentication state changes
  Stream<AuthUser?> get authStateChanges {
    return _auth.authStateChanges().map((user) {
      return user != null ? AuthUser.fromFirebaseUser(user) : null;
    });
  }

  /// Signs in with email and password
  Future<AuthResult> signInWithEmailAndPassword({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (rememberMe) {
        await _setPersistence();
      }

      return AuthResult.success;
    } on FirebaseAuthException catch (e) {
      return _handleFirebaseAuthException(e);
    } catch (e) {
      return AuthResult.unknown;
    }
  }

  /// Creates account with email and password
  Future<AuthResult> createAccountWithEmailAndPassword({
    required String email,
    required String password,
    String? displayName,
    bool rememberMe = false,
  }) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Update display name if provided
      if (displayName != null && credential.user != null) {
        await credential.user!.updateDisplayName(displayName);
      }

      // Send email verification
      await credential.user?.sendEmailVerification();

      if (rememberMe) {
        await _setPersistence();
      }

      return AuthResult.success;
    } on FirebaseAuthException catch (e) {
      return _handleFirebaseAuthException(e);
    } catch (e) {
      return AuthResult.unknown;
    }
  }

  /// Signs in with Google
  Future<AuthResult> signInWithGoogle({bool rememberMe = false}) async {
    try {
      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser == null) {
        // User canceled the sign-in
        return AuthResult.unknown;
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase with the Google credential
      await _auth.signInWithCredential(credential);

      if (rememberMe) {
        await _setPersistence();
      }

      return AuthResult.success;
    } on FirebaseAuthException catch (e) {
      return _handleFirebaseAuthException(e);
    } catch (e) {
      return AuthResult.unknown;
    }
  }

  /// Sends password reset email
  Future<AuthResult> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      return AuthResult.success;
    } on FirebaseAuthException catch (e) {
      return _handleFirebaseAuthException(e);
    } catch (e) {
      return AuthResult.unknown;
    }
  }

  /// Signs out the current user
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      await _googleSignIn.signOut();
      await _clearPersistence();
    } catch (e) {
      // Handle sign out errors silently
    }
  }

  /// Checks if user session should persist
  Future<bool> shouldPersistSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final persistenceEnabled = prefs.getBool(_persistenceKey) ?? false;
      
      if (!persistenceEnabled) return false;

      final expiryTimestamp = prefs.getInt(_persistenceExpiryKey) ?? 0;
      final expiryDate = DateTime.fromMillisecondsSinceEpoch(expiryTimestamp);
      
      return DateTime.now().isBefore(expiryDate);
    } catch (e) {
      return false;
    }
  }

  /// Sets user session persistence for 30 days
  Future<void> _setPersistence() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final expiryDate = DateTime.now().add(const Duration(days: 30));
      
      await prefs.setBool(_persistenceKey, true);
      await prefs.setInt(_persistenceExpiryKey, expiryDate.millisecondsSinceEpoch);
    } catch (e) {
      // Handle persistence error silently
    }
  }

  /// Clears user session persistence
  Future<void> _clearPersistence() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_persistenceKey);
      await prefs.remove(_persistenceExpiryKey);
    } catch (e) {
      // Handle clear error silently
    }
  }

  /// Handles Firebase Auth exceptions
  AuthResult _handleFirebaseAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return AuthResult.userNotFound;
      case 'wrong-password':
        return AuthResult.wrongPassword;
      case 'email-already-in-use':
        return AuthResult.emailAlreadyInUse;
      case 'weak-password':
        return AuthResult.weakPassword;
      case 'invalid-email':
        return AuthResult.invalidEmail;
      case 'user-disabled':
        return AuthResult.userDisabled;
      case 'too-many-requests':
        return AuthResult.tooManyRequests;
      case 'network-request-failed':
        return AuthResult.networkError;
      default:
        return AuthResult.unknown;
    }
  }

  /// Gets user-friendly error message for auth result
  static String getErrorMessage(AuthResult result) {
    switch (result) {
      case AuthResult.success:
        return 'Success';
      case AuthResult.userNotFound:
        return 'No account found with this email address.';
      case AuthResult.wrongPassword:
        return 'Incorrect password. Please try again.';
      case AuthResult.emailAlreadyInUse:
        return 'An account already exists with this email address.';
      case AuthResult.weakPassword:
        return 'Password is too weak. Please choose a stronger password.';
      case AuthResult.invalidEmail:
        return 'Please enter a valid email address.';
      case AuthResult.userDisabled:
        return 'This account has been disabled. Please contact support.';
      case AuthResult.tooManyRequests:
        return 'Too many failed attempts. Please try again later.';
      case AuthResult.networkError:
        return 'Network error. Please check your connection and try again.';
      case AuthResult.unknown:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Validates email format
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Validates password strength
  static bool isValidPassword(String password) {
    // At least 6 characters, contains letter and number
    return password.length >= 6 && 
           RegExp(r'^(?=.*[A-Za-z])(?=.*\d)').hasMatch(password);
  }

  /// Gets password strength description
  static String getPasswordStrengthMessage(String password) {
    if (password.isEmpty) {
      return 'Password is required';
    }
    if (password.length < 6) {
      return 'Password must be at least 6 characters';
    }
    if (!RegExp(r'^(?=.*[A-Za-z])').hasMatch(password)) {
      return 'Password must contain at least one letter';
    }
    if (!RegExp(r'^(?=.*\d)').hasMatch(password)) {
      return 'Password must contain at least one number';
    }
    return 'Password is strong';
  }
}
