/// Accessibility helper utilities for Story Quest
/// 
/// Provides consistent accessibility features including semantics,
/// high contrast support, and screen reader compatibility.
library;

import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Accessibility helper class with utility methods
class AccessibilityHelper {
  /// Minimum touch target size for accessibility (48x48 dp)
  static const double minTouchTargetSize = 48.0;

  /// High contrast color ratios
  static const double minimumContrastRatio = 4.5;
  static const double enhancedContrastRatio = 7.0;

  /// Creates a semantically labeled button
  static Widget createAccessibleButton({
    required Widget child,
    required VoidCallback? onPressed,
    required String semanticLabel,
    String? tooltip,
    double? width,
    double? height,
    EdgeInsets? padding,
  }) {
    return Semantics(
      button: true,
      label: semanticLabel,
      child: Tooltip(
        message: tooltip ?? semanticLabel,
        child: SizedBox(
          width: width ?? minTouchTargetSize,
          height: height ?? minTouchTargetSize,
          child: InkWell(
            onTap: onPressed,
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: padding ?? const EdgeInsets.all(8),
              child: child,
            ),
          ),
        ),
      ),
    );
  }

  /// Creates an accessible text widget with proper contrast
  static Widget createAccessibleText(
    String text, {
    TextStyle? style,
    Color? backgroundColor,
    String? semanticLabel,
    TextAlign? textAlign,
    int? maxLines,
  }) {
    final textStyle = style ?? const TextStyle();
    final textColor = textStyle.color ?? Colors.black;
    final bgColor = backgroundColor ?? Colors.transparent;

    // Check contrast ratio
    if (!hasGoodContrast(textColor, bgColor)) {
      debugPrint('Warning: Poor contrast ratio between text and background');
    }

    return Semantics(
      label: semanticLabel ?? text,
      child: Text(
        text,
        style: textStyle,
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: maxLines != null ? TextOverflow.ellipsis : null,
      ),
    );
  }

  /// Creates an accessible image with semantic description
  static Widget createAccessibleImage({
    required ImageProvider image,
    required String semanticLabel,
    double? width,
    double? height,
    BoxFit? fit,
    String? tooltip,
  }) {
    return Semantics(
      image: true,
      label: semanticLabel,
      child: Tooltip(
        message: tooltip ?? semanticLabel,
        child: Image(
          image: image,
          width: width,
          height: height,
          fit: fit,
          semanticLabel: semanticLabel,
        ),
      ),
    );
  }

  /// Creates an accessible form field
  static Widget createAccessibleFormField({
    required String label,
    required TextEditingController controller,
    String? hint,
    String? errorText,
    bool obscureText = false,
    TextInputType? keyboardType,
    Function(String)? onChanged,
  }) {
    return Semantics(
      textField: true,
      label: label,
      hint: hint,
      child: TextFormField(
        controller: controller,
        obscureText: obscureText,
        keyboardType: keyboardType,
        onChanged: onChanged,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          errorText: errorText,
          border: const OutlineInputBorder(),
          contentPadding: const EdgeInsets.all(16),
        ),
      ),
    );
  }

  /// Checks if two colors have good contrast ratio
  static bool hasGoodContrast(Color foreground, Color background) {
    final ratio = calculateContrastRatio(foreground, background);
    return ratio >= minimumContrastRatio;
  }

  /// Calculates contrast ratio between two colors
  static double calculateContrastRatio(Color color1, Color color2) {
    final luminance1 = _calculateLuminance(color1);
    final luminance2 = _calculateLuminance(color2);
    
    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Calculates relative luminance of a color
  static double _calculateLuminance(Color color) {
    final r = _linearizeColorComponent(color.red / 255.0);
    final g = _linearizeColorComponent(color.green / 255.0);
    final b = _linearizeColorComponent(color.blue / 255.0);
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /// Linearizes a color component for luminance calculation
  static double _linearizeColorComponent(double component) {
    if (component <= 0.03928) {
      return component / 12.92;
    } else {
      return math.pow((component + 0.055) / 1.055, 2.4).toDouble();
    }
  }

  /// Provides haptic feedback for accessibility
  static void provideHapticFeedback() {
    HapticFeedback.vibrate();
  }

  /// Announces text to screen readers
  static void announceToScreenReader(String message) {
    // SemanticsService.announce(message, TextDirection.ltr);
    // Note: SemanticsService is not available in current Flutter version
    debugPrint('Screen reader announcement: $message');
  }

  /// Creates high contrast colors for better accessibility
  static ColorScheme createHighContrastColorScheme({bool isDark = false}) {
    if (isDark) {
      return const ColorScheme.dark(
        primary: Colors.white,
        onPrimary: Colors.black,
        secondary: Colors.yellow,
        onSecondary: Colors.black,
        surface: Colors.black,
        onSurface: Colors.white,
        background: Colors.black,
        onBackground: Colors.white,
        error: Colors.red,
        onError: Colors.white,
      );
    } else {
      return const ColorScheme.light(
        primary: Colors.black,
        onPrimary: Colors.white,
        secondary: Colors.blue,
        onSecondary: Colors.white,
        surface: Colors.white,
        onSurface: Colors.black,
        background: Colors.white,
        onBackground: Colors.black,
        error: Colors.red,
        onError: Colors.white,
      );
    }
  }

  /// Validates that a widget meets accessibility requirements
  static bool validateAccessibility(Widget widget) {
    // This would perform accessibility validation
    // For now, it's a placeholder that returns true
    return true;
  }

  /// Gets accessible text size based on system settings
  static double getAccessibleTextSize(BuildContext context, double baseSize) {
    final mediaQuery = MediaQuery.of(context);
    return baseSize * mediaQuery.textScaleFactor;
  }

  /// Creates accessible spacing based on touch target requirements
  static EdgeInsets getAccessiblePadding({
    double horizontal = 8.0,
    double vertical = 8.0,
  }) {
    return EdgeInsets.symmetric(
      horizontal: math.max(horizontal, 8.0),
      vertical: math.max(vertical, 8.0),
    );
  }

  /// Ensures minimum touch target size
  static Size ensureMinimumTouchTarget(Size size) {
    return Size(
      math.max(size.width, minTouchTargetSize),
      math.max(size.height, minTouchTargetSize),
    );
  }

  /// Creates accessible focus decoration
  static BoxDecoration createFocusDecoration({
    Color? focusColor,
    double borderWidth = 2.0,
  }) {
    return BoxDecoration(
      border: Border.all(
        color: focusColor ?? Colors.blue,
        width: borderWidth,
      ),
      borderRadius: BorderRadius.circular(4),
    );
  }

  /// Wraps a widget with accessibility features
  static Widget wrapWithAccessibility({
    required Widget child,
    String? label,
    String? hint,
    bool? button,
    bool? header,
    bool? focusable,
    VoidCallback? onTap,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      button: button,
      header: header,
      focusable: focusable,
      onTap: onTap,
      child: child,
    );
  }
}
