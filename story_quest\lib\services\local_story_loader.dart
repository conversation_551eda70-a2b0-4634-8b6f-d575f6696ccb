/// Local Story Loader service
/// 
/// Loads stories from local assets (story013, story014) and provides
/// them to the app for immediate playback without download.
library;

import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/index.dart';

/// Service for loading stories from local assets
class LocalStoryLoader {
  static const List<String> _localStoryIds = ['story013', 'story014'];
  
  /// Cache for loaded stories
  static final Map<String, StoryModel> _storyCache = {};

  /// Loads all available local stories
  static Future<List<StoryModel>> loadAllLocalStories() async {
    final stories = <StoryModel>[];
    
    for (final storyId in _localStoryIds) {
      try {
        final story = await loadLocalStory(storyId);
        if (story != null) {
          stories.add(story);
        }
      } catch (e) {
        print('Failed to load local story $storyId: $e');
      }
    }
    
    return stories;
  }

  /// Loads a specific local story by ID
  static Future<StoryModel?> loadLocalStory(String storyId) async {
    // Check cache first
    if (_storyCache.containsKey(storyId)) {
      return _storyCache[storyId];
    }

    try {
      // Load story.json from assets
      final storyJsonPath = 'assets/$storyId/story.json';
      final storyJsonString = await rootBundle.loadString(storyJsonPath);
      final storyData = jsonDecode(storyJsonString) as Map<String, dynamic>;
      
      // Create story model
      final story = StoryModel.fromJson(storyData);
      
      // Validate story
      if (!story.isValid()) {
        print('Invalid story structure for $storyId');
        return null;
      }
      
      // Cache the story
      _storyCache[storyId] = story;
      
      return story;
    } catch (e) {
      print('Error loading local story $storyId: $e');
      return null;
    }
  }

  /// Checks if a story is available locally
  static bool isStoryAvailableLocally(String storyId) {
    return _localStoryIds.contains(storyId);
  }

  /// Gets the asset path for a local story resource
  static String getLocalAssetPath(String storyId, String assetPath) {
    if (!isStoryAvailableLocally(storyId)) {
      // Return default asset if story not available locally
      if (assetPath.contains('.jpg') || assetPath.contains('.png')) {
        return 'assets/default/default_image.png';
      } else if (assetPath.contains('.mp3') || assetPath.contains('.wav')) {
        return 'assets/default/happy-outro-8110.mp3';
      }
      return assetPath;
    }

    // For local stories, construct the full asset path
    if (assetPath.startsWith('assets/')) {
      return assetPath; // Already full path
    }

    // Determine asset type and construct path
    if (assetPath.contains('.jpg') || assetPath.contains('.png')) {
      return 'assets/$storyId/images/$assetPath';
    } else if (assetPath.contains('.mp3') || assetPath.contains('.wav')) {
      return 'assets/$storyId/assets/$assetPath';
    } else {
      return 'assets/$storyId/$assetPath';
    }
  }

  /// Tests loading a specific story and prints debug information
  static Future<void> testStoryLoading(String storyId) async {
    print('Testing story loading for: $storyId');

    try {
      final story = await loadLocalStory(storyId);
      if (story == null) {
        print('❌ Failed to load story $storyId');
        return;
      }

      print('✅ Successfully loaded story: ${story.title}');
      print('   - Age Group: ${story.ageGroup}');
      print('   - Difficulty: ${story.difficulty}');
      print('   - Scenes: ${story.scenes.length}');
      print('   - Characters: ${story.characters.length}');
      print('   - Cover Image: ${story.coverImage}');

      // Test asset paths
      final coverPath = getLocalAssetPath(storyId, story.coverImage);
      print('   - Cover Path: $coverPath');

      // Test loading the cover image
      try {
        await rootBundle.load(coverPath);
        print('   ✅ Cover image loads successfully');
      } catch (e) {
        print('   ❌ Cover image failed to load: $e');
      }

      // Test first scene image if available
      if (story.scenes.isNotEmpty && story.scenes.first.image.isNotEmpty) {
        final scenePath = getLocalAssetPath(storyId, story.scenes.first.image);
        print('   - First Scene Image: $scenePath');

        try {
          await rootBundle.load(scenePath);
          print('   ✅ First scene image loads successfully');
        } catch (e) {
          print('   ❌ First scene image failed to load: $e');
        }
      }

    } catch (e) {
      print('❌ Error testing story $storyId: $e');
    }
  }

  /// Preloads all local stories into cache
  static Future<void> preloadLocalStories() async {
    await loadAllLocalStories();
  }

  /// Clears the story cache
  static void clearCache() {
    _storyCache.clear();
  }

  /// Gets cached story count
  static int get cachedStoryCount => _storyCache.length;

  /// Gets all cached story IDs
  static List<String> get cachedStoryIds => _storyCache.keys.toList();

  /// Validates that all local story assets exist
  static Future<Map<String, bool>> validateLocalStoryAssets() async {
    final validationResults = <String, bool>{};
    
    for (final storyId in _localStoryIds) {
      try {
        final story = await loadLocalStory(storyId);
        if (story == null) {
          validationResults[storyId] = false;
          continue;
        }

        bool allAssetsValid = true;
        
        // Check cover image
        try {
          final coverImagePath = getLocalAssetPath(storyId, story.coverImage);
          await rootBundle.load(coverImagePath);
        } catch (e) {
          print('Missing cover image for $storyId: ${story.coverImage}');
          allAssetsValid = false;
        }

        // Check scene images
        for (final scene in story.scenes) {
          if (scene.image.isNotEmpty) {
            try {
              final imagePath = getLocalAssetPath(storyId, scene.image);
              await rootBundle.load(imagePath);
            } catch (e) {
              print('Missing scene image for $storyId: ${scene.image}');
              allAssetsValid = false;
            }
          }
        }

        // Check background music
        if (story.setup.backgroundMusic.isNotEmpty) {
          try {
            final musicPath = getLocalAssetPath(storyId, story.setup.backgroundMusic);
            await rootBundle.load(musicPath);
          } catch (e) {
            print('Missing background music for $storyId: ${story.setup.backgroundMusic}');
            // Don't mark as invalid for missing audio - we have fallbacks
          }
        }

        validationResults[storyId] = allAssetsValid;
      } catch (e) {
        print('Error validating assets for $storyId: $e');
        validationResults[storyId] = false;
      }
    }
    
    return validationResults;
  }

  /// Gets story statistics
  static Future<Map<String, dynamic>> getLocalStoryStatistics() async {
    final stories = await loadAllLocalStories();
    final validationResults = await validateLocalStoryAssets();
    
    return {
      'totalLocalStories': _localStoryIds.length,
      'loadedStories': stories.length,
      'cachedStories': _storyCache.length,
      'validStories': validationResults.values.where((v) => v).length,
      'storyDetails': stories.map((story) => {
        'id': story.storyId,
        'title': story.title,
        'ageGroup': story.ageGroup,
        'difficulty': story.difficulty,
        'sceneCount': story.scenes.length,
        'characterCount': story.characters.length,
        'isValid': validationResults[story.storyId] ?? false,
      }).toList(),
    };
  }

  /// Creates demo stories for testing when local stories aren't available
  static List<StoryModel> createDemoStories() {
    return [
      StoryModel(
        storyId: 'demo_story_1',
        ageGroup: '3-5',
        difficulty: 'easy',
        title: 'The Friendly Forest',
        moral: 'Friendship and kindness matter',
        coverImage: 'forest_cover.jpg',
        estimatedTime: '5 minutes',
        setup: const StorySetupModel(
          setting: 'A magical forest',
          tone: 'cheerful',
          context: 'adventure',
          briefIntro: 'Join Mia on a magical adventure in the friendly forest!',
          backgroundMusic: 'forest_music.mp3',
        ),
        narratorProfile: NarratorProfileModel(
          name: 'Story Narrator',
          voice: const VoiceModel(
            name: 'en-US-Standard-A',
            pitch: 1.0,
            rate: 1.0,
            volume: 1.0,
          ),
          defaultVoice: true,
        ),
        characters: [
          CharacterModel(
            name: 'Mia',
            description: 'A curious and kind little girl',
            role: 'Protagonist',
            voice: const VoiceModel(
              name: 'en-US-Wavenet-F',
              pitch: 1.2,
              rate: 1.0,
              volume: 1.0,
            ),
          ),
        ],
        scenes: [
          const SceneModel(
            id: 'scene_1',
            text: 'Once upon a time, in a magical forest, lived a little girl named Mia.',
            speaker: 'narrator',
            emotion: 'happy',
            image: 'forest_scene.jpg',
            pauseDuration: 2000,
            next: 'scene_2',
            progressWeight: 1,
          ),
        ],
        vocabulary: [],
        postStory: PostStoryModel(
          discussion: DiscussionModel(
            text: 'What did you learn about friendship?',
            vocabularyDiscussion: [],
            emotion: 'curious',
          ),
          replayPrompt: const ReplayPromptModel(
            text: 'Would you like to read this story again?',
            emotion: 'excited',
          ),
          parentalDiscussionPrompts: ['Talk about friendship'],
          feedbackSection: const FeedbackSectionModel(
            rating: '5-star',
            comments: 'optional',
          ),
        ),
      ),
    ];
  }
}
