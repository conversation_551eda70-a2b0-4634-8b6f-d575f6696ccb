/// Reflection model for scene outcomes
///
/// Represents reflection questions and prompts that appear after
/// certain story scenes to encourage learning and discussion.
library;

class ReflectionModel {
  /// The reflection text or question
  final String text;
  
  /// The emotional tone for the reflection
  final String emotion;

  const ReflectionModel({
    required this.text,
    required this.emotion,
  });

  /// Creates a ReflectionModel from JSON data
  factory ReflectionModel.fromJson(Map<String, dynamic> json) {
    return ReflectionModel(
      text: json['text'] as String,
      emotion: json['emotion'] as String,
    );
  }

  /// Converts ReflectionModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'emotion': emotion,
    };
  }

  /// Creates a copy of this ReflectionModel with optional parameter overrides
  ReflectionModel copyWith({
    String? text,
    String? emotion,
  }) {
    return ReflectionModel(
      text: text ?? this.text,
      emotion: emotion ?? this.emotion,
    );
  }

  /// Validates that all required fields are present
  bool isValid() {
    return text.isNotEmpty && emotion.isNotEmpty;
  }

  @override
  String toString() {
    return 'ReflectionModel(text: $text, emotion: $emotion)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReflectionModel &&
           other.text == text &&
           other.emotion == emotion;
  }

  @override
  int get hashCode {
    return Object.hash(text, emotion);
  }
}
