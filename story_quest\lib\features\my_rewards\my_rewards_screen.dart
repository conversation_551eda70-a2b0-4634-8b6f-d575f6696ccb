/// My Rewards screen for displaying earned badges and achievements
///
/// Shows user's badge collection with filtering, statistics,
/// and detailed badge information in an engaging interface.
library;

import 'package:flutter/material.dart';
import '../../models/badge_model.dart';
import '../../services/badge_service.dart';
import '../../services/service_locator.dart';
import '../../utils/accessibility_helper.dart';
import '../../widgets/badge_card_widget.dart';

/// My Rewards screen widget
class MyRewardsScreen extends StatefulWidget {
  /// User ID to display rewards for
  final String userId;

  const MyRewardsScreen({
    super.key,
    required this.userId,
  });

  @override
  State<MyRewardsScreen> createState() => _MyRewardsScreenState();
}

class _MyRewardsScreenState extends State<MyRewardsScreen>
    with TickerProviderStateMixin {
  final BadgeService _badgeService = getIt<BadgeService>();
  
  List<BadgeModel> _allBadges = [];
  List<BadgeModel> _filteredBadges = [];
  Map<String, dynamic> _badgeStats = {};
  BadgeType? _selectedFilter;
  bool _isLoading = true;
  String _error = '';

  late AnimationController _headerController;
  late AnimationController _gridController;
  late Animation<double> _headerAnimation;
  late Animation<double> _gridAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize animations
    _headerController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _gridController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _headerAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _headerController,
      curve: Curves.easeOutCubic,
    ));

    _gridAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _gridController,
      curve: Curves.easeOutBack,
    ));

    _loadBadges();
  }

  @override
  void dispose() {
    _headerController.dispose();
    _gridController.dispose();
    super.dispose();
  }

  /// Loads badges and statistics
  Future<void> _loadBadges() async {
    try {
      setState(() {
        _isLoading = true;
        _error = '';
      });

      final badges = await _badgeService.getBadgesForUser(widget.userId);
      final stats = await _badgeService.getBadgeStats(widget.userId);

      // Mark new badges as viewed
      final newBadges = badges.where((badge) => badge.isNew).toList();
      if (newBadges.isNotEmpty) {
        await _badgeService.markBadgesAsViewed(
          widget.userId,
          newBadges.map((badge) => badge.id).toList(),
        );
      }

      setState(() {
        _allBadges = badges;
        _filteredBadges = badges;
        _badgeStats = stats;
        _isLoading = false;
      });

      // Start animations
      _headerController.forward();
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          _gridController.forward();
        }
      });

    } catch (e) {
      setState(() {
        _error = 'Failed to load badges: $e';
        _isLoading = false;
      });
    }
  }

  /// Filters badges by type
  void _filterBadges(BadgeType? type) {
    setState(() {
      _selectedFilter = type;
      if (type == null) {
        _filteredBadges = _allBadges;
      } else {
        _filteredBadges = _allBadges.where((badge) => badge.type == type).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFFFF3E0), // Light orange
              Color(0xFFE8F5E8), // Light green
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              if (_isLoading) _buildLoadingIndicator(),
              if (_error.isNotEmpty) _buildErrorWidget(),
              if (!_isLoading && _error.isEmpty) ...[
                _buildFilterTabs(),
                _buildStatsSection(),
                Expanded(child: _buildBadgeGrid()),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the screen header
  Widget _buildHeader() {
    return AnimatedBuilder(
      animation: _headerAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, -50 * (1 - _headerAnimation.value)),
          child: Opacity(
            opacity: _headerAnimation.value,
            child: Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  // Back button
                  AccessibilityHelper.createAccessibleButton(
                    child: Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.9),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.arrow_back,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                    onPressed: () => Navigator.of(context).pop(),
                    semanticLabel: 'Go back',
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // Title and trophy icon
                  Expanded(
                    child: Row(
                      children: [
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFCA28),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: const Color(0xFFFFCA28).withValues(alpha: 0.3),
                                blurRadius: 12,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.emoji_events,
                            color: Colors.white,
                            size: 28,
                          ),
                        ),
                        
                        const SizedBox(width: 12),
                        
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AccessibilityHelper.createAccessibleText(
                              'My Rewards',
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF2E7D32),
                              ),
                            ),
                            AccessibilityHelper.createAccessibleText(
                              '${_badgeStats['total'] ?? 0} badges earned',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Color(0xFF757575),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Builds loading indicator
  Widget _buildLoadingIndicator() {
    return const Expanded(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            ),
            SizedBox(height: 16),
            Text(
              'Loading your rewards...',
              style: TextStyle(
                fontSize: 16,
                color: Color(0xFF757575),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds error widget
  Widget _buildErrorWidget() {
    return Expanded(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Color(0xFFE57373),
            ),
            const SizedBox(height: 16),
            Text(
              _error,
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF757575),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadBadges,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4CAF50),
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds filter tabs
  Widget _buildFilterTabs() {
    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip('All', null),
          ...BadgeType.values.map((type) => _buildFilterChip(type.displayName, type)),
        ],
      ),
    );
  }

  /// Builds individual filter chip
  Widget _buildFilterChip(String label, BadgeType? type) {
    final isSelected = _selectedFilter == type;
    final count = type == null 
        ? _allBadges.length 
        : _allBadges.where((badge) => badge.type == type).length;
    
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text('$label ($count)'),
        selected: isSelected,
        onSelected: (_) => _filterBadges(type),
        backgroundColor: Colors.white.withValues(alpha: 0.8),
        selectedColor: const Color(0xFF4CAF50),
        labelStyle: TextStyle(
          color: isSelected ? Colors.white : const Color(0xFF2E7D32),
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
    );
  }

  /// Builds statistics section
  Widget _buildStatsSection() {
    if (_badgeStats.isEmpty) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('Total', _badgeStats['total'].toString(), Icons.emoji_events),
          _buildStatItem('New', _badgeStats['new'].toString(), Icons.fiber_new),
          _buildStatItem('Types', _badgeStats['byType'].length.toString(), Icons.category),
        ],
      ),
    );
  }

  /// Builds individual stat item
  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: const Color(0xFF4CAF50), size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2E7D32),
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF757575),
          ),
        ),
      ],
    );
  }

  /// Builds the badge grid
  Widget _buildBadgeGrid() {
    if (_filteredBadges.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.emoji_events_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _selectedFilter == null 
                  ? 'No badges earned yet!\nStart reading stories to earn rewards.'
                  : 'No ${_selectedFilter!.displayName.toLowerCase()} badges yet.',
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF757575),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return AnimatedBuilder(
      animation: _gridAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _gridAnimation.value,
          child: Opacity(
            opacity: _gridAnimation.value,
            child: GridView.builder(
              padding: const EdgeInsets.all(20),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.8,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: _filteredBadges.length,
              itemBuilder: (context, index) {
                return BadgeCardWidget(
                  badge: _filteredBadges[index],
                  animationDelay: Duration(milliseconds: index * 100),
                );
              },
            ),
          ),
        );
      },
    );
  }
}
