/// Unit tests for TTS service implementations
/// 
/// Tests TTS functionality including voice configuration,
/// speech operations, and error handling.
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:story_quest/models/voice_model.dart';
import 'package:story_quest/services/flutter_tts_service.dart';

// Generate mocks
@GenerateMocks([FlutterTts])
import 'tts_service_test.mocks.dart';

void main() {
  group('FlutterTTSService Tests', () {
    late FlutterTTSService ttsService;
    late MockFlutterTts mockFlutterTts;

    setUp(() {
      mockFlutterTts = MockFlutterTts();
      ttsService = FlutterTTSService();
      // Note: In a real implementation, we'd need to inject the mock
      // For now, these tests demonstrate the testing approach
    });

    test('should speak text with default voice', () async {
      // Arrange
      const testText = 'Hello, this is a test';
      
      // Act & Assert
      expect(() => ttsService.speak(testText), returnsNormally);
    });

    test('should speak text with custom voice configuration', () async {
      // Arrange
      const testText = 'Hello with custom voice';
      const customVoice = VoiceModel(
        name: 'en-US-Wavenet-A',
        pitch: 1.2,
        rate: 0.8,
        volume: 0.9,
      );
      
      // Act & Assert
      expect(() => ttsService.speak(testText, voiceConfig: customVoice), returnsNormally);
    });

    test('should stop speech', () async {
      // Act & Assert
      expect(() => ttsService.stop(), returnsNormally);
    });

    test('should set language', () async {
      // Arrange
      const language = 'en-GB';
      
      // Act & Assert
      expect(() => ttsService.setLanguage(language), returnsNormally);
    });

    test('should set voice configuration', () async {
      // Arrange
      const voice = VoiceModel(
        name: 'en-US-Standard-A',
        pitch: 1.0,
        rate: 1.0,
        volume: 1.0,
      );
      
      // Act & Assert
      expect(() => ttsService.setVoice(voice), returnsNormally);
    });

    test('should validate speech rate bounds', () async {
      // Act & Assert
      expect(() => ttsService.setSpeechRate(0.05), throwsArgumentError); // Too low
      expect(() => ttsService.setSpeechRate(5.0), throwsArgumentError);  // Too high
      expect(() => ttsService.setSpeechRate(1.0), returnsNormally);      // Valid
    });

    test('should validate pitch bounds', () async {
      // Act & Assert
      expect(() => ttsService.setPitch(0.3), throwsArgumentError); // Too low
      expect(() => ttsService.setPitch(3.0), throwsArgumentError); // Too high
      expect(() => ttsService.setPitch(1.0), returnsNormally);     // Valid
    });

    test('should validate volume bounds', () async {
      // Act & Assert
      expect(() => ttsService.setVolume(-0.1), throwsArgumentError); // Too low
      expect(() => ttsService.setVolume(1.5), throwsArgumentError);  // Too high
      expect(() => ttsService.setVolume(0.5), returnsNormally);      // Valid
    });

    test('should handle invalid voice configuration', () async {
      // Arrange
      const invalidVoice = VoiceModel(
        name: '',
        pitch: 5.0, // Invalid
        rate: 0.05, // Invalid
        volume: 1.5, // Invalid
      );
      
      // Act & Assert
      expect(() => ttsService.setVoice(invalidVoice), throwsArgumentError);
    });

    test('should pause and resume speech', () async {
      // Act & Assert
      expect(() => ttsService.pause(), returnsNormally);
      expect(() => ttsService.resume(), returnsNormally);
    });

    test('should dispose properly', () async {
      // Act & Assert
      expect(() => ttsService.dispose(), returnsNormally);
    });

    test('should get available voices', () async {
      // Act
      final voices = await ttsService.getAvailableVoices();
      
      // Assert
      expect(voices, isA<List<String>>());
    });

    test('should get available languages', () async {
      // Act
      final languages = await ttsService.getAvailableLanguages();
      
      // Assert
      expect(languages, isA<List<String>>());
    });

    test('should check speaking state', () async {
      // Act
      final isSpeaking = await ttsService.isSpeaking;
      
      // Assert
      expect(isSpeaking, isA<bool>());
    });
  });

  group('Voice Configuration Tests', () {
    test('should apply voice configuration correctly', () {
      // Arrange
      const voice1 = VoiceModel(
        name: 'voice1',
        pitch: 1.0,
        rate: 1.0,
        volume: 1.0,
      );

      const voice2 = VoiceModel(
        name: 'voice2',
        pitch: 1.5,
        rate: 0.8,
        volume: 0.7,
      );

      // Act
      final updatedVoice = voice1.copyWith(
        pitch: voice2.pitch,
        rate: voice2.rate,
      );

      // Assert
      expect(updatedVoice.name, 'voice1'); // Unchanged
      expect(updatedVoice.pitch, 1.5);     // Updated
      expect(updatedVoice.rate, 0.8);      // Updated
      expect(updatedVoice.volume, 1.0);    // Unchanged
    });

    test('should validate voice equality', () {
      // Arrange
      const voice1 = VoiceModel(
        name: 'test-voice',
        pitch: 1.0,
        rate: 1.0,
        volume: 1.0,
      );

      const voice2 = VoiceModel(
        name: 'test-voice',
        pitch: 1.0,
        rate: 1.0,
        volume: 1.0,
      );

      const voice3 = VoiceModel(
        name: 'different-voice',
        pitch: 1.0,
        rate: 1.0,
        volume: 1.0,
      );

      // Assert
      expect(voice1, equals(voice2));
      expect(voice1, isNot(equals(voice3)));
    });

    test('should generate consistent hash codes', () {
      // Arrange
      const voice1 = VoiceModel(
        name: 'test-voice',
        pitch: 1.0,
        rate: 1.0,
        volume: 1.0,
      );

      const voice2 = VoiceModel(
        name: 'test-voice',
        pitch: 1.0,
        rate: 1.0,
        volume: 1.0,
      );

      // Assert
      expect(voice1.hashCode, equals(voice2.hashCode));
    });
  });
}
