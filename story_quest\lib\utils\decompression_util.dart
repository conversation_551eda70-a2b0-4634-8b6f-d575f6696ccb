/// Decompression utility for story archive files
/// 
/// Handles extraction of story zip files and manages decompressed assets
/// with proper error handling and cleanup.
library;

import 'dart:io';
import 'package:archive/archive.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// Utility class for decompressing story archives
class DecompressionUtil {
  /// Decompresses a story zip file to the local storage
  /// 
  /// [zipFilePath] - Path to the zip file to decompress
  /// [storyId] - Unique identifier for the story
  /// Returns the path to the decompressed story directory
  static Future<String> decompressStory(String zipFilePath, String storyId) async {
    try {
      // Read the zip file
      final zipFile = File(zipFilePath);
      if (!await zipFile.exists()) {
        throw FileSystemException('Zip file not found', zipFilePath);
      }

      final bytes = await zipFile.readAsBytes();
      final archive = ZipDecoder().decodeBytes(bytes);

      // Get the target directory for decompression
      final targetDir = await _getStoryDirectory(storyId);
      
      // Clean up existing directory if it exists
      if (await targetDir.exists()) {
        await targetDir.delete(recursive: true);
      }
      
      // Create the target directory
      await targetDir.create(recursive: true);

      // Extract all files
      int extractedFiles = 0;
      for (final file in archive) {
        if (file.isFile) {
          await _extractFile(file, targetDir.path);
          extractedFiles++;
        } else {
          // Create directory
          final dirPath = path.join(targetDir.path, file.name);
          await Directory(dirPath).create(recursive: true);
        }
      }

      print('Successfully extracted $extractedFiles files for story $storyId');
      return targetDir.path;
    } catch (e) {
      print('Failed to decompress story $storyId: $e');
      rethrow;
    }
  }

  /// Extracts a single file from the archive
  static Future<void> _extractFile(ArchiveFile file, String targetDirPath) async {
    final filePath = path.join(targetDirPath, file.name);
    final outputFile = File(filePath);
    
    // Ensure parent directory exists
    await outputFile.parent.create(recursive: true);
    
    // Write file content
    await outputFile.writeAsBytes(file.content as List<int>);
  }

  /// Gets the directory for storing decompressed story files
  static Future<Directory> _getStoryDirectory(String storyId) async {
    final appDir = await getApplicationDocumentsDirectory();
    return Directory(path.join(appDir.path, 'stories', 'extracted', storyId));
  }

  /// Validates that a decompressed story has the required structure
  /// 
  /// [storyPath] - Path to the decompressed story directory
  /// Returns true if the story structure is valid
  static Future<bool> validateStoryStructure(String storyPath) async {
    try {
      final storyDir = Directory(storyPath);
      if (!await storyDir.exists()) {
        return false;
      }

      // Check for required files and directories
      final storyJsonFile = File(path.join(storyPath, 'story.json'));
      final imagesDir = Directory(path.join(storyPath, 'images'));
      final assetsDir = Directory(path.join(storyPath, 'assets'));

      final hasStoryJson = await storyJsonFile.exists();
      final hasImagesDir = await imagesDir.exists();
      final hasAssetsDir = await assetsDir.exists();

      if (!hasStoryJson) {
        print('Missing story.json file');
        return false;
      }

      if (!hasImagesDir) {
        print('Missing images directory');
        return false;
      }

      if (!hasAssetsDir) {
        print('Missing assets directory');
        return false;
      }

      return true;
    } catch (e) {
      print('Error validating story structure: $e');
      return false;
    }
  }

  /// Gets the path to a decompressed story directory
  /// 
  /// [storyId] - Unique identifier for the story
  /// Returns the path if the story exists, null otherwise
  static Future<String?> getDecompressedStoryPath(String storyId) async {
    final storyDir = await _getStoryDirectory(storyId);
    if (await storyDir.exists()) {
      return storyDir.path;
    }
    return null;
  }

  /// Checks if a story is already decompressed
  /// 
  /// [storyId] - Unique identifier for the story
  /// Returns true if the story is decompressed and valid
  static Future<bool> isStoryDecompressed(String storyId) async {
    final storyPath = await getDecompressedStoryPath(storyId);
    if (storyPath == null) {
      return false;
    }
    return await validateStoryStructure(storyPath);
  }

  /// Deletes a decompressed story directory
  /// 
  /// [storyId] - Unique identifier for the story
  static Future<void> deleteDecompressedStory(String storyId) async {
    try {
      final storyDir = await _getStoryDirectory(storyId);
      if (await storyDir.exists()) {
        await storyDir.delete(recursive: true);
        print('Deleted decompressed story: $storyId');
      }
    } catch (e) {
      print('Failed to delete decompressed story $storyId: $e');
      rethrow;
    }
  }

  /// Gets the total size of all decompressed stories
  static Future<int> getDecompressedStoriesSize() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final extractedDir = Directory(path.join(appDir.path, 'stories', 'extracted'));
      
      if (!await extractedDir.exists()) {
        return 0;
      }

      int totalSize = 0;
      await for (final entity in extractedDir.list(recursive: true)) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
        }
      }

      return totalSize;
    } catch (e) {
      print('Failed to calculate decompressed stories size: $e');
      return 0;
    }
  }

  /// Cleans up all decompressed stories
  static Future<void> cleanupAllDecompressedStories() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final extractedDir = Directory(path.join(appDir.path, 'stories', 'extracted'));
      
      if (await extractedDir.exists()) {
        await extractedDir.delete(recursive: true);
        print('Cleaned up all decompressed stories');
      }
    } catch (e) {
      print('Failed to cleanup decompressed stories: $e');
      rethrow;
    }
  }

  /// Lists all decompressed story IDs
  static Future<List<String>> listDecompressedStories() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final extractedDir = Directory(path.join(appDir.path, 'stories', 'extracted'));
      
      if (!await extractedDir.exists()) {
        return [];
      }

      final storyIds = <String>[];
      await for (final entity in extractedDir.list()) {
        if (entity is Directory) {
          final storyId = path.basename(entity.path);
          if (await validateStoryStructure(entity.path)) {
            storyIds.add(storyId);
          }
        }
      }

      return storyIds;
    } catch (e) {
      print('Failed to list decompressed stories: $e');
      return [];
    }
  }
}
