                        -HC:\flutter\packages\flutter_tools\gradle\src\main\scripts
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-D<PERSON>DROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=D:\Install\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=D:\Install\Android\Sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=D:\Install\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\Install\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\AliM Studio\App 01\App\story_quest\build\app\intermediates\cxx\Debug\2x18z2m4\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\AliM Studio\App 01\App\story_quest\build\app\intermediates\cxx\Debug\2x18z2m4\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-BD:\AliM Studio\App 01\App\story_quest\build\.cxx\Debug\2x18z2m4\arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2