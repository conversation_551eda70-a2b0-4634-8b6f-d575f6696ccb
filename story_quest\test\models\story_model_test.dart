/// Unit tests for StoryModel and related models
/// 
/// Tests JSON serialization/deserialization and validation logic
/// for all story-related data models.
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:story_quest/models/index.dart';

void main() {
  group('VoiceModel Tests', () {
    test('should create VoiceModel from JSON', () {
      final json = {
        'name': 'en-US-Wavenet-A',
        'pitch': 0.9,
        'rate': 0.95,
        'volume': 1.0,
      };

      final voice = VoiceModel.fromJson(json);

      expect(voice.name, 'en-US-Wavenet-A');
      expect(voice.pitch, 0.9);
      expect(voice.rate, 0.95);
      expect(voice.volume, 1.0);
    });

    test('should convert VoiceModel to JSON', () {
      const voice = VoiceModel(
        name: 'en-US-Wavenet-A',
        pitch: 0.9,
        rate: 0.95,
        volume: 1.0,
      );

      final json = voice.toJson();

      expect(json['name'], 'en-US-Wavenet-A');
      expect(json['pitch'], 0.9);
      expect(json['rate'], 0.95);
      expect(json['volume'], 1.0);
    });

    test('should validate voice parameters', () {
      const validVoice = VoiceModel(
        name: 'test-voice',
        pitch: 1.0,
        rate: 1.0,
        volume: 0.5,
      );

      const invalidVoice = VoiceModel(
        name: '',
        pitch: 5.0, // Invalid: > 2.0
        rate: 0.05, // Invalid: < 0.1
        volume: 1.5, // Invalid: > 1.0
      );

      expect(validVoice.isValid(), true);
      expect(invalidVoice.isValid(), false);
    });
  });

  group('CharacterModel Tests', () {
    test('should create CharacterModel from JSON', () {
      final json = {
        'name': 'Mia',
        'description': 'A curious girl who loves to play',
        'role': 'Protagonist',
        'voice': {
          'name': 'en-GB-Standard-A',
          'pitch': 1.3,
          'rate': 1.2,
          'volume': 0.9,
        },
      };

      final character = CharacterModel.fromJson(json);

      expect(character.name, 'Mia');
      expect(character.description, 'A curious girl who loves to play');
      expect(character.role, 'Protagonist');
      expect(character.voice.name, 'en-GB-Standard-A');
    });

    test('should validate character data', () {
      final validCharacter = CharacterModel(
        name: 'Test Character',
        description: 'Test description',
        role: 'Test role',
        voice: const VoiceModel(
          name: 'test-voice',
          pitch: 1.0,
          rate: 1.0,
          volume: 1.0,
        ),
      );

      final invalidCharacter = CharacterModel(
        name: '', // Invalid: empty name
        description: 'Test description',
        role: 'Test role',
        voice: const VoiceModel(
          name: 'test-voice',
          pitch: 1.0,
          rate: 1.0,
          volume: 1.0,
        ),
      );

      expect(validCharacter.isValid(), true);
      expect(invalidCharacter.isValid(), false);
    });
  });

  group('ChoiceModel Tests', () {
    test('should create ChoiceModel from JSON', () {
      final json = {
        'option': 'Help Alex find the toy',
        'visual': 'helping_hand_icon.jpg',
        'next': 'scene_4a',
      };

      final choice = ChoiceModel.fromJson(json);

      expect(choice.option, 'Help Alex find the toy');
      expect(choice.visual, 'helping_hand_icon.jpg');
      expect(choice.next, 'scene_4a');
    });

    test('should validate choice data', () {
      const validChoice = ChoiceModel(
        option: 'Test option',
        visual: 'test.jpg',
        next: 'next_scene',
      );

      const invalidChoice = ChoiceModel(
        option: '', // Invalid: empty option
        visual: 'test.jpg',
        next: 'next_scene',
      );

      expect(validChoice.isValid(), true);
      expect(invalidChoice.isValid(), false);
    });
  });

  group('SceneModel Tests', () {
    test('should create SceneModel from JSON', () {
      final json = {
        'id': 'scene_1',
        'text': 'Test scene text',
        'speaker': 'narrator',
        'emotion': 'happy',
        'image': 'test.jpg',
        'pause_duration': 2000,
        'next': 'scene_2',
        'progress_weight': 1,
        'interactive_elements': [],
      };

      final scene = SceneModel.fromJson(json);

      expect(scene.id, 'scene_1');
      expect(scene.text, 'Test scene text');
      expect(scene.speaker, 'narrator');
      expect(scene.next, 'scene_2');
      expect(scene.hasChoices, false);
    });

    test('should handle choice scenes', () {
      final json = {
        'id': 'choice_scene',
        'text': 'What should you do?',
        'speaker': 'narrator',
        'emotion': 'curious',
        'image': 'choice.jpg',
        'pause_duration': 0,
        'choices': [
          {
            'option': 'Option 1',
            'visual': 'option1.jpg',
            'next': 'scene_a',
          },
          {
            'option': 'Option 2',
            'visual': 'option2.jpg',
            'next': 'scene_b',
          },
        ],
        'progress_weight': 2,
        'interactive_elements': [],
      };

      final scene = SceneModel.fromJson(json);

      expect(scene.hasChoices, true);
      expect(scene.choices.length, 2);
      expect(scene.next, null);
    });

    test('should validate scene structure', () {
      const validScene = SceneModel(
        id: 'test_scene',
        text: 'Test text',
        speaker: 'narrator',
        emotion: 'happy',
        image: 'test.jpg',
        pauseDuration: 1000,
        next: 'next_scene',
        progressWeight: 1,
      );

      const invalidScene = SceneModel(
        id: '', // Invalid: empty ID
        text: 'Test text',
        speaker: 'narrator',
        emotion: 'happy',
        image: 'test.jpg',
        pauseDuration: 1000,
        progressWeight: -1, // Invalid: negative weight
      );

      expect(validScene.isValid(), true);
      expect(invalidScene.isValid(), false);
    });
  });

  group('StoryModel Tests', () {
    test('should validate story structure', () {
      final validStory = StoryModel(
        storyId: 'test_story',
        ageGroup: '3-5',
        difficulty: 'easy',
        title: 'Test Story',
        moral: 'Test moral',
        coverImage: 'cover.jpg',
        estimatedTime: '5 minutes',
        setup: const StorySetupModel(
          setting: 'Test setting',
          tone: 'Test tone',
          context: 'Test context',
          briefIntro: 'Test intro',
          backgroundMusic: 'music.mp3',
        ),
        narratorProfile: NarratorProfileModel(
          name: 'Test Narrator',
          voice: const VoiceModel(
            name: 'test-voice',
            pitch: 1.0,
            rate: 1.0,
            volume: 1.0,
          ),
          defaultVoice: true,
        ),
        characters: [],
        scenes: [
          const SceneModel(
            id: 'scene_1',
            text: 'Test scene',
            speaker: 'narrator',
            emotion: 'happy',
            image: 'test.jpg',
            pauseDuration: 1000,
            progressWeight: 1,
          ),
        ],
        vocabulary: [],
        postStory: PostStoryModel(
          discussion: DiscussionModel(
            text: 'Test discussion',
            vocabularyDiscussion: [],
            emotion: 'curious',
          ),
          replayPrompt: const ReplayPromptModel(
            text: 'Test replay',
            emotion: 'excited',
          ),
          parentalDiscussionPrompts: ['Test prompt'],
          feedbackSection: const FeedbackSectionModel(
            rating: '5-star',
            comments: 'optional',
          ),
        ),
      );

      expect(validStory.isValid(), true);
      expect(validStory.firstScene?.id, 'scene_1');
      expect(validStory.totalProgressWeight, 1);
    });

    test('should find scenes by ID', () {
      final story = StoryModel(
        storyId: 'test_story',
        ageGroup: '3-5',
        difficulty: 'easy',
        title: 'Test Story',
        moral: 'Test moral',
        coverImage: 'cover.jpg',
        estimatedTime: '5 minutes',
        setup: const StorySetupModel(
          setting: 'Test setting',
          tone: 'Test tone',
          context: 'Test context',
          briefIntro: 'Test intro',
          backgroundMusic: 'music.mp3',
        ),
        narratorProfile: NarratorProfileModel(
          name: 'Test Narrator',
          voice: const VoiceModel(
            name: 'test-voice',
            pitch: 1.0,
            rate: 1.0,
            volume: 1.0,
          ),
          defaultVoice: true,
        ),
        characters: [],
        scenes: [
          const SceneModel(
            id: 'scene_1',
            text: 'First scene',
            speaker: 'narrator',
            emotion: 'happy',
            image: 'test1.jpg',
            pauseDuration: 1000,
            progressWeight: 1,
          ),
          const SceneModel(
            id: 'scene_2',
            text: 'Second scene',
            speaker: 'narrator',
            emotion: 'happy',
            image: 'test2.jpg',
            pauseDuration: 1000,
            progressWeight: 1,
          ),
        ],
        vocabulary: [],
        postStory: PostStoryModel(
          discussion: DiscussionModel(
            text: 'Test discussion',
            vocabularyDiscussion: [],
            emotion: 'curious',
          ),
          replayPrompt: const ReplayPromptModel(
            text: 'Test replay',
            emotion: 'excited',
          ),
          parentalDiscussionPrompts: ['Test prompt'],
          feedbackSection: const FeedbackSectionModel(
            rating: '5-star',
            comments: 'optional',
          ),
        ),
      );

      final scene1 = story.getSceneById('scene_1');
      final scene2 = story.getSceneById('scene_2');
      final nonExistent = story.getSceneById('scene_999');

      expect(scene1?.text, 'First scene');
      expect(scene2?.text, 'Second scene');
      expect(nonExistent, null);
    });
  });
}
