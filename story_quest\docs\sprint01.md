Tasks for AI Agent
Initialize Flutter Project:
Update pubspec.yaml by adding dependencies: firebase_storage, archive, get_it, and any others necessary.
Configure Firebase using the provided google-services.json.
Set Up Folder Structure:
Add barrel files (index.dart) in lib/features, lib/models, lib/services, lib/utils, and lib/widgets for easier imports.
Implement Data Models:





Create story_model.dart, scene_model.dart, and choice_model.dart in lib/models.



Base models on the provided story.json structure.



Ensure models support JSON serialization/deserialization.



Develop API Service:





Create firebase_service.dart in lib/services.



Implement methods to fetch story zip files from Firebase Storage and cache them locally.



Set Up Local Database:





Use sqflite to create a local database in lib/services.



Define tables for stories, scenes, and assets to cache decompressed story data.



Implement Decompression Logic:





Create a utility in lib/utils using the archive package to decompress zip files.



Store decompressed assets locally and map them to their respective stories.



Create TTS Service:





Define a tts_service.dart interface in lib/services with methods: speak, stop, setLanguage.



Implement flutter_tts_service.dart in lib/services that adheres to this interface.



Use get_it for dependency injection of the TTS service.



Write Unit Tests:





Create tests in the test/ folder:





Test data model serialization/deserialization.



Test firebase_service.dart for successful zip downloads.



Test TTS service for basic functionality (e.g., speak and stop).



Set Up Automated Documentation Updates:





Create a script (e.g., update_docs.sh) to append changes to docs/script_changes.md after each run or build.



Include details like script name, modification date, and a summary of changes.



Automate this script using a post-build hook or integrate it into the CI/CD pipeline.



Add Robust Error Handling:





Wrap network calls and asset loading operations in try-catch blocks.



Implement retry logic for network failures (e.g., retry up to 3 times with a delay).



Provide user-friendly fallbacks:





Use the default image (story_quest\assets\default\default_image.png) when a scene lacks a specific image.



Use the default music (story_quest\assets\default\happy-outro-8110.mp3) when a scene lacks specific audio.



Display informative messages using ScaffoldMessenger for errors.



Optimize Asset Loading:





Implement lazy loading by deferring asset loading until needed.



Use a caching mechanism (e.g., in-memory cache or sqflite) to store loaded assets for quick access.



Ensure assets are only loaded once and reused across the app.



Enhance UI Accessibility:





Use Flutter's Semantics widget to add labels for screen readers.



Ensure high contrast for text and backgrounds.



Provide large touch targets (at least 48x48 dp) for buttons and interactive elements.



Test the app with screen readers like TalkBack (Android) or VoiceOver (iOS).



Additional Instructions for Asset Management





Default Assets:





Use the default image (story_quest\assets\default\default_image.png) as a fallback when a scene's image is missing or fails to load.



Use the default music (story_quest\assets\default\happy-outro-8110.mp3) as a fallback when a scene's audio is missing or fails to load.



Story Structure:





Each story is stored in a folder named after its story ID (e.g., <storyid>).



Inside each story folder:





images/ contains image assets (e.g., scene images).



assets/ contains other assets like audio files.



story.json defines the story's structure, including references to assets in images/ and assets/.



Asset References:





When parsing story.json, ensure that asset paths are correctly resolved relative to the story folder.



For example, if story.json references "image": "scene1.png", the app should load it from <storyid>/images/scene1.png.



Error Handling for Assets:





If an asset fails to load (e.g., file not found), fallback to the default image or music.



Log the error and display a user-friendly message (e.g., "Asset not found, using default").



Guidelines





Follow modular design principles and naming conventions in docs/guidelines.md.



Use snake_case for all file names (e.g., story_model.dart).



Document all code with clear, concise comments.



Deliverables





A functional app skeleton with Firebase integration, local caching, TTS setup, automated documentation, error handling, optimized asset loading, and accessible UI.



Unit tests for data models, API service, and TTS service.