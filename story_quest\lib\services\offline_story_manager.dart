/// Offline Story Manager service
/// 
/// Manages offline story caching, download progress tracking,
/// and local story availability for seamless offline playback.
library;

import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../models/story_model.dart';
import '../utils/decompression_util.dart';
import '../utils/asset_manager.dart';
import 'firebase_service.dart';
import 'database_service.dart';

/// Download status for stories
enum DownloadStatus {
  notDownloaded,
  downloading,
  downloaded,
  failed,
}

/// Download progress information
class DownloadProgress {
  final String storyId;
  final DownloadStatus status;
  final double progress;
  final String? error;

  const DownloadProgress({
    required this.storyId,
    required this.status,
    this.progress = 0.0,
    this.error,
  });
}

/// Offline story manager for local caching and playback
class OfflineStoryManager {
  final FirebaseService _firebaseService;
  final DatabaseService _databaseService;
  final Map<String, DownloadProgress> _downloadProgress = {};

  OfflineStoryManager({
    required FirebaseService firebaseService,
    required DatabaseService databaseService,
  }) : _firebaseService = firebaseService,
       _databaseService = databaseService;

  /// Downloads a story for offline use
  Future<bool> downloadStory(
    StoryModel story, {
    Function(DownloadProgress)? onProgress,
  }) async {
    final storyId = story.storyId;
    
    try {
      // Update progress to downloading
      _updateProgress(storyId, DownloadStatus.downloading, 0.0);
      onProgress?.call(_downloadProgress[storyId]!);

      // Download story zip from Firebase
      final zipPath = await _firebaseService.downloadStory(
        storyId,
        onProgress: (received, total) {
          final progress = total > 0 ? received / total : 0.0;
          _updateProgress(storyId, DownloadStatus.downloading, progress * 0.5);
          onProgress?.call(_downloadProgress[storyId]!);
        },
      );

      // Decompress story assets
      _updateProgress(storyId, DownloadStatus.downloading, 0.6);
      onProgress?.call(_downloadProgress[storyId]!);

      final extractedPath = await DecompressionUtil.decompressStory(zipPath, storyId);
      
      // Validate story structure
      _updateProgress(storyId, DownloadStatus.downloading, 0.8);
      onProgress?.call(_downloadProgress[storyId]!);

      final isValid = await DecompressionUtil.validateStoryStructure(extractedPath);
      if (!isValid) {
        throw Exception('Invalid story structure after extraction');
      }

      // Load and validate story.json
      final storyJsonPath = '$extractedPath/story.json';
      final storyJsonFile = File(storyJsonPath);
      
      if (!await storyJsonFile.exists()) {
        throw Exception('story.json not found in extracted files');
      }

      final storyJsonContent = await storyJsonFile.readAsString();
      final storyData = jsonDecode(storyJsonContent) as Map<String, dynamic>;
      final loadedStory = StoryModel.fromJson(storyData);

      // Validate story data matches
      if (loadedStory.storyId != storyId) {
        throw Exception('Story ID mismatch in downloaded content');
      }

      // Cache story in database
      _updateProgress(storyId, DownloadStatus.downloading, 0.9);
      onProgress?.call(_downloadProgress[storyId]!);

      await _databaseService.saveStory(loadedStory);

      // Cache asset paths
      await _cacheAssetPaths(storyId, extractedPath);

      // Mark as downloaded
      _updateProgress(storyId, DownloadStatus.downloaded, 1.0);
      onProgress?.call(_downloadProgress[storyId]!);

      return true;
    } catch (e) {
      _updateProgress(storyId, DownloadStatus.failed, 0.0, e.toString());
      onProgress?.call(_downloadProgress[storyId]!);
      
      // Clean up failed download
      await _cleanupFailedDownload(storyId);
      
      return false;
    }
  }

  /// Checks if a story is available offline
  Future<bool> isStoryAvailableOffline(String storyId) async {
    try {
      // Check database
      final story = await _databaseService.getStory(storyId);
      if (story == null) return false;

      // Check extracted files
      final isDecompressed = await DecompressionUtil.isStoryDecompressed(storyId);
      if (!isDecompressed) return false;

      // Validate structure
      final extractedPath = await DecompressionUtil.getDecompressedStoryPath(storyId);
      if (extractedPath == null) return false;

      return await DecompressionUtil.validateStoryStructure(extractedPath);
    } catch (e) {
      return false;
    }
  }

  /// Gets offline story data
  Future<StoryModel?> getOfflineStory(String storyId) async {
    try {
      final isAvailable = await isStoryAvailableOffline(storyId);
      if (!isAvailable) return null;

      return await _databaseService.getStory(storyId);
    } catch (e) {
      return null;
    }
  }

  /// Gets asset path for offline story
  Future<String> getOfflineAssetPath(
    String storyId,
    String assetPath,
    String assetType,
  ) async {
    try {
      final extractedPath = await DecompressionUtil.getDecompressedStoryPath(storyId);
      if (extractedPath == null) {
        return await AssetManager.resolveAssetPath(storyId, assetPath, assetType, '');
      }

      return await AssetManager.resolveAssetPath(storyId, assetPath, assetType, extractedPath);
    } catch (e) {
      // Return default asset on error
      return assetType.toLowerCase() == 'image' 
          ? AssetManager.defaultImagePath
          : AssetManager.defaultAudioPath;
    }
  }

  /// Deletes offline story data
  Future<bool> deleteOfflineStory(String storyId) async {
    try {
      // Delete from database
      await _databaseService.deleteStory(storyId);

      // Delete extracted files
      await DecompressionUtil.deleteDecompressedStory(storyId);

      // Delete zip file
      await _firebaseService.deleteLocalStory(storyId);

      // Update progress
      _downloadProgress.remove(storyId);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Gets list of downloaded stories
  Future<List<String>> getDownloadedStoryIds() async {
    try {
      final stories = await _databaseService.getAllStories();
      final downloadedIds = <String>[];

      for (final storyData in stories) {
        final storyId = storyData['story_id'] as String;
        if (await isStoryAvailableOffline(storyId)) {
          downloadedIds.add(storyId);
        }
      }

      return downloadedIds;
    } catch (e) {
      return [];
    }
  }

  /// Gets download progress for a story
  DownloadProgress? getDownloadProgress(String storyId) {
    return _downloadProgress[storyId];
  }

  /// Gets total offline storage size
  Future<int> getOfflineStorageSize() async {
    try {
      final cacheSize = await _firebaseService.getLocalCacheSize();
      final extractedSize = await DecompressionUtil.getDecompressedStoriesSize();
      return cacheSize + extractedSize;
    } catch (e) {
      return 0;
    }
  }

  /// Clears all offline data
  Future<void> clearAllOfflineData() async {
    try {
      await _databaseService.clearAllData();
      await DecompressionUtil.cleanupAllDecompressedStories();
      await _firebaseService.clearLocalCache();
      _downloadProgress.clear();
    } catch (e) {
      // Log error but don't throw
      print('Error clearing offline data: $e');
    }
  }

  /// Validates offline story integrity
  Future<bool> validateOfflineStory(String storyId) async {
    try {
      // Check database entry
      final story = await _databaseService.getStory(storyId);
      if (story == null) return false;

      // Check extracted files
      final extractedPath = await DecompressionUtil.getDecompressedStoryPath(storyId);
      if (extractedPath == null) return false;

      // Validate structure
      final isValid = await DecompressionUtil.validateStoryStructure(extractedPath);
      if (!isValid) return false;

      // Validate story.json matches database
      final storyJsonPath = '$extractedPath/story.json';
      final storyJsonFile = File(storyJsonPath);
      
      if (!await storyJsonFile.exists()) return false;

      final storyJsonContent = await storyJsonFile.readAsString();
      final storyData = jsonDecode(storyJsonContent) as Map<String, dynamic>;
      final fileStory = StoryModel.fromJson(storyData);

      return fileStory.storyId == story.storyId && fileStory.title == story.title;
    } catch (e) {
      return false;
    }
  }

  /// Updates download progress
  void _updateProgress(
    String storyId,
    DownloadStatus status,
    double progress, [
    String? error,
  ]) {
    _downloadProgress[storyId] = DownloadProgress(
      storyId: storyId,
      status: status,
      progress: progress,
      error: error,
    );
  }

  /// Caches asset paths in database
  Future<void> _cacheAssetPaths(String storyId, String extractedPath) async {
    try {
      // Get all images
      final images = await AssetManager.getStoryImages(extractedPath);
      for (final imagePath in images) {
        // Cache image path info - could be stored in database if needed
      }

      // Get all audio files
      final audioFiles = await AssetManager.getStoryAudio(extractedPath);
      for (final audioPath in audioFiles) {
        // Cache audio path info - could be stored in database if needed
      }
    } catch (e) {
      // Non-critical error, continue
      print('Warning: Failed to cache asset paths for $storyId: $e');
    }
  }

  /// Cleans up failed download
  Future<void> _cleanupFailedDownload(String storyId) async {
    try {
      await _databaseService.deleteStory(storyId);
      await DecompressionUtil.deleteDecompressedStory(storyId);
      await _firebaseService.deleteLocalStory(storyId);
    } catch (e) {
      // Best effort cleanup
      print('Warning: Failed to cleanup failed download for $storyId: $e');
    }
  }

  /// Preloads story assets for faster playback
  Future<void> preloadStoryAssets(String storyId) async {
    try {
      final extractedPath = await DecompressionUtil.getDecompressedStoryPath(storyId);
      if (extractedPath == null) return;

      // Get all asset paths
      final images = await AssetManager.getStoryImages(extractedPath);
      final audioFiles = await AssetManager.getStoryAudio(extractedPath);
      
      final allAssets = [...images, ...audioFiles];
      
      // Preload assets (this could be expanded to actually cache in memory)
      await AssetManager.preloadAssets(allAssets);
    } catch (e) {
      // Non-critical error
      print('Warning: Failed to preload assets for $storyId: $e');
    }
  }

  /// Gets offline story statistics
  Future<Map<String, dynamic>> getOfflineStatistics() async {
    try {
      final downloadedStories = await getDownloadedStoryIds();
      final totalSize = await getOfflineStorageSize();
      
      return {
        'downloadedCount': downloadedStories.length,
        'totalSizeBytes': totalSize,
        'totalSizeMB': (totalSize / (1024 * 1024)).toStringAsFixed(2),
        'downloadedStories': downloadedStories,
      };
    } catch (e) {
      return {
        'downloadedCount': 0,
        'totalSizeBytes': 0,
        'totalSizeMB': '0.00',
        'downloadedStories': <String>[],
      };
    }
  }
}
