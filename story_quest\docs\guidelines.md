# Guidelines for AI Coding Assistant

To develop the kids' storytelling app efficiently and ensure it remains maintainable, the AI coding assistant must follow these principles:

## 1. Modular Design Principles
- **Feature-Based Structure**:
  - Organize the codebase into distinct features or screens, each in its own folder under `lib/features` (e.g., `lib/features/story_library`, `lib/features/story_play`).
  - Each feature folder should include its own widgets, models, and services to promote independence and reusability.
- **Component Independence**:
  - Minimize dependencies between features to allow isolated development, testing, and updates.
  - Use interfaces or abstract classes to define contracts between modules (e.g., `TTSService` for text-to-speech).
- **Naming Convention**:
  - Use **snake_case** for all file names (e.g., `story_library_screen.dart`, `story_model.dart`, `story_service.dart`).
  - Follow Dart’s naming conventions for classes (PascalCase) and variables (camelCase).

## 2. Responsive UI Elements
- **Adapt to Screen Sizes**:
  - Use `MediaQuery` to retrieve device dimensions and adjust layouts dynamically.
  - Define breakpoints for different device types (e.g., mobile: <600px, tablet: 600-1200px, TV: >1200px).
- **Flexible Layouts**:
  - Utilize widgets like `Flexible`, `Expanded`, and `LayoutBuilder` to create adaptive UIs.
  - Prefer relative sizing (e.g., `width: MediaQuery.of(context).size.width * 0.5`) over fixed pixel values.
- **Orientation Support**:
  - Ensure the UI works in both portrait and landscape modes using `OrientationBuilder` where needed.

## 3. Parent-Child Relationships (Widget Tree)
- **Widget Hierarchy**:
  - Structure the widget tree with clear parent-child relationships, ensuring each widget has a single responsibility.
  - Avoid deep nesting to prevent performance issues and improve readability.
- **Composition**:
  - Build complex UIs by composing smaller, reusable widgets (e.g., a `story_card.dart` widget used across screens).
  - Encapsulate widget logic within custom widgets for reusability.
- **State Management**:
  - Use state management solutions (e.g., `Provider`, `Riverpod`) to handle state efficiently across the widget tree.
  - Lift state to the appropriate level to minimize unnecessary rebuilds.

## 4. Codebase Management
- **Folder Structure**:
  - Organize code into a logical structure:
    - `lib/features`: Feature-specific code (screens, widgets, models).
    - `lib/models`: Data models used app-wide.
    - `lib/services`: Services (e.g., API, TTS, storage).
    - `lib/utils`: Utility functions and helpers.
    - `lib/widgets`: Reusable widgets shared across features.
- **Barrel Files**:
  - Use `index.dart` files within folders to export multiple files (e.g., `export 'story_library_screen.dart';` in `lib/features/story_library/index.dart`).
- **Code Organization**:
  - Keep related code (e.g., a screen’s widgets, models, and controllers) within the same feature folder.

## 5. Documentation
- **API Documentation**:
  - Use Dart’s `dartdoc` to generate documentation for classes, methods, and functions.
  - Write clear, concise doc comments following Dart’s guidelines.
- **Asset Documentation**:
  - Use tools like `flutter_gen` to document assets, fonts, and resources.
- **Script Tracking**:
  - After each run or build, append changes to a documentation file (e.g., `docs/script_changes.md`) with details like script name, modification date, and a summary of changes.
  - Automate this using a post-build script or CI/CD pipeline.

## 6. Future Updates and Modifications
- **Version Control**:
  - Use Git with descriptive commit messages.
  - Tag releases and maintain a `CHANGELOG.md` to track updates.
- **Dependency Injection**:
  - Implement a service locator (e.g., `get_it`) to manage dependencies, enabling easy swaps (e.g., from `flutter_tts` to a custom TTS).
- **Testing**:
  - Write unit tests for models and services.
  - Write widget tests for UI components.
  - Use integration tests for critical user flows to catch regressions.
- **Code Quality**:
  - Regularly run `flutter analyze` and `dartfmt` to maintain consistency and quality.

## 7. Additional Instructions
- **TTS Flexibility**:
  - Define a `TTSService` interface with methods like `speak`, `stop`, and `setLanguage`.
  - Use this interface app-wide to allow seamless switching of TTS implementations.
- **Asset Management**:
  - For Firebase-stored stories, ensure decompression logic maps assets to local paths correctly.
  - Use a consistent naming scheme for assets to avoid conflicts.
- **Error Handling**:
  - Implement robust error handling for network operations and asset loading.
  - Provide user-friendly fallbacks (e.g., default assets) when resources fail to load.
- **Performance Optimization**:
  - Optimize asset loading and caching for offline use.
  - Use `const` constructors for widgets to improve rendering performance.
- **Accessibility**:
  - Ensure UI elements are accessible (e.g., sufficient contrast, screen reader support).
  - Use large touch targets and simple gestures for younger users.

---

### Notes on Snake_Case Implementation
- **File Naming**: All Dart files must use snake_case (e.g., `story_library_screen.dart`, `tts_service.dart`).
- **Consistency**: Ensure all new files follow this convention. If renaming existing files, do so in a separate commit with clear documentation.
- **Barrel Files**: Update barrel files (e.g., `index.dart`) to reflect snake_case exports.

This update ensures the app’s codebase is consistent, readable, and aligned with industry standards, making it easier to maintain and extend.