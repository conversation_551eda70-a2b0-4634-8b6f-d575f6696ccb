/// Device detection and adaptive layout utilities
///
/// Provides utilities for detecting device types and creating adaptive layouts
/// that work across mobile phones, tablets, PCs, and TVs.
library;

import 'package:flutter/material.dart';
import 'dart:io' show Platform;
import 'dart:math' as math;
import 'package:flutter/foundation.dart' show kIsWeb;

/// Device types supported by the app
enum DeviceType {
  mobile,
  tablet,
  desktop,
  tv,
}

/// Screen orientation types
enum ScreenOrientation {
  portrait,
  landscape,
}

/// Device breakpoints for responsive design
class DeviceBreakpoints {
  static const double mobileMaxWidth = 600;
  static const double tabletMaxWidth = 1200;
  static const double desktopMaxWidth = 1920;
  
  static const double mobileMaxHeight = 800;
  static const double tabletMaxHeight = 1024;
  static const double desktopMaxHeight = 1080;
}

/// Device detection and adaptive layout utilities
class DeviceUtils {
  /// Gets the current device type based on screen size and platform
  static DeviceType getDeviceType(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final width = size.width;
    final height = size.height;
    final diagonal = _calculateDiagonal(width, height);
    
    // TV detection (large screens, typically landscape)
    if (diagonal > 40 && width > 1200) {
      return DeviceType.tv;
    }
    
    // Desktop detection
    if (kIsWeb || Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
      if (width > DeviceBreakpoints.tabletMaxWidth) {
        return DeviceType.desktop;
      }
    }
    
    // Tablet detection
    if (width > DeviceBreakpoints.mobileMaxWidth || diagonal > 7) {
      return DeviceType.tablet;
    }
    
    // Default to mobile
    return DeviceType.mobile;
  }
  
  /// Gets the current screen orientation
  static ScreenOrientation getOrientation(BuildContext context) {
    final orientation = MediaQuery.of(context).orientation;
    return orientation == Orientation.portrait 
        ? ScreenOrientation.portrait 
        : ScreenOrientation.landscape;
  }
  
  /// Checks if the device is in landscape mode
  static bool isLandscape(BuildContext context) {
    return getOrientation(context) == ScreenOrientation.landscape;
  }
  
  /// Checks if the device is in portrait mode
  static bool isPortrait(BuildContext context) {
    return getOrientation(context) == ScreenOrientation.portrait;
  }
  
  /// Gets responsive padding based on device type
  static EdgeInsets getResponsivePadding(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(16);
      case DeviceType.tablet:
        return const EdgeInsets.all(24);
      case DeviceType.desktop:
        return const EdgeInsets.all(32);
      case DeviceType.tv:
        return const EdgeInsets.all(48);
    }
  }
  
  /// Gets responsive font size based on device type
  static double getResponsiveFontSize(BuildContext context, double baseFontSize) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseFontSize;
      case DeviceType.tablet:
        return baseFontSize * 1.2;
      case DeviceType.desktop:
        return baseFontSize * 1.4;
      case DeviceType.tv:
        return baseFontSize * 1.8;
    }
  }
  
  /// Gets responsive icon size based on device type
  static double getResponsiveIconSize(BuildContext context, double baseIconSize) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseIconSize;
      case DeviceType.tablet:
        return baseIconSize * 1.3;
      case DeviceType.desktop:
        return baseIconSize * 1.5;
      case DeviceType.tv:
        return baseIconSize * 2.0;
    }
  }
  
  /// Gets responsive button height based on device type
  static double getResponsiveButtonHeight(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return 48;
      case DeviceType.tablet:
        return 56;
      case DeviceType.desktop:
        return 64;
      case DeviceType.tv:
        return 80;
    }
  }
  
  /// Gets responsive grid columns based on device type and orientation
  static int getResponsiveGridColumns(BuildContext context) {
    final deviceType = getDeviceType(context);
    final isLandscapeMode = isLandscape(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return isLandscapeMode ? 3 : 2;
      case DeviceType.tablet:
        return isLandscapeMode ? 4 : 3;
      case DeviceType.desktop:
        return isLandscapeMode ? 5 : 4;
      case DeviceType.tv:
        return isLandscapeMode ? 6 : 5;
    }
  }
  
  /// Gets responsive card width based on device type
  static double getResponsiveCardWidth(BuildContext context) {
    final deviceType = getDeviceType(context);
    final screenWidth = MediaQuery.of(context).size.width;
    
    switch (deviceType) {
      case DeviceType.mobile:
        return screenWidth * 0.4;
      case DeviceType.tablet:
        return screenWidth * 0.3;
      case DeviceType.desktop:
        return screenWidth * 0.2;
      case DeviceType.tv:
        return screenWidth * 0.15;
    }
  }
  
  /// Gets responsive card height based on device type
  static double getResponsiveCardHeight(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return 200;
      case DeviceType.tablet:
        return 240;
      case DeviceType.desktop:
        return 280;
      case DeviceType.tv:
        return 320;
    }
  }
  
  /// Gets responsive spacing based on device type
  static double getResponsiveSpacing(BuildContext context, double baseSpacing) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseSpacing;
      case DeviceType.tablet:
        return baseSpacing * 1.2;
      case DeviceType.desktop:
        return baseSpacing * 1.5;
      case DeviceType.tv:
        return baseSpacing * 2.0;
    }
  }
  
  /// Gets responsive border radius based on device type
  static double getResponsiveBorderRadius(BuildContext context, double baseBorderRadius) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseBorderRadius;
      case DeviceType.tablet:
        return baseBorderRadius * 1.2;
      case DeviceType.desktop:
        return baseBorderRadius * 1.4;
      case DeviceType.tv:
        return baseBorderRadius * 1.6;
    }
  }
  
  /// Checks if the device supports touch input
  static bool supportsTouchInput(BuildContext context) {
    final deviceType = getDeviceType(context);
    return deviceType == DeviceType.mobile || deviceType == DeviceType.tablet;
  }
  
  /// Checks if the device is likely to use remote control navigation
  static bool usesRemoteControl(BuildContext context) {
    final deviceType = getDeviceType(context);
    return deviceType == DeviceType.tv;
  }
  
  /// Gets the maximum content width for the device
  static double getMaxContentWidth(BuildContext context) {
    final deviceType = getDeviceType(context);
    final screenWidth = MediaQuery.of(context).size.width;
    
    switch (deviceType) {
      case DeviceType.mobile:
        return screenWidth;
      case DeviceType.tablet:
        return screenWidth * 0.9;
      case DeviceType.desktop:
        return 1200;
      case DeviceType.tv:
        return 1400;
    }
  }
  
  /// Calculates diagonal screen size in inches (approximate)
  static double _calculateDiagonal(double width, double height) {
    // Assuming 160 DPI (Android standard)
    const dpi = 160.0;
    final widthInches = width / dpi;
    final heightInches = height / dpi;
    return math.sqrt(widthInches * widthInches + heightInches * heightInches);
  }
}

/// Responsive layout builder widget
class ResponsiveLayoutBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, DeviceType deviceType, ScreenOrientation orientation) builder;
  
  const ResponsiveLayoutBuilder({
    super.key,
    required this.builder,
  });
  
  @override
  Widget build(BuildContext context) {
    final deviceType = DeviceUtils.getDeviceType(context);
    final orientation = DeviceUtils.getOrientation(context);
    
    return builder(context, deviceType, orientation);
  }
}

/// Responsive widget that adapts based on device type
class ResponsiveWidget extends StatelessWidget {
  final Widget? mobile;
  final Widget? tablet;
  final Widget? desktop;
  final Widget? tv;
  final Widget? fallback;
  
  const ResponsiveWidget({
    super.key,
    this.mobile,
    this.tablet,
    this.desktop,
    this.tv,
    this.fallback,
  });
  
  @override
  Widget build(BuildContext context) {
    final deviceType = DeviceUtils.getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile ?? fallback ?? const SizedBox.shrink();
      case DeviceType.tablet:
        return tablet ?? mobile ?? fallback ?? const SizedBox.shrink();
      case DeviceType.desktop:
        return desktop ?? tablet ?? fallback ?? const SizedBox.shrink();
      case DeviceType.tv:
        return tv ?? desktop ?? fallback ?? const SizedBox.shrink();
    }
  }
}

/// Extension methods for responsive design
extension ResponsiveContext on BuildContext {
  DeviceType get deviceType => DeviceUtils.getDeviceType(this);
  ScreenOrientation get screenOrientation => DeviceUtils.getOrientation(this);
  bool get isLandscape => DeviceUtils.isLandscape(this);
  bool get isPortrait => DeviceUtils.isPortrait(this);
  bool get isMobile => deviceType == DeviceType.mobile;
  bool get isTablet => deviceType == DeviceType.tablet;
  bool get isDesktop => deviceType == DeviceType.desktop;
  bool get isTV => deviceType == DeviceType.tv;
  bool get supportsTouchInput => DeviceUtils.supportsTouchInput(this);
  bool get usesRemoteControl => DeviceUtils.usesRemoteControl(this);
}
