/// Story Play screen
/// 
/// Immersive story playback with TTS narration, synchronized subtitles,
/// interactive elements, and themed visual experience.
library;

import 'dart:async';
import 'package:flutter/material.dart';
import '../../models/story_model.dart';
import '../../models/scene_model.dart';
import '../../models/choice_model.dart';
import '../../utils/accessibility_helper.dart';
import '../../utils/asset_manager.dart';
import '../../services/service_locator.dart';

/// Story playback state
enum PlaybackState {
  stopped,
  playing,
  paused,
  loading,
}

/// Story Play screen widget
class StoryPlayScreen extends StatefulWidget {
  /// The story to play
  final StoryModel story;
  
  /// Starting scene ID (optional)
  final String? startingSceneId;
  
  /// Callback when story is completed
  final VoidCallback? onStoryComplete;
  
  /// Callback for back navigation
  final VoidCallback? onBack;

  const StoryPlayScreen({
    super.key,
    required this.story,
    this.startingSceneId,
    this.onStoryComplete,
    this.onBack,
  });

  @override
  State<StoryPlayScreen> createState() => _StoryPlayScreenState();
}

class _StoryPlayScreenState extends State<StoryPlayScreen>
    with TickerProviderStateMixin {
  late AnimationController _subtitleController;
  late AnimationController _sceneController;
  late Animation<double> _subtitleAnimation;
  late Animation<double> _sceneAnimation;
  
  PlaybackState _playbackState = PlaybackState.stopped;
  SceneModel? _currentScene;
  String _currentText = '';
  String _highlightedText = '';
  int _currentWordIndex = 0;
  List<String> _words = [];
  Timer? _wordTimer;
  double _progress = 0.0;
  bool _showSubtitles = true;
  double _subtitleSize = 16.0;
  double _subtitleOpacity = 0.8;

  @override
  void initState() {
    super.initState();
    
    // Initialize animations
    _subtitleController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _sceneController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _subtitleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _subtitleController,
      curve: Curves.easeInOut,
    ));

    _sceneAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _sceneController,
      curve: Curves.easeInOut,
    ));

    // Load initial scene
    _loadScene(widget.startingSceneId ?? widget.story.firstScene?.id);
  }

  @override
  void dispose() {
    _wordTimer?.cancel();
    _subtitleController.dispose();
    _sceneController.dispose();
    serviceLocator.tts.stop();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        // Show confirmation dialog before leaving story
        final shouldPop = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Leave Story?'),
            content: const Text('Are you sure you want to leave this story? Your progress will be lost.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Stay'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Leave'),
              ),
            ],
          ),
        );

        if (shouldPop == true && context.mounted) {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: Stack(
        children: [
          // Background and scene content
          _buildSceneBackground(),
          
          // Interactive elements
          if (_currentScene != null) _buildInteractiveElements(),
          
          // Subtitle overlay
          if (_showSubtitles) _buildSubtitleOverlay(),
          
          // Control bar
          _buildControlBar(),
          
          // Back button
          _buildBackButton(),
          
          // Moral moment popup
          if (_shouldShowMoralMoment()) _buildMoralMomentPopup(),
        ],
      ),
    ),
    );
  }

  /// Builds the themed scene background
  Widget _buildSceneBackground() {
    return AnimatedBuilder(
      animation: _sceneAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _sceneAnimation.value,
          child: Container(
            decoration: _buildThemedDecoration(),
            child: _currentScene != null 
                ? _buildSceneImage()
                : _buildLoadingState(),
          ),
        );
      },
    );
  }

  /// Builds themed decoration based on scene
  BoxDecoration _buildThemedDecoration() {
    // Default night scene with stars
    return const BoxDecoration(
      gradient: RadialGradient(
        center: Alignment.topRight,
        radius: 1.5,
        colors: [
          Color(0xFF1A237E), // Deep blue
          Color(0xFF000051), // Very dark blue
          Colors.black,
        ],
      ),
    );
  }

  /// Builds scene image
  Widget _buildSceneImage() {
    return FutureBuilder<String>(
      future: _getSceneImagePath(),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(snapshot.data!),
                fit: BoxFit.cover,
                colorFilter: ColorFilter.mode(
                  Colors.black.withValues(alpha: 0.3),
                  BlendMode.darken,
                ),
              ),
            ),
            child: _buildStarField(),
          );
        }
        return _buildStarField();
      },
    );
  }

  /// Builds animated star field overlay
  Widget _buildStarField() {
    return Stack(
      children: List.generate(20, (index) {
        return Positioned(
          left: (index * 37) % MediaQuery.of(context).size.width,
          top: (index * 23) % MediaQuery.of(context).size.height,
          child: AnimatedBuilder(
            animation: _sceneController,
            builder: (context, child) {
              return Opacity(
                opacity: (0.3 + (index % 3) * 0.2) * _sceneAnimation.value,
                child: Icon(
                  Icons.star,
                  color: Colors.white,
                  size: 8 + (index % 3) * 4,
                ),
              );
            },
          ),
        );
      }),
    );
  }

  /// Builds interactive elements (tappable characters/objects)
  Widget _buildInteractiveElements() {
    return Positioned.fill(
      child: Stack(
        children: [
          // Example interactive element - could be expanded based on scene data
          if (_currentScene!.speaker != 'narrator')
            Positioned(
              bottom: 200,
              left: 50,
              child: AccessibilityHelper.createAccessibleButton(
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 2,
                    ),
                  ),
                  child: const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 40,
                  ),
                ),
                onPressed: _playCharacterSound,
                semanticLabel: 'Tap to hear ${_currentScene!.speaker}',
              ),
            ),
        ],
      ),
    );
  }

  /// Builds subtitle overlay with word highlighting
  Widget _buildSubtitleOverlay() {
    return Positioned(
      bottom: 120,
      left: 20,
      right: 20,
      child: AnimatedBuilder(
        animation: _subtitleAnimation,
        builder: (context, child) {
          return Opacity(
            opacity: _subtitleAnimation.value * _subtitleOpacity,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: _buildHighlightedText(),
            ),
          );
        },
      ),
    );
  }

  /// Builds text with word highlighting
  Widget _buildHighlightedText() {
    if (_words.isEmpty) {
      return AccessibilityHelper.createAccessibleText(
        _currentText,
        style: TextStyle(
          color: Colors.white,
          fontSize: _subtitleSize,
          height: 1.4,
        ),
      );
    }

    return RichText(
      text: TextSpan(
        children: _words.asMap().entries.map((entry) {
          final index = entry.key;
          final word = entry.value;
          final isHighlighted = index == _currentWordIndex;
          
          return TextSpan(
            text: '$word ',
            style: TextStyle(
              color: isHighlighted 
                  ? const Color(0xFFFFCA28) // Yellow highlight
                  : Colors.white,
              fontSize: _subtitleSize,
              fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
              height: 1.4,
            ),
          );
        }).toList(),
      ),
    );
  }

  /// Builds control bar
  Widget _buildControlBar() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        height: 80,
        decoration: BoxDecoration(
          color: const Color(0xFFB0BEC5).withValues(alpha: 0.9),
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: Row(
          children: [
            // Play/Pause button
            AccessibilityHelper.createAccessibleButton(
              child: Container(
                margin: const EdgeInsets.all(12),
                decoration: const BoxDecoration(
                  color: Color(0xFF4CAF50),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _playbackState == PlaybackState.playing 
                      ? Icons.pause 
                      : Icons.play_arrow,
                  color: Colors.white,
                  size: 32,
                ),
              ),
              onPressed: _togglePlayback,
              semanticLabel: _playbackState == PlaybackState.playing 
                  ? 'Pause story' 
                  : 'Play story',
            ),
            
            // Progress bar
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  LinearProgressIndicator(
                    value: _progress,
                    backgroundColor: Colors.white.withValues(alpha: 0.3),
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Color(0xFF4CAF50),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${(_progress * 100).toInt()}% complete',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            
            // Rewind button
            AccessibilityHelper.createAccessibleButton(
              child: Container(
                margin: const EdgeInsets.all(8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFCA28),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.replay_10,
                  color: Colors.black87,
                  size: 24,
                ),
              ),
              onPressed: _rewind10Seconds,
              semanticLabel: 'Rewind 10 seconds',
            ),
            
            // Settings button
            AccessibilityHelper.createAccessibleButton(
              child: Container(
                margin: const EdgeInsets.all(8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF1976D2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.settings,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              onPressed: _showSettings,
              semanticLabel: 'Story settings',
            ),
          ],
        ),
      ),
    );
  }

  /// Builds back button
  Widget _buildBackButton() {
    return Positioned(
      top: 40,
      left: 20,
      child: AccessibilityHelper.createAccessibleButton(
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFFB0BEC5).withValues(alpha: 0.8),
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.arrow_back,
            color: Colors.white,
            size: 24,
          ),
        ),
        onPressed: _handleBack,
        semanticLabel: 'Exit story playback',
      ),
    );
  }

  /// Builds moral moment popup
  Widget _buildMoralMomentPopup() {
    return Positioned.fill(
      child: Container(
        color: Colors.black.withValues(alpha: 0.8),
        child: Center(
          child: Container(
            margin: const EdgeInsets.all(40),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFF1976D2),
                width: 3,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.lightbulb,
                  color: Color(0xFF1976D2),
                  size: 48,
                ),
                const SizedBox(height: 16),
                AccessibilityHelper.createAccessibleText(
                  'Moral Moment',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1976D2),
                  ),
                ),
                const SizedBox(height: 16),
                AccessibilityHelper.createAccessibleText(
                  'What do you think ${_currentScene?.speaker} should do?',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: _closeMoralMoment,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF1976D2),
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Continue Story'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Builds loading state
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
          ),
          SizedBox(height: 16),
          Text(
            'Loading story...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
            ),
          ),
        ],
      ),
    );
  }

  /// Loads a scene by ID
  void _loadScene(String? sceneId) async {
    if (sceneId == null) {
      _handleStoryComplete();
      return;
    }

    setState(() {
      _playbackState = PlaybackState.loading;
    });

    final scene = widget.story.getSceneById(sceneId);
    if (scene == null) {
      _handleStoryComplete();
      return;
    }

    setState(() {
      _currentScene = scene;
      _currentText = scene.text;
      _words = scene.text.split(' ');
      _currentWordIndex = 0;
    });

    _sceneController.forward();
    _subtitleController.forward();

    // Auto-play scene
    await Future.delayed(const Duration(milliseconds: 500));
    _playScene();
  }

  /// Plays the current scene
  void _playScene() async {
    if (_currentScene == null) return;

    setState(() {
      _playbackState = PlaybackState.playing;
    });

    try {
      // Set voice based on speaker
      final character = widget.story.getCharacterByName(_currentScene!.speaker);
      if (character != null) {
        await serviceLocator.tts.setVoice(character.voice);
      } else {
        await serviceLocator.tts.setVoice(widget.story.narratorProfile.voice);
      }

      // Start word highlighting
      _startWordHighlighting();

      // Speak the text
      await serviceLocator.tts.speak(_currentScene!.text);

      // Handle scene completion
      _handleSceneComplete();
    } catch (e) {
      setState(() {
        _playbackState = PlaybackState.stopped;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unable to play audio, using default'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  /// Starts word highlighting animation
  void _startWordHighlighting() {
    if (_words.isEmpty) return;

    final wordsPerSecond = 2.5; // Adjust based on TTS speed
    final intervalMs = (1000 / wordsPerSecond).round();

    _wordTimer = Timer.periodic(Duration(milliseconds: intervalMs), (timer) {
      if (_currentWordIndex < _words.length - 1) {
        setState(() {
          _currentWordIndex++;
        });
      } else {
        timer.cancel();
      }
    });
  }

  /// Handles scene completion
  void _handleSceneComplete() {
    _wordTimer?.cancel();
    
    setState(() {
      _playbackState = PlaybackState.stopped;
    });

    if (_currentScene!.hasChoices) {
      _showChoices();
    } else if (_currentScene!.next != null) {
      // Auto-advance to next scene after pause
      Future.delayed(Duration(milliseconds: _currentScene!.pauseDuration), () {
        if (mounted) {
          _loadScene(_currentScene!.next);
        }
      });
    } else {
      // Scene has no next - show post-story content before completing
      _showPostStoryContent();
    }
  }

  /// Shows choice selection dialog
  void _showChoices() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('What happens next?'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _currentScene!.choices.map((choice) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _handleChoiceSelection(choice);
                  },
                  child: Text(choice.option),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Toggles playback state
  void _togglePlayback() {
    if (_playbackState == PlaybackState.playing) {
      serviceLocator.tts.pause();
      setState(() {
        _playbackState = PlaybackState.paused;
      });
    } else if (_playbackState == PlaybackState.paused) {
      serviceLocator.tts.resume();
      setState(() {
        _playbackState = PlaybackState.playing;
      });
    } else {
      _playScene();
    }
  }

  /// Rewinds 10 seconds (restarts current scene)
  void _rewind10Seconds() {
    serviceLocator.tts.stop();
    _wordTimer?.cancel();
    setState(() {
      _currentWordIndex = 0;
    });
    _playScene();
  }

  /// Shows settings dialog
  void _showSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Story Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('Show Subtitles'),
              value: _showSubtitles,
              onChanged: (value) {
                setState(() {
                  _showSubtitles = value;
                });
                Navigator.of(context).pop();
              },
            ),
            ListTile(
              title: const Text('Subtitle Size'),
              subtitle: Slider(
                value: _subtitleSize,
                min: 12.0,
                max: 24.0,
                divisions: 6,
                onChanged: (value) {
                  setState(() {
                    _subtitleSize = value;
                  });
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Plays character sound effect
  void _playCharacterSound() {
    // Play character-specific sound or voice line
    AccessibilityHelper.provideHapticFeedback();
    
    // Could play bird chirps, animal sounds, etc.
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${_currentScene!.speaker} says hello!'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  /// Handles back navigation
  void _handleBack() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exit Story?'),
        content: const Text('Are you sure you want to stop reading this story?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Continue Reading'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onBack?.call();
            },
            child: const Text('Exit'),
          ),
        ],
      ),
    );
  }

  /// Handles choice selection and potential badge awarding
  void _handleChoiceSelection(ChoiceModel choice) {
    // Load the next scene
    _loadScene(choice.next);

    // Award badge if applicable
    _checkAndAwardBadge(choice);
  }

  /// Checks if a badge should be awarded for the choice
  void _checkAndAwardBadge(ChoiceModel choice) {
    // This would integrate with the badge service
    // For now, we'll show a simple feedback based on the choice

    // Find the next scene to check its outcome
    final nextScene = widget.story.getSceneById(choice.next);
    if (nextScene?.outcome == 'good') {
      // Show positive feedback
      Future.delayed(const Duration(milliseconds: 1500), () {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(Icons.star, color: Colors.amber),
                  SizedBox(width: 8),
                  Text('Great choice! You showed kindness.'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      });
    }
  }

  /// Shows post-story content (moral lesson, vocabulary, discussion)
  void _showPostStoryContent() {
    // First show moral lesson if current scene has reflection
    if (_currentScene?.reflection != null) {
      _showMoralLesson();
    } else {
      // Skip to vocabulary if no reflection
      _showVocabularyDiscussion();
    }
  }

  /// Shows moral lesson popup
  void _showMoralLesson() {
    if (_currentScene?.reflection == null) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Think About It'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _currentScene!.reflection!.text,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            // Show any earned badges here
            if (_currentScene!.outcome == 'good')
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green),
                ),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.star, color: Colors.amber),
                    SizedBox(width: 8),
                    Text('Great choice! You showed kindness.'),
                  ],
                ),
              ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showVocabularyDiscussion();
            },
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  /// Shows vocabulary discussion
  void _showVocabularyDiscussion() {
    if (widget.story.vocabulary.isEmpty) {
      _showPostStoryDiscussion();
      return;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('New Words We Learned'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: widget.story.vocabulary.length,
            itemBuilder: (context, index) {
              final vocab = widget.story.vocabulary[index];
              return Card(
                margin: const EdgeInsets.symmetric(vertical: 4),
                child: ListTile(
                  leading: const Icon(Icons.book, color: Colors.blue),
                  title: Text(
                    vocab.word,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Text(vocab.explanation),
                ),
              );
            },
          ),
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showPostStoryDiscussion();
            },
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  /// Shows post-story discussion
  void _showPostStoryDiscussion() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Story Complete!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              widget.story.postStory.discussion.text,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            const Text(
              'Great job reading this story!',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ],
        ),
        actions: [
          OutlinedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Restart the story
              _loadScene(widget.story.firstScene?.id);
            },
            child: const Text('Read Again'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _handleStoryComplete();
            },
            child: const Text('Finish'),
          ),
        ],
      ),
    );
  }

  /// Handles story completion
  void _handleStoryComplete() {
    // Use post frame callback to avoid calling during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        widget.onStoryComplete?.call();
      }
    });
  }

  /// Determines if moral moment should be shown
  bool _shouldShowMoralMoment() {
    // Show moral moment for certain scenes or story progression
    return false; // Placeholder logic
  }

  /// Closes moral moment popup
  void _closeMoralMoment() {
    // Continue with story
  }

  /// Gets scene image path with fallback
  Future<String> _getSceneImagePath() async {
    if (_currentScene == null) {
      return AssetManager.defaultImagePath;
    }

    // This would use AssetManager to resolve the actual image path
    // For now, return default
    return AssetManager.defaultImagePath;
  }
}
