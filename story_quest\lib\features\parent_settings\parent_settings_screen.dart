/// Parent Settings screen for app configuration and controls
///
/// Provides comprehensive parental controls including TTS settings,
/// content filtering, time limits, and notification preferences.
library;

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../utils/accessibility_helper.dart';

/// Parent Settings screen widget
class ParentSettingsScreen extends StatefulWidget {
  const ParentSettingsScreen({super.key});

  @override
  State<ParentSettingsScreen> createState() => _ParentSettingsScreenState();
}

class _ParentSettingsScreenState extends State<ParentSettingsScreen>
    with TickerProviderStateMixin {
  
  // Settings values
  double _ttsSpeed = 1.0;
  bool _contentFilteringEnabled = true;
  int _dailyTimeLimitMinutes = 60;
  bool _badgeNotificationsEnabled = true;
  bool _storyCompletionNotificationsEnabled = true;
  bool _milestoneNotificationsEnabled = true;
  bool _soundEffectsEnabled = true;
  bool _autoPlayEnabled = false;
  
  bool _isLoading = true;
  bool _isSaving = false;
  
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize animations
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(1, 0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _loadSettings();
    _slideController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  /// Loads settings from SharedPreferences
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      setState(() {
        _ttsSpeed = prefs.getDouble('tts_speed') ?? 1.0;
        _contentFilteringEnabled = prefs.getBool('content_filtering') ?? true;
        _dailyTimeLimitMinutes = prefs.getInt('daily_time_limit') ?? 60;
        _badgeNotificationsEnabled = prefs.getBool('badge_notifications') ?? true;
        _storyCompletionNotificationsEnabled = prefs.getBool('story_completion_notifications') ?? true;
        _milestoneNotificationsEnabled = prefs.getBool('milestone_notifications') ?? true;
        _soundEffectsEnabled = prefs.getBool('sound_effects') ?? true;
        _autoPlayEnabled = prefs.getBool('auto_play') ?? false;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Saves settings to SharedPreferences
  Future<void> _saveSettings() async {
    setState(() {
      _isSaving = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      
      await Future.wait([
        prefs.setDouble('tts_speed', _ttsSpeed),
        prefs.setBool('content_filtering', _contentFilteringEnabled),
        prefs.setInt('daily_time_limit', _dailyTimeLimitMinutes),
        prefs.setBool('badge_notifications', _badgeNotificationsEnabled),
        prefs.setBool('story_completion_notifications', _storyCompletionNotificationsEnabled),
        prefs.setBool('milestone_notifications', _milestoneNotificationsEnabled),
        prefs.setBool('sound_effects', _soundEffectsEnabled),
        prefs.setBool('auto_play', _autoPlayEnabled),
      ]);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Settings saved successfully!'),
            backgroundColor: Color(0xFF4CAF50),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to save settings. Please try again.'),
            backgroundColor: Color(0xFFE57373),
          ),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A237E), // Deep blue
              Color(0xFF3F51B5), // Indigo
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: SlideTransition(
                  position: _slideAnimation,
                  child: _isLoading ? _buildLoadingIndicator() : _buildSettingsContent(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the screen header
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // Back button
          AccessibilityHelper.createAccessibleButton(
            child: Container(
              width: 44,
              height: 44,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Colors.white,
              ),
            ),
            onPressed: () => Navigator.of(context).pop(),
            semanticLabel: 'Go back',
          ),
          
          const SizedBox(width: 16),
          
          // Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AccessibilityHelper.createAccessibleText(
                  'Parent Settings',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                AccessibilityHelper.createAccessibleText(
                  'Customize your child\'s experience',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          
          // Save button
          AccessibilityHelper.createAccessibleButton(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: const Color(0xFF4CAF50),
                borderRadius: BorderRadius.circular(20),
              ),
              child: _isSaving
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'Save',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
            onPressed: _isSaving ? null : _saveSettings,
            semanticLabel: 'Save settings',
          ),
        ],
      ),
    );
  }

  /// Builds loading indicator
  Widget _buildLoadingIndicator() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
      ),
    );
  }

  /// Builds settings content
  Widget _buildSettingsContent() {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('Audio Settings'),
            _buildTTSSpeedSetting(),
            _buildSwitchSetting(
              'Sound Effects',
              'Enable sound effects during stories',
              _soundEffectsEnabled,
              (value) => setState(() => _soundEffectsEnabled = value),
            ),
            _buildSwitchSetting(
              'Auto Play',
              'Automatically play next story scene',
              _autoPlayEnabled,
              (value) => setState(() => _autoPlayEnabled = value),
            ),
            
            const SizedBox(height: 30),
            
            _buildSectionTitle('Content & Safety'),
            _buildSwitchSetting(
              'Content Filtering',
              'Filter age-appropriate content',
              _contentFilteringEnabled,
              (value) => setState(() => _contentFilteringEnabled = value),
            ),
            _buildTimeLimitSetting(),
            
            const SizedBox(height: 30),
            
            _buildSectionTitle('Notifications'),
            _buildSwitchSetting(
              'Badge Achievements',
              'Notify when child earns badges',
              _badgeNotificationsEnabled,
              (value) => setState(() => _badgeNotificationsEnabled = value),
            ),
            _buildSwitchSetting(
              'Story Completion',
              'Notify when child completes stories',
              _storyCompletionNotificationsEnabled,
              (value) => setState(() => _storyCompletionNotificationsEnabled = value),
            ),
            _buildSwitchSetting(
              'Milestones',
              'Notify about important milestones',
              _milestoneNotificationsEnabled,
              (value) => setState(() => _milestoneNotificationsEnabled = value),
            ),
            
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  /// Builds section title
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Color(0xFF2E7D32),
        ),
      ),
    );
  }

  /// Builds TTS speed setting
  Widget _buildTTSSpeedSetting() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Reading Speed',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF2E7D32),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Adjust how fast stories are read aloud',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF757575),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Text('Slow', style: TextStyle(fontSize: 12)),
              Expanded(
                child: Slider(
                  value: _ttsSpeed,
                  min: 0.5,
                  max: 2.0,
                  divisions: 6,
                  label: '${_ttsSpeed.toStringAsFixed(1)}x',
                  activeColor: const Color(0xFF4CAF50),
                  onChanged: (value) => setState(() => _ttsSpeed = value),
                ),
              ),
              const Text('Fast', style: TextStyle(fontSize: 12)),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds time limit setting
  Widget _buildTimeLimitSetting() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Daily Time Limit',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF2E7D32),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Set maximum daily usage time',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF757575),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Text('15 min', style: TextStyle(fontSize: 12)),
              Expanded(
                child: Slider(
                  value: _dailyTimeLimitMinutes.toDouble(),
                  min: 15,
                  max: 180,
                  divisions: 11,
                  label: '$_dailyTimeLimitMinutes min',
                  activeColor: const Color(0xFF4CAF50),
                  onChanged: (value) => setState(() => _dailyTimeLimitMinutes = value.round()),
                ),
              ),
              const Text('3 hrs', style: TextStyle(fontSize: 12)),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds switch setting
  Widget _buildSwitchSetting(String title, String subtitle, bool value, ValueChanged<bool> onChanged) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2E7D32),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF757575),
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: const Color(0xFF4CAF50),
          ),
        ],
      ),
    );
  }
}
