{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Install\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\AliM Studio\\App 01\\App\\story_quest\\build\\.cxx\\Debug\\2x18z2m4\\x86", "clean"]], "buildTargetsCommandComponents": ["D:\\Install\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\AliM Studio\\App 01\\App\\story_quest\\build\\.cxx\\Debug\\2x18z2m4\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}