/// First Time User Experience (FTUE) screen
/// 
/// Clean interface for account creation and login with social sign-in options
/// and privacy consent. Responsive design with accessibility support.
library;

import 'package:flutter/material.dart';

/// FTUE screen for new user onboarding
class FTUEScreen extends StatefulWidget {
  /// Callback for create account action
  final VoidCallback? onCreateAccount;
  
  /// Callback for login action
  final VoidCallback? onLogin;
  
  /// Callback for Google sign-in
  final VoidCallback? onGoogleSignIn;
  
  /// Callback for Apple sign-in
  final VoidCallback? onAppleSignIn;
  
  /// Callback for back navigation
  final VoidCallback? onBack;

  const FTUEScreen({
    super.key,
    this.onCreateAccount,
    this.onLogin,
    this.onGoogleSignIn,
    this.onAppleSignIn,
    this.onBack,
  });

  @override
  State<FTUEScreen> createState() => _FTUEScreenState();
}

class _FTUEScreenState extends State<FTUEScreen> {
  bool _privacyConsent = false;

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: isTablet ? _buildTabletLayout(context) : _buildMobileLayout(context),
      ),
    );
  }

  /// Builds layout for mobile devices
  Widget _buildMobileLayout(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    // Responsive padding and spacing
    final padding = screenWidth * 0.04; // 4% of screen width
    final spacing = screenHeight * 0.02; // 2% of screen height

    return SingleChildScrollView(
      padding: EdgeInsets.all(padding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildHeader(context),
          SizedBox(height: spacing * 2),
          _buildLoginOptions(context),
          SizedBox(height: spacing * 1.5),
          _buildSocialSignIn(context),
          SizedBox(height: spacing * 2),
          _buildPrivacySection(context),
          SizedBox(height: spacing),
        ],
      ),
    );
  }

  /// Builds layout for tablets with split screen
  Widget _buildTabletLayout(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    // Responsive padding and spacing for landscape mode
    final padding = screenWidth * 0.02; // 2% of screen width (reduced)
    final spacing = screenHeight * 0.015; // 1.5% of screen height (reduced)

    return Row(
      children: [
        // Left side - Login options
        Expanded(
          flex: 3,
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                _buildHeader(context),
                SizedBox(height: spacing),
                _buildLoginOptions(context),
                SizedBox(height: spacing * 0.8),
                _buildSocialSignIn(context),
                SizedBox(height: spacing * 0.6),
                _buildPrivacySection(context),
                ],
              ),
            ),
          ),
        ),

        // Right side - Reassuring image
        Expanded(
          flex: 2,
          child: Container(
            color: const Color(0xFFF5F5F5),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.family_restroom,
                    size: screenWidth * 0.08, // Responsive icon size
                    color: const Color(0xFF1976D2),
                  ),
                  SizedBox(height: spacing * 0.7),
                  Text(
                    'Safe & Fun\nStory Adventures',
                    style: TextStyle(
                      fontSize: screenWidth * 0.02, // Responsive font size
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF1976D2),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the header section
  Widget _buildHeader(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Back button
        Align(
          alignment: Alignment.centerLeft,
          child: Semantics(
            button: true,
            label: 'Go back to previous screen',
            child: IconButton(
              onPressed: widget.onBack,
              icon: Icon(
                Icons.arrow_back,
                color: const Color(0xFFB0BEC5),
                size: screenWidth * 0.025, // Responsive icon size
              ),
            ),
          ),
        ),

        SizedBox(height: screenHeight * 0.015),

        // Title
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: screenHeight * 0.015),
          decoration: const BoxDecoration(
            color: Color(0xFF1976D2),
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
          child: Text(
            'First Time Setup',
            style: TextStyle(
              fontSize: screenWidth * 0.025, // Responsive font size
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  /// Builds the main login options
  Widget _buildLoginOptions(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    return Column(
      children: [
        // Create Account button
        Semantics(
          button: true,
          label: 'Create a new account to get started',
          child: SizedBox(
            width: double.infinity,
            height: screenHeight * 0.05, // Responsive button height (reduced)
            child: ElevatedButton(
              onPressed: _privacyConsent ? widget.onCreateAccount : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4CAF50),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: _privacyConsent ? 4 : 0,
                shadowColor: _privacyConsent
                    ? const Color(0xFF4CAF50).withValues(alpha: 0.4)
                    : Colors.transparent,
              ),
              child: Text(
                'Create Account',
                style: TextStyle(
                  fontSize: screenWidth * 0.02, // Responsive font size
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),

        SizedBox(height: screenHeight * 0.015),

        // Login button
        Semantics(
          button: true,
          label: 'Login with existing account',
          child: SizedBox(
            width: double.infinity,
            height: screenHeight * 0.05, // Responsive button height (reduced)
            child: ElevatedButton(
              onPressed: _privacyConsent ? widget.onLogin : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4CAF50),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: _privacyConsent ? 4 : 0,
                shadowColor: _privacyConsent
                    ? const Color(0xFF4CAF50).withValues(alpha: 0.4)
                    : Colors.transparent,
              ),
              child: Text(
                'Login',
                style: TextStyle(
                  fontSize: screenWidth * 0.02, // Responsive font size
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds social sign-in options
  Widget _buildSocialSignIn(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    return Column(
      children: [
        Text(
          'Or sign in with',
          style: TextStyle(
            fontSize: screenWidth * 0.018, // Responsive font size
            color: const Color(0xFF757575),
          ),
        ),

        SizedBox(height: screenHeight * 0.015),

        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Google sign-in
            Semantics(
              button: true,
              label: 'Sign in with Google',
              child: GestureDetector(
                onTap: _privacyConsent ? widget.onGoogleSignIn : null,
                child: Container(
                  width: screenWidth * 0.04, // Responsive size (reduced)
                  height: screenWidth * 0.04, // Keep square aspect ratio
                  decoration: BoxDecoration(
                    color: _privacyConsent
                        ? const Color(0xFFB0BEC5)
                        : const Color(0xFFE0E0E0),
                    shape: BoxShape.circle,
                    boxShadow: _privacyConsent ? [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ] : null,
                  ),
                  child: Center(
                    child: Text(
                      'G',
                      style: TextStyle(
                        fontSize: screenWidth * 0.025, // Responsive font size
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // Apple sign-in
            Semantics(
              button: true,
              label: 'Sign in with Apple',
              child: GestureDetector(
                onTap: _privacyConsent ? widget.onAppleSignIn : null,
                child: Container(
                  width: screenWidth * 0.04, // Responsive size (reduced)
                  height: screenWidth * 0.04, // Keep square aspect ratio
                  decoration: BoxDecoration(
                    color: _privacyConsent
                        ? const Color(0xFFB0BEC5)
                        : const Color(0xFFE0E0E0),
                    shape: BoxShape.circle,
                    boxShadow: _privacyConsent ? [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ] : null,
                  ),
                  child: Icon(
                    Icons.apple,
                    color: Colors.white,
                    size: screenWidth * 0.025, // Responsive icon size
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Builds privacy consent section
  Widget _buildPrivacySection(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Privacy consent checkbox
        Semantics(
          label: 'Privacy consent checkbox. Required to continue.',
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Checkbox(
                value: _privacyConsent,
                onChanged: (value) {
                  setState(() {
                    _privacyConsent = value ?? false;
                  });
                },
                activeColor: const Color(0xFF4CAF50),
                materialTapTargetSize: MaterialTapTargetSize.padded,
              ),

              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _privacyConsent = !_privacyConsent;
                    });
                  },
                  child: Padding(
                    padding: EdgeInsets.only(top: screenHeight * 0.01),
                    child: Text(
                      'I agree to the Privacy Policy and Terms of Service. '
                      'Story Quest is committed to protecting children\'s privacy and safety.',
                      style: TextStyle(
                        fontSize: screenWidth * 0.015, // Responsive font size
                        color: const Color(0xFF757575),
                        height: 1.4,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: screenHeight * 0.015),

        // Additional privacy notice
        Container(
          padding: EdgeInsets.all(screenWidth * 0.01),
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: const Color(0xFFE0E0E0),
              width: 1,
            ),
          ),
          child: Text(
            '🔒 Your child\'s safety is our priority. We follow strict privacy '
            'guidelines and never share personal information.',
            style: TextStyle(
              fontSize: screenWidth * 0.013, // Responsive font size
              color: const Color(0xFF757575),
              height: 1.3,
            ),
          ),
        ),
      ],
    );
  }
}
