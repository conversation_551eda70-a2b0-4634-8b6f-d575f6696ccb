/// Asset manager utility for handling story assets
/// 
/// Manages asset path resolution, fallback handling, and asset loading
/// with proper error handling and default asset support.
library;

import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path/path.dart' as path;

/// Asset manager for story content
class AssetManager {
  // Default asset paths
  static const String defaultImagePath = 'assets/default/default_image.png';
  static const String defaultAudioPath = 'assets/default/happy-outro-8110.mp3';
  
  /// Resolves the full path to a story asset
  /// 
  /// [storyId] - Unique identifier for the story
  /// [assetPath] - Relative path to the asset within the story
  /// [assetType] - Type of asset ('image' or 'audio')
  /// [storyBasePath] - Base path to the decompressed story directory
  /// Returns the resolved asset path or default fallback
  static Future<String> resolveAssetPath(
    String storyId,
    String assetPath,
    String assetType,
    String storyBasePath,
  ) async {
    try {
      // Determine the subdirectory based on asset type
      String subDir;
      switch (assetType.toLowerCase()) {
        case 'image':
          subDir = 'images';
          break;
        case 'audio':
        case 'sound':
          subDir = 'assets';
          break;
        default:
          subDir = 'assets';
      }

      // Construct the full asset path
      final fullAssetPath = path.join(storyBasePath, subDir, assetPath);
      final assetFile = File(fullAssetPath);

      // Check if the asset exists
      if (await assetFile.exists()) {
        return fullAssetPath;
      } else {
        print('Asset not found: $fullAssetPath');
        return await _getDefaultAsset(assetType);
      }
    } catch (e) {
      print('Error resolving asset path: $e');
      return await _getDefaultAsset(assetType);
    }
  }

  /// Gets the default asset path for the given type
  static Future<String> _getDefaultAsset(String assetType) async {
    switch (assetType.toLowerCase()) {
      case 'image':
        return defaultImagePath;
      case 'audio':
      case 'sound':
        return defaultAudioPath;
      default:
        return defaultImagePath;
    }
  }

  /// Checks if an asset exists in the story directory
  /// 
  /// [storyBasePath] - Base path to the decompressed story directory
  /// [assetPath] - Relative path to the asset
  /// [assetType] - Type of asset ('image' or 'audio')
  /// Returns true if the asset exists
  static Future<bool> assetExists(
    String storyBasePath,
    String assetPath,
    String assetType,
  ) async {
    try {
      String subDir;
      switch (assetType.toLowerCase()) {
        case 'image':
          subDir = 'images';
          break;
        case 'audio':
        case 'sound':
          subDir = 'assets';
          break;
        default:
          subDir = 'assets';
      }

      final fullAssetPath = path.join(storyBasePath, subDir, assetPath);
      return await File(fullAssetPath).exists();
    } catch (e) {
      print('Error checking asset existence: $e');
      return false;
    }
  }

  /// Gets all image assets for a story
  /// 
  /// [storyBasePath] - Base path to the decompressed story directory
  /// Returns a list of image file paths
  static Future<List<String>> getStoryImages(String storyBasePath) async {
    try {
      final imagesDir = Directory(path.join(storyBasePath, 'images'));
      if (!await imagesDir.exists()) {
        return [];
      }

      final imageFiles = <String>[];
      await for (final entity in imagesDir.list()) {
        if (entity is File && _isImageFile(entity.path)) {
          imageFiles.add(entity.path);
        }
      }

      return imageFiles;
    } catch (e) {
      print('Error getting story images: $e');
      return [];
    }
  }

  /// Gets all audio assets for a story
  /// 
  /// [storyBasePath] - Base path to the decompressed story directory
  /// Returns a list of audio file paths
  static Future<List<String>> getStoryAudio(String storyBasePath) async {
    try {
      final assetsDir = Directory(path.join(storyBasePath, 'assets'));
      if (!await assetsDir.exists()) {
        return [];
      }

      final audioFiles = <String>[];
      await for (final entity in assetsDir.list()) {
        if (entity is File && _isAudioFile(entity.path)) {
          audioFiles.add(entity.path);
        }
      }

      return audioFiles;
    } catch (e) {
      print('Error getting story audio: $e');
      return [];
    }
  }

  /// Checks if a file is an image based on its extension
  static bool _isImageFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    return ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].contains(extension);
  }

  /// Checks if a file is an audio file based on its extension
  static bool _isAudioFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    return ['.mp3', '.wav', '.aac', '.ogg', '.m4a'].contains(extension);
  }

  /// Validates that all required assets exist for a story
  /// 
  /// [storyBasePath] - Base path to the decompressed story directory
  /// [requiredAssets] - Map of asset paths by type
  /// Returns a map of missing assets by type
  static Future<Map<String, List<String>>> validateStoryAssets(
    String storyBasePath,
    Map<String, List<String>> requiredAssets,
  ) async {
    final missingAssets = <String, List<String>>{};

    for (final entry in requiredAssets.entries) {
      final assetType = entry.key;
      final assetPaths = entry.value;
      final missing = <String>[];

      for (final assetPath in assetPaths) {
        if (!await assetExists(storyBasePath, assetPath, assetType)) {
          missing.add(assetPath);
        }
      }

      if (missing.isNotEmpty) {
        missingAssets[assetType] = missing;
      }
    }

    return missingAssets;
  }

  /// Preloads assets into memory for faster access
  /// 
  /// [assetPaths] - List of asset paths to preload
  /// Returns a map of preloaded asset data
  static Future<Map<String, dynamic>> preloadAssets(List<String> assetPaths) async {
    final preloadedAssets = <String, dynamic>{};

    for (final assetPath in assetPaths) {
      try {
        if (_isImageFile(assetPath)) {
          // For images, we might want to preload them differently
          // This is a placeholder for image preloading logic
          preloadedAssets[assetPath] = assetPath;
        } else if (_isAudioFile(assetPath)) {
          // For audio, we might want to preload them differently
          // This is a placeholder for audio preloading logic
          preloadedAssets[assetPath] = assetPath;
        }
      } catch (e) {
        print('Failed to preload asset $assetPath: $e');
      }
    }

    return preloadedAssets;
  }

  /// Gets the size of all assets for a story
  /// 
  /// [storyBasePath] - Base path to the decompressed story directory
  /// Returns the total size in bytes
  static Future<int> getStoryAssetsSize(String storyBasePath) async {
    try {
      int totalSize = 0;
      final storyDir = Directory(storyBasePath);

      if (!await storyDir.exists()) {
        return 0;
      }

      await for (final entity in storyDir.list(recursive: true)) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
        }
      }

      return totalSize;
    } catch (e) {
      print('Error calculating story assets size: $e');
      return 0;
    }
  }

  /// Cleans up cached assets for a story
  /// 
  /// [storyId] - Unique identifier for the story
  static Future<void> cleanupStoryAssets(String storyId) async {
    try {
      // This would clean up any cached or temporary assets
      // Implementation depends on caching strategy
      print('Cleaning up assets for story: $storyId');
    } catch (e) {
      print('Error cleaning up story assets: $e');
    }
  }

  /// Gets asset metadata
  /// 
  /// [assetPath] - Path to the asset file
  /// Returns metadata map with file information
  static Future<Map<String, dynamic>> getAssetMetadata(String assetPath) async {
    try {
      final file = File(assetPath);
      if (!await file.exists()) {
        return {};
      }

      final stat = await file.stat();
      final extension = path.extension(assetPath).toLowerCase();
      
      return {
        'path': assetPath,
        'name': path.basename(assetPath),
        'extension': extension,
        'size': stat.size,
        'modified': stat.modified.toIso8601String(),
        'type': _isImageFile(assetPath) ? 'image' : 
                _isAudioFile(assetPath) ? 'audio' : 'unknown',
      };
    } catch (e) {
      print('Error getting asset metadata: $e');
      return {};
    }
  }
}
