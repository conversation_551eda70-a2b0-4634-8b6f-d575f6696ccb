/// Vocabulary model for educational content
///
/// Represents vocabulary items that help children learn new words
/// through the story experience.
library;

class VocabularyModel {
  /// The vocabulary word being taught
  final String word;
  
  /// Image filename that illustrates the word
  final String image;
  
  /// Child-friendly explanation of the word
  final String explanation;

  const VocabularyModel({
    required this.word,
    required this.image,
    required this.explanation,
  });

  /// Creates a VocabularyModel from JSON data
  factory VocabularyModel.fromJson(Map<String, dynamic> json) {
    return VocabularyModel(
      word: json['word'] as String,
      image: json['image'] as String,
      explanation: json['explanation'] as String,
    );
  }

  /// Converts VocabularyModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'word': word,
      'image': image,
      'explanation': explanation,
    };
  }

  /// Creates a copy of this VocabularyModel with optional parameter overrides
  VocabularyModel copyWith({
    String? word,
    String? image,
    String? explanation,
  }) {
    return VocabularyModel(
      word: word ?? this.word,
      image: image ?? this.image,
      explanation: explanation ?? this.explanation,
    );
  }

  /// Validates that all required fields are present
  bool isValid() {
    return word.isNotEmpty &&
           image.isNotEmpty &&
           explanation.isNotEmpty;
  }

  @override
  String toString() {
    return 'VocabularyModel(word: $word, image: $image, explanation: $explanation)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VocabularyModel &&
           other.word == word &&
           other.image == image &&
           other.explanation == explanation;
  }

  @override
  int get hashCode {
    return Object.hash(word, image, explanation);
  }
}
