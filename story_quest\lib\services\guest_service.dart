/// Guest user service for managing guest sessions
///
/// Provides functionality for guest users who don't want to create accounts.
/// Guest users have limited features and no progress tracking.
library;

import 'package:shared_preferences/shared_preferences.dart';

/// Guest service for managing guest user sessions
class GuestService {
  static final GuestService _instance = GuestService._internal();
  factory GuestService() => _instance;
  GuestService._internal();

  // Storage keys
  static const String _isGuestKey = 'is_guest_user';
  static const String _guestSessionKey = 'guest_session_id';

  bool _isGuestUser = false;
  String? _guestSessionId;

  /// Gets whether the current user is a guest
  bool get isGuestUser => _isGuestUser;

  /// Gets the guest session ID
  String? get guestSessionId => _guestSessionId;

  /// Initializes the guest service
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isGuestUser = prefs.getBool(_isGuestKey) ?? false;
      _guestSessionId = prefs.getString(_guestSessionKey);
    } catch (e) {
      _isGuestUser = false;
      _guestSessionId = null;
    }
  }

  /// Starts a guest session
  Future<void> startGuestSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionId = DateTime.now().millisecondsSinceEpoch.toString();
      
      _isGuestUser = true;
      _guestSessionId = sessionId;
      
      await prefs.setBool(_isGuestKey, true);
      await prefs.setString(_guestSessionKey, sessionId);
    } catch (e) {
      // Handle error silently
    }
  }

  /// Ends the guest session
  Future<void> endGuestSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      _isGuestUser = false;
      _guestSessionId = null;
      
      await prefs.remove(_isGuestKey);
      await prefs.remove(_guestSessionKey);
    } catch (e) {
      // Handle error silently
    }
  }

  /// Checks if guest users can access a feature
  bool canAccessFeature(String feature) {
    if (!_isGuestUser) return true; // Authenticated users can access everything
    
    // Define features that guests cannot access
    const restrictedFeatures = [
      'progress_tracking',
      'profile_management',
      'badge_system',
      'parental_controls',
      'cloud_sync',
      'favorites',
      'reading_history',
    ];
    
    return !restrictedFeatures.contains(feature);
  }

  /// Gets a user-friendly message for restricted features
  String getRestrictionMessage(String feature) {
    switch (feature) {
      case 'progress_tracking':
        return 'Create an account to track your reading progress!';
      case 'profile_management':
        return 'Create an account to manage child profiles!';
      case 'badge_system':
        return 'Create an account to earn and collect badges!';
      case 'parental_controls':
        return 'Create an account to access parental controls!';
      case 'cloud_sync':
        return 'Create an account to sync your data across devices!';
      case 'favorites':
        return 'Create an account to save your favorite stories!';
      case 'reading_history':
        return 'Create an account to view your reading history!';
      default:
        return 'Create an account to access this feature!';
    }
  }

  /// Gets guest user display name
  String get displayName => 'Guest User';

  /// Gets guest user limitations
  List<String> get limitations => [
    'No progress tracking',
    'No badge collection',
    'No favorite stories',
    'No reading history',
    'No cloud sync',
    'Limited parental features',
  ];

  /// Gets guest user benefits (what they can still do)
  List<String> get benefits => [
    'Read all available stories',
    'Use text-to-speech features',
    'Access story library',
    'Play interactive stories',
    'Use basic app features',
  ];

  /// Clears all guest data
  Future<void> clearGuestData() async {
    await endGuestSession();
    // Clear any temporary guest data if needed
  }
}
