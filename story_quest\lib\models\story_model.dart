import 'story_setup_model.dart';
import 'narrator_profile_model.dart';
import 'character_model.dart';
import 'scene_model.dart';
import 'vocabulary_model.dart';
import 'post_story_model.dart';

/// Main story model containing all story data and configuration
/// 
/// This is the root model that represents a complete interactive story
/// with all its components including setup, characters, scenes, and educational content.

class StoryModel {
  /// Unique identifier for this story
  final String storyId;
  
  /// Target age group (e.g., "3-5", "6-8")
  final String ageGroup;
  
  /// Difficulty level (e.g., "easy", "medium", "hard")
  final String difficulty;
  
  /// Story title
  final String title;
  
  /// Moral or lesson of the story
  final String moral;
  
  /// Cover image filename
  final String coverImage;
  
  /// Estimated reading/play time
  final String estimatedTime;
  
  /// Story setup and environment configuration
  final StorySetupModel setup;
  
  /// Narrator profile and voice configuration
  final NarratorProfileModel narratorProfile;
  
  /// List of story characters
  final List<CharacterModel> characters;
  
  /// List of story scenes in order
  final List<SceneModel> scenes;
  
  /// Vocabulary items for educational content
  final List<VocabularyModel> vocabulary;
  
  /// Post-story content including discussion and feedback
  final PostStoryModel postStory;

  const StoryModel({
    required this.storyId,
    required this.ageGroup,
    required this.difficulty,
    required this.title,
    required this.moral,
    required this.coverImage,
    required this.estimatedTime,
    required this.setup,
    required this.narratorProfile,
    required this.characters,
    required this.scenes,
    required this.vocabulary,
    required this.postStory,
  });

  /// Creates a StoryModel from JSON data
  factory StoryModel.fromJson(Map<String, dynamic> json) {
    return StoryModel(
      storyId: json['story_id'] as String,
      ageGroup: json['age_group'] as String,
      difficulty: json['difficulty'] as String,
      title: json['title'] as String,
      moral: json['moral'] as String,
      coverImage: json['cover_image'] as String,
      estimatedTime: json['estimated_time'] as String,
      setup: StorySetupModel.fromJson(json['setup'] as Map<String, dynamic>),
      narratorProfile: NarratorProfileModel.fromJson(json['narrator_profile'] as Map<String, dynamic>),
      characters: (json['characters'] as List<dynamic>)
          .map((character) => CharacterModel.fromJson(character as Map<String, dynamic>))
          .toList(),
      scenes: (json['scenes'] as List<dynamic>)
          .map((scene) => SceneModel.fromJson(scene as Map<String, dynamic>))
          .toList(),
      vocabulary: (json['vocabulary'] as List<dynamic>)
          .map((vocab) => VocabularyModel.fromJson(vocab as Map<String, dynamic>))
          .toList(),
      postStory: PostStoryModel.fromJson(json['post_story'] as Map<String, dynamic>),
    );
  }

  /// Converts StoryModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'story_id': storyId,
      'age_group': ageGroup,
      'difficulty': difficulty,
      'title': title,
      'moral': moral,
      'cover_image': coverImage,
      'estimated_time': estimatedTime,
      'setup': setup.toJson(),
      'narrator_profile': narratorProfile.toJson(),
      'characters': characters.map((character) => character.toJson()).toList(),
      'scenes': scenes.map((scene) => scene.toJson()).toList(),
      'vocabulary': vocabulary.map((vocab) => vocab.toJson()).toList(),
      'post_story': postStory.toJson(),
    };
  }

  /// Gets a scene by its ID
  SceneModel? getSceneById(String sceneId) {
    try {
      return scenes.firstWhere((scene) => scene.id == sceneId);
    } catch (e) {
      return null;
    }
  }

  /// Gets a character by name
  CharacterModel? getCharacterByName(String name) {
    try {
      return characters.firstWhere((character) => character.name == name);
    } catch (e) {
      return null;
    }
  }

  /// Gets the first scene of the story
  SceneModel? get firstScene => scenes.isNotEmpty ? scenes.first : null;

  /// Calculates total progress weight for the story
  int get totalProgressWeight {
    return scenes.fold(0, (sum, scene) => sum + scene.progressWeight);
  }

  /// Validates that the story has all required components and valid structure
  bool isValid() {
    // Basic field validation
    if (storyId.isEmpty || title.isEmpty || scenes.isEmpty) {
      return false;
    }

    // Validate all components
    if (!setup.isValid() || !narratorProfile.isValid() || !postStory.isValid()) {
      return false;
    }

    // Validate all characters
    if (!characters.every((character) => character.isValid())) {
      return false;
    }

    // Validate all scenes
    if (!scenes.every((scene) => scene.isValid())) {
      return false;
    }

    // Validate all vocabulary items
    if (!vocabulary.every((vocab) => vocab.isValid())) {
      return false;
    }

    // Validate scene connectivity (all scene references should exist)
    final sceneIds = scenes.map((scene) => scene.id).toSet();
    for (final scene in scenes) {
      if (scene.next != null && !sceneIds.contains(scene.next)) {
        return false; // Invalid scene reference
      }
      for (final choice in scene.choices) {
        if (!sceneIds.contains(choice.next)) {
          return false; // Invalid choice reference
        }
      }
    }

    return true;
  }

  @override
  String toString() {
    return 'StoryModel(id: $storyId, title: $title, scenes: ${scenes.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StoryModel && other.storyId == storyId;
  }

  @override
  int get hashCode => storyId.hashCode;

  /// Creates a copy of this story with updated fields
  StoryModel copyWith({
    String? storyId,
    String? ageGroup,
    String? difficulty,
    String? title,
    String? moral,
    String? coverImage,
    String? estimatedTime,
    StorySetupModel? setup,
    NarratorProfileModel? narratorProfile,
    List<CharacterModel>? characters,
    List<SceneModel>? scenes,
    List<VocabularyModel>? vocabulary,
    PostStoryModel? postStory,
  }) {
    return StoryModel(
      storyId: storyId ?? this.storyId,
      ageGroup: ageGroup ?? this.ageGroup,
      difficulty: difficulty ?? this.difficulty,
      title: title ?? this.title,
      moral: moral ?? this.moral,
      coverImage: coverImage ?? this.coverImage,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      setup: setup ?? this.setup,
      narratorProfile: narratorProfile ?? this.narratorProfile,
      characters: characters ?? this.characters,
      scenes: scenes ?? this.scenes,
      vocabulary: vocabulary ?? this.vocabulary,
      postStory: postStory ?? this.postStory,
    );
  }
}
