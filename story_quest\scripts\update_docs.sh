#!/bin/bash

# Documentation update script for Story Quest
# Automatically updates docs/script_changes.md with build information

# Get current timestamp
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# Get current git commit hash (if available)
if command -v git &> /dev/null && git rev-parse --git-dir > /dev/null 2>&1; then
    COMMIT_HASH=$(git rev-parse --short HEAD 2>/dev/null || echo "N/A")
    BRANCH=$(git branch --show-current 2>/dev/null || echo "N/A")
else
    COMMIT_HASH="N/A"
    BRANCH="N/A"
fi

# Create docs directory if it doesn't exist
mkdir -p docs

# Initialize script_changes.md if it doesn't exist
if [ ! -f "docs/script_changes.md" ]; then
    cat > docs/script_changes.md << EOF
# Script Changes Log

This file tracks changes made to the Story Quest project during builds and deployments.

---

EOF
fi

# Function to add entry to script_changes.md
add_entry() {
    local script_name="$1"
    local summary="$2"
    local details="$3"
    
    cat >> docs/script_changes.md << EOF
## Build Entry - $TIMESTAMP

**Script:** $script_name  
**Branch:** $BRANCH  
**Commit:** $COMMIT_HASH  
**Summary:** $summary

### Details
$details

### Modified Files
EOF

    # List recently modified Dart files
    if command -v find &> /dev/null; then
        echo "Recent Dart file changes:" >> docs/script_changes.md
        find lib -name "*.dart" -mtime -1 2>/dev/null | head -10 | while read file; do
            echo "- $file" >> docs/script_changes.md
        done
    fi

    echo "" >> docs/script_changes.md
    echo "---" >> docs/script_changes.md
    echo "" >> docs/script_changes.md
}

# Main execution
case "$1" in
    "build")
        add_entry "Flutter Build" "Application build completed" "Standard Flutter build process executed successfully."
        ;;
    "test")
        add_entry "Test Run" "Unit tests executed" "Flutter test suite completed. Check test results for details."
        ;;
    "deploy")
        add_entry "Deployment" "Application deployed" "Application has been deployed to target environment."
        ;;
    "init")
        add_entry "Initialization" "Project initialized" "Story Quest project setup and dependencies configured."
        ;;
    *)
        add_entry "Manual Update" "Documentation updated manually" "Manual documentation update triggered."
        ;;
esac

echo "Documentation updated: docs/script_changes.md"
echo "Timestamp: $TIMESTAMP"
echo "Commit: $COMMIT_HASH"
