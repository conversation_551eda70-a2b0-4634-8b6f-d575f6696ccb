/// Badge model for reward system
///
/// Represents achievements and rewards earned through story choices
/// and moral decisions. Supports different badge types and rarity levels.
library;

/// Badge types representing different virtues and achievements
enum BadgeType {
  kindness('Kindness', 'Acts of kindness and compassion', '💝'),
  bravery('Bravery', 'Courageous actions and facing fears', '🦁'),
  honesty('Honesty', 'Truthfulness and integrity', '✨'),
  friendship('Friendship', 'Building and maintaining friendships', '🤝'),
  helpfulness('Helpfulness', 'Helping others in need', '🤲'),
  creativity('Creativity', 'Creative thinking and problem solving', '🎨'),
  perseverance('Perseverance', 'Not giving up when things get tough', '💪'),
  empathy('Empathy', 'Understanding and caring for others', '❤️'),
  responsibility('Responsibility', 'Taking care of duties and commitments', '🛡️'),
  wisdom('Wisdom', 'Making good decisions and learning from mistakes', '🦉');

  const BadgeType(this.displayName, this.description, this.emoji);
  final String displayName;
  final String description;
  final String emoji;
}

/// Badge rarity levels
enum BadgeRarity {
  common('Common', 1),
  uncommon('Uncommon', 2),
  rare('Rare', 3),
  epic('Epic', 4),
  legendary('Legend<PERSON>', 5);

  const BadgeRarity(this.displayName, this.level);
  final String displayName;
  final int level;
}

/// Badge model class
class BadgeModel {
  /// Unique identifier for this badge
  final String id;
  
  /// Type of badge (virtue/achievement category)
  final BadgeType type;
  
  /// Rarity level of the badge
  final BadgeRarity rarity;
  
  /// Specific name of this badge
  final String name;
  
  /// Description of how this badge was earned
  final String description;
  
  /// Story ID where this badge was earned
  final String storyId;
  
  /// Scene ID where the choice was made
  final String sceneId;
  
  /// Choice that led to earning this badge
  final String choiceText;
  
  /// Timestamp when badge was earned
  final DateTime earnedAt;
  
  /// Whether this badge has been viewed by the user
  final bool isNew;

  const BadgeModel({
    required this.id,
    required this.type,
    required this.rarity,
    required this.name,
    required this.description,
    required this.storyId,
    required this.sceneId,
    required this.choiceText,
    required this.earnedAt,
    this.isNew = true,
  });

  /// Creates a BadgeModel from JSON data
  factory BadgeModel.fromJson(Map<String, dynamic> json) {
    return BadgeModel(
      id: json['id'] as String,
      type: BadgeType.values.firstWhere(
        (type) => type.name == json['type'],
        orElse: () => BadgeType.kindness,
      ),
      rarity: BadgeRarity.values.firstWhere(
        (rarity) => rarity.name == json['rarity'],
        orElse: () => BadgeRarity.common,
      ),
      name: json['name'] as String,
      description: json['description'] as String,
      storyId: json['story_id'] as String,
      sceneId: json['scene_id'] as String,
      choiceText: json['choice_text'] as String,
      earnedAt: DateTime.parse(json['earned_at'] as String),
      isNew: json['is_new'] as bool? ?? true,
    );
  }

  /// Converts BadgeModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'rarity': rarity.name,
      'name': name,
      'description': description,
      'story_id': storyId,
      'scene_id': sceneId,
      'choice_text': choiceText,
      'earned_at': earnedAt.toIso8601String(),
      'is_new': isNew,
    };
  }

  /// Creates a copy with optional parameter overrides
  BadgeModel copyWith({
    String? id,
    BadgeType? type,
    BadgeRarity? rarity,
    String? name,
    String? description,
    String? storyId,
    String? sceneId,
    String? choiceText,
    DateTime? earnedAt,
    bool? isNew,
  }) {
    return BadgeModel(
      id: id ?? this.id,
      type: type ?? this.type,
      rarity: rarity ?? this.rarity,
      name: name ?? this.name,
      description: description ?? this.description,
      storyId: storyId ?? this.storyId,
      sceneId: sceneId ?? this.sceneId,
      choiceText: choiceText ?? this.choiceText,
      earnedAt: earnedAt ?? this.earnedAt,
      isNew: isNew ?? this.isNew,
    );
  }

  /// Gets the color associated with this badge's rarity
  int get rarityColor {
    switch (rarity) {
      case BadgeRarity.common:
        return 0xFF9E9E9E; // Grey
      case BadgeRarity.uncommon:
        return 0xFF4CAF50; // Green
      case BadgeRarity.rare:
        return 0xFF2196F3; // Blue
      case BadgeRarity.epic:
        return 0xFF9C27B0; // Purple
      case BadgeRarity.legendary:
        return 0xFFFF9800; // Orange/Gold
    }
  }

  /// Gets the display emoji for this badge
  String get displayEmoji => type.emoji;

  /// Validates that all required fields are present
  bool isValid() {
    return id.isNotEmpty &&
           name.isNotEmpty &&
           description.isNotEmpty &&
           storyId.isNotEmpty &&
           sceneId.isNotEmpty &&
           choiceText.isNotEmpty;
  }

  @override
  String toString() {
    return 'BadgeModel(id: $id, type: ${type.displayName}, name: $name, rarity: ${rarity.displayName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BadgeModel &&
           other.id == id &&
           other.type == type &&
           other.rarity == rarity &&
           other.name == name;
  }

  @override
  int get hashCode {
    return Object.hash(id, type, rarity, name);
  }
}

/// Badge criteria for determining when badges should be awarded
class BadgeCriteria {
  /// Maps choice keywords to badge types
  static const Map<String, BadgeType> choiceKeywords = {
    // Kindness keywords
    'help': BadgeType.kindness,
    'share': BadgeType.kindness,
    'care': BadgeType.kindness,
    'comfort': BadgeType.kindness,
    'gentle': BadgeType.kindness,
    
    // Bravery keywords
    'brave': BadgeType.bravery,
    'courage': BadgeType.bravery,
    'face': BadgeType.bravery,
    'stand up': BadgeType.bravery,
    'protect': BadgeType.bravery,
    
    // Honesty keywords
    'truth': BadgeType.honesty,
    'honest': BadgeType.honesty,
    'tell': BadgeType.honesty,
    'admit': BadgeType.honesty,
    
    // Friendship keywords
    'friend': BadgeType.friendship,
    'together': BadgeType.friendship,
    'include': BadgeType.friendship,
    'invite': BadgeType.friendship,
    
    // Helpfulness keywords
    'assist': BadgeType.helpfulness,
    'support': BadgeType.helpfulness,
    'aid': BadgeType.helpfulness,
    'volunteer': BadgeType.helpfulness,
  };

  /// Determines badge type based on choice text
  static BadgeType? determineBadgeType(String choiceText) {
    final lowerChoice = choiceText.toLowerCase();
    
    for (final entry in choiceKeywords.entries) {
      if (lowerChoice.contains(entry.key)) {
        return entry.value;
      }
    }
    
    return null;
  }

  /// Determines badge rarity based on story difficulty and choice complexity
  static BadgeRarity determineBadgeRarity(String storyDifficulty, String choiceText) {
    final choiceLength = choiceText.split(' ').length;
    
    switch (storyDifficulty.toLowerCase()) {
      case 'easy':
        return choiceLength > 8 ? BadgeRarity.uncommon : BadgeRarity.common;
      case 'medium':
        return choiceLength > 10 ? BadgeRarity.rare : BadgeRarity.uncommon;
      case 'hard':
        return choiceLength > 12 ? BadgeRarity.epic : BadgeRarity.rare;
      default:
        return BadgeRarity.common;
    }
  }

  /// Creates a badge name based on type and context
  static String createBadgeName(BadgeType type, String storyTitle) {
    switch (type) {
      case BadgeType.kindness:
        return 'Heart of Gold';
      case BadgeType.bravery:
        return 'Brave Heart';
      case BadgeType.honesty:
        return 'Truth Teller';
      case BadgeType.friendship:
        return 'True Friend';
      case BadgeType.helpfulness:
        return 'Helping Hand';
      case BadgeType.creativity:
        return 'Creative Mind';
      case BadgeType.perseverance:
        return 'Never Give Up';
      case BadgeType.empathy:
        return 'Caring Soul';
      case BadgeType.responsibility:
        return 'Reliable One';
      case BadgeType.wisdom:
        return 'Wise Choice';
    }
  }

  /// Creates a badge description based on the choice made
  static String createBadgeDescription(BadgeType type, String choiceText) {
    switch (type) {
      case BadgeType.kindness:
        return 'Showed kindness by choosing to $choiceText';
      case BadgeType.bravery:
        return 'Demonstrated bravery by choosing to $choiceText';
      case BadgeType.honesty:
        return 'Chose honesty by deciding to $choiceText';
      case BadgeType.friendship:
        return 'Built friendship by choosing to $choiceText';
      case BadgeType.helpfulness:
        return 'Helped others by choosing to $choiceText';
      case BadgeType.creativity:
        return 'Used creativity by choosing to $choiceText';
      case BadgeType.perseverance:
        return 'Showed perseverance by choosing to $choiceText';
      case BadgeType.empathy:
        return 'Showed empathy by choosing to $choiceText';
      case BadgeType.responsibility:
        return 'Took responsibility by choosing to $choiceText';
      case BadgeType.wisdom:
        return 'Made a wise choice by deciding to $choiceText';
    }
  }
}
