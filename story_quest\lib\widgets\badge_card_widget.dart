/// Badge card widget for displaying individual badges
///
/// Shows badge information with animations, rarity indicators,
/// and interactive details in an engaging card format.
library;

import 'package:flutter/material.dart';
import '../models/badge_model.dart';
import '../utils/accessibility_helper.dart';

/// Badge card widget for displaying individual badges
class BadgeCardWidget extends StatefulWidget {
  /// The badge to display
  final BadgeModel badge;
  
  /// Animation delay for staggered animations
  final Duration animationDelay;
  
  /// Whether the badge is newly earned
  final bool isNew;

  const BadgeCardWidget({
    super.key,
    required this.badge,
    this.animationDelay = Duration.zero,
    this.isNew = false,
  });

  @override
  State<BadgeCardWidget> createState() => _BadgeCardWidgetState();
}

class _BadgeCardWidgetState extends State<BadgeCardWidget>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _shimmerController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shimmerAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize scale animation
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    // Initialize shimmer animation for new badges
    _shimmerController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _shimmerAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _shimmerController,
      curve: Curves.easeInOut,
    ));

    // Start animations with delay
    Future.delayed(widget.animationDelay, () {
      if (mounted) {
        _scaleController.forward();
        if (widget.isNew) {
          _shimmerController.repeat(reverse: true);
        }
      }
    });
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _shimmerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: _buildCard(),
        );
      },
    );
  }

  /// Builds the main card
  Widget _buildCard() {
    return AccessibilityHelper.createAccessibleButton(
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        transform: Matrix4.identity()..scale(_isPressed ? 0.95 : 1.0),
        child: Container(
          decoration: BoxDecoration(
            gradient: _buildCardGradient(),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Color(widget.badge.rarityColor),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Color(widget.badge.rarityColor).withValues(alpha: 0.3),
                blurRadius: 12,
                offset: const Offset(0, 6),
              ),
              if (widget.isNew)
                BoxShadow(
                  color: const Color(0xFFFFCA28).withValues(alpha: 0.4),
                  blurRadius: 20,
                  spreadRadius: 2,
                ),
            ],
          ),
          child: Stack(
            children: [
              // Shimmer effect for new badges
              if (widget.isNew) _buildShimmerEffect(),
              
              // Main content
              _buildCardContent(),
              
              // New badge indicator
              if (widget.isNew) _buildNewBadgeIndicator(),
              
              // Rarity indicator
              _buildRarityIndicator(),
            ],
          ),
        ),
      ),
      onPressed: _showBadgeDetails,
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) => setState(() => _isPressed = false),
      onTapCancel: () => setState(() => _isPressed = false),
      semanticLabel: 'Badge: ${widget.badge.name}, ${widget.badge.rarity.displayName} rarity, earned for ${widget.badge.type.displayName}',
    );
  }

  /// Builds the card gradient based on rarity
  Gradient _buildCardGradient() {
    final baseColor = Color(widget.badge.rarityColor);
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        Colors.white,
        baseColor.withValues(alpha: 0.1),
        baseColor.withValues(alpha: 0.2),
      ],
    );
  }

  /// Builds shimmer effect for new badges
  Widget _buildShimmerEffect() {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(14),
              gradient: LinearGradient(
                begin: Alignment(-1.0 + 2.0 * _shimmerAnimation.value, 0.0),
                end: Alignment(1.0 + 2.0 * _shimmerAnimation.value, 0.0),
                colors: [
                  Colors.transparent,
                  Colors.white.withValues(alpha: 0.3),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Builds the main card content
  Widget _buildCardContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Badge emoji
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Color(widget.badge.rarityColor).withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                widget.badge.displayEmoji,
                style: const TextStyle(fontSize: 32),
              ),
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Badge name
          AccessibilityHelper.createAccessibleText(
            widget.badge.name,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(widget.badge.rarityColor),
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
          ),
          
          const SizedBox(height: 4),
          
          // Badge type
          AccessibilityHelper.createAccessibleText(
            widget.badge.type.displayName,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF757575),
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 8),
          
          // Earned date
          AccessibilityHelper.createAccessibleText(
            _formatDate(widget.badge.earnedAt),
            style: const TextStyle(
              fontSize: 10,
              color: Color(0xFF9E9E9E),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Builds new badge indicator
  Widget _buildNewBadgeIndicator() {
    return Positioned(
      top: 8,
      left: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: const Color(0xFFFF5722),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Text(
          'NEW',
          style: TextStyle(
            color: Colors.white,
            fontSize: 8,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// Builds rarity indicator
  Widget _buildRarityIndicator() {
    return Positioned(
      top: 8,
      right: 8,
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          color: Color(widget.badge.rarityColor),
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Text(
            widget.badge.rarity.level.toString(),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  /// Shows detailed badge information
  void _showBadgeDetails() {
    AccessibilityHelper.provideHapticFeedback();
    
    showDialog(
      context: context,
      builder: (context) => _BadgeDetailDialog(badge: widget.badge),
    );
  }

  /// Formats date for display
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

/// Badge detail dialog
class _BadgeDetailDialog extends StatelessWidget {
  final BadgeModel badge;

  const _BadgeDetailDialog({required this.badge});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white,
              Color(badge.rarityColor).withValues(alpha: 0.1),
            ],
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Badge display
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Color(badge.rarityColor).withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  badge.displayEmoji,
                  style: const TextStyle(fontSize: 40),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Badge name and rarity
            Text(
              badge.name,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(badge.rarityColor),
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 8),
            
            Text(
              '${badge.rarity.displayName} ${badge.type.displayName}',
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF757575),
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 16),
            
            // Description
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Color(badge.rarityColor).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                badge.description,
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF2E7D32),
                ),
                textAlign: TextAlign.center,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Earned info
            Text(
              'Earned on ${badge.earnedAt.day}/${badge.earnedAt.month}/${badge.earnedAt.year}',
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF9E9E9E),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Close button
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(badge.rarityColor),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Close'),
            ),
          ],
        ),
      ),
    );
  }
}
