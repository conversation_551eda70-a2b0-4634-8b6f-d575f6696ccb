/// Voice configuration model for TTS settings
///
/// Represents voice settings used by both narrator and character profiles.
/// Supports JSON serialization for data persistence and API communication.
library;

class VoiceModel {
  /// The voice name/identifier for TTS engine
  final String name;
  
  /// Voice pitch (0.5 to 2.0, where 1.0 is normal)
  final double pitch;
  
  /// Speech rate (0.1 to 3.0, where 1.0 is normal)
  final double rate;
  
  /// Voice volume (0.0 to 1.0, where 1.0 is maximum)
  final double volume;

  const VoiceModel({
    required this.name,
    required this.pitch,
    required this.rate,
    required this.volume,
  });

  /// Creates a VoiceModel from JSON data
  factory VoiceModel.fromJson(Map<String, dynamic> json) {
    return VoiceModel(
      name: json['name'] as String,
      pitch: (json['pitch'] as num).toDouble(),
      rate: (json['rate'] as num).toDouble(),
      volume: (json['volume'] as num).toDouble(),
    );
  }

  /// Converts VoiceModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'pitch': pitch,
      'rate': rate,
      'volume': volume,
    };
  }

  /// Creates a copy of this VoiceModel with optional parameter overrides
  VoiceModel copyWith({
    String? name,
    double? pitch,
    double? rate,
    double? volume,
  }) {
    return VoiceModel(
      name: name ?? this.name,
      pitch: pitch ?? this.pitch,
      rate: rate ?? this.rate,
      volume: volume ?? this.volume,
    );
  }

  /// Validates voice parameters are within acceptable ranges
  bool isValid() {
    return pitch >= 0.5 && pitch <= 2.0 &&
           rate >= 0.1 && rate <= 3.0 &&
           volume >= 0.0 && volume <= 1.0 &&
           name.isNotEmpty;
  }

  @override
  String toString() {
    return 'VoiceModel(name: $name, pitch: $pitch, rate: $rate, volume: $volume)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VoiceModel &&
           other.name == name &&
           other.pitch == pitch &&
           other.rate == rate &&
           other.volume == volume;
  }

  @override
  int get hashCode {
    return Object.hash(name, pitch, rate, volume);
  }
}
