/// Parent Zone screen for parental controls and monitoring
///
/// Provides secure access to parental features including settings,
/// child progress monitoring, and feedback options with PIN authentication.
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../services/service_locator.dart';
import '../../services/badge_service.dart';
import '../../utils/accessibility_helper.dart';
import '../parent_settings/parent_settings_screen.dart';
import '../feedback/feedback_screen.dart';

/// Parent Zone screen widget
class ParentZoneScreen extends StatefulWidget {
  /// Child user ID for progress monitoring
  final String? childUserId;

  const ParentZoneScreen({
    super.key,
    this.childUserId,
  });

  @override
  State<ParentZoneScreen> createState() => _ParentZoneScreenState();
}

class _ParentZoneScreenState extends State<ParentZoneScreen>
    with TickerProviderStateMixin {
  final BadgeService _badgeService = getIt<BadgeService>();
  
  bool _isAuthenticated = false;
  bool _isAuthenticating = false;
  String _pin = '';
  String _error = '';
  Map<String, dynamic> _childStats = {};
  
  late AnimationController _slideController;
  late AnimationController _cardController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _cardAnimation;

  // Default PIN - in production this should be stored securely
  static const String _defaultPin = '1234';

  @override
  void initState() {
    super.initState();
    
    // Initialize animations
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _cardController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _cardAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _cardController,
      curve: Curves.easeOutBack,
    ));

    _slideController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _cardController.dispose();
    super.dispose();
  }

  /// Loads child statistics if authenticated
  Future<void> _loadChildStats() async {
    if (widget.childUserId != null) {
      try {
        final stats = await _badgeService.getBadgeStats(widget.childUserId!);
        setState(() {
          _childStats = stats;
        });
      } catch (e) {
        // Handle error silently for now
      }
    }
  }

  /// Handles PIN authentication
  Future<void> _authenticatePin() async {
    if (_pin.length != 4) return;
    
    setState(() {
      _isAuthenticating = true;
      _error = '';
    });

    // Simulate authentication delay
    await Future.delayed(const Duration(milliseconds: 500));

    if (_pin == _defaultPin) {
      setState(() {
        _isAuthenticated = true;
        _isAuthenticating = false;
      });
      
      // Start card animations
      _cardController.forward();
      
      // Load child stats
      await _loadChildStats();
      
      AccessibilityHelper.provideHapticFeedback();
    } else {
      setState(() {
        _error = 'Incorrect PIN. Please try again.';
        _pin = '';
        _isAuthenticating = false;
      });
      
      // Vibrate for error
      HapticFeedback.heavyImpact();
    }
  }

  /// Adds digit to PIN
  void _addDigit(String digit) {
    if (_pin.length < 4) {
      setState(() {
        _pin += digit;
        _error = '';
      });
      
      AccessibilityHelper.provideHapticFeedback();
      
      if (_pin.length == 4) {
        _authenticatePin();
      }
    }
  }

  /// Removes last digit from PIN
  void _removeDigit() {
    if (_pin.isNotEmpty) {
      setState(() {
        _pin = _pin.substring(0, _pin.length - 1);
        _error = '';
      });
      
      AccessibilityHelper.provideHapticFeedback();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A237E), // Deep blue
              Color(0xFF3F51B5), // Indigo
            ],
          ),
        ),
        child: SafeArea(
          child: SlideTransition(
            position: _slideAnimation,
            child: _isAuthenticated ? _buildParentDashboard() : _buildPinEntry(),
          ),
        ),
      ),
    );
  }

  /// Builds PIN entry interface
  Widget _buildPinEntry() {
    return Column(
      children: [
        _buildPinHeader(),
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildPinDisplay(),
                const SizedBox(height: 40),
                _buildPinKeypad(),
                if (_error.isNotEmpty) ...[
                  const SizedBox(height: 20),
                  _buildErrorMessage(),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Builds PIN entry header
  Widget _buildPinHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // Back button
          AccessibilityHelper.createAccessibleButton(
            child: Container(
              width: 44,
              height: 44,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Colors.white,
              ),
            ),
            onPressed: () => Navigator.of(context).pop(),
            semanticLabel: 'Go back',
          ),
          
          const SizedBox(width: 16),
          
          // Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AccessibilityHelper.createAccessibleText(
                  'Parent Zone',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                AccessibilityHelper.createAccessibleText(
                  'Enter your PIN to continue',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          
          // Lock icon
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.lock,
              color: Colors.white,
              size: 28,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds PIN display dots
  Widget _buildPinDisplay() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 40),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(4, (index) {
          final isFilled = index < _pin.length;
          return AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: isFilled ? Colors.white : Colors.transparent,
              border: Border.all(color: Colors.white, width: 2),
              shape: BoxShape.circle,
            ),
          );
        }),
      ),
    );
  }

  /// Builds PIN keypad
  Widget _buildPinKeypad() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 40),
      child: Column(
        children: [
          // Numbers 1-3
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: ['1', '2', '3'].map((digit) => _buildKeypadButton(digit)).toList(),
          ),
          const SizedBox(height: 20),
          
          // Numbers 4-6
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: ['4', '5', '6'].map((digit) => _buildKeypadButton(digit)).toList(),
          ),
          const SizedBox(height: 20),
          
          // Numbers 7-9
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: ['7', '8', '9'].map((digit) => _buildKeypadButton(digit)).toList(),
          ),
          const SizedBox(height: 20),
          
          // 0 and backspace
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              const SizedBox(width: 60), // Empty space
              _buildKeypadButton('0'),
              _buildBackspaceButton(),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds individual keypad button
  Widget _buildKeypadButton(String digit) {
    return AccessibilityHelper.createAccessibleButton(
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
        ),
        child: Center(
          child: Text(
            digit,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ),
      onPressed: () => _addDigit(digit),
      semanticLabel: 'PIN digit $digit',
    );
  }

  /// Builds backspace button
  Widget _buildBackspaceButton() {
    return AccessibilityHelper.createAccessibleButton(
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
        ),
        child: const Icon(
          Icons.backspace,
          color: Colors.white,
          size: 24,
        ),
      ),
      onPressed: _removeDigit,
      semanticLabel: 'Delete PIN digit',
    );
  }

  /// Builds error message
  Widget _buildErrorMessage() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 40),
      child: Text(
        _error,
        style: const TextStyle(
          color: Colors.redAccent,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// Builds parent dashboard after authentication
  Widget _buildParentDashboard() {
    return Column(
      children: [
        _buildDashboardHeader(),
        Expanded(
          child: AnimatedBuilder(
            animation: _cardAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _cardAnimation.value,
                child: Opacity(
                  opacity: _cardAnimation.value,
                  child: _buildDashboardContent(),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// Builds dashboard header
  Widget _buildDashboardHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // Back button
          AccessibilityHelper.createAccessibleButton(
            child: Container(
              width: 44,
              height: 44,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Colors.white,
              ),
            ),
            onPressed: () => Navigator.of(context).pop(),
            semanticLabel: 'Go back',
          ),
          
          const SizedBox(width: 16),
          
          // Title and welcome
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AccessibilityHelper.createAccessibleText(
                  'Parent Dashboard',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                AccessibilityHelper.createAccessibleText(
                  'Manage your child\'s experience',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          
          // Settings icon
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.admin_panel_settings,
              color: Colors.white,
              size: 28,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds dashboard content
  Widget _buildDashboardContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          if (widget.childUserId != null) _buildChildProgress(),
          const SizedBox(height: 20),
          _buildParentActions(),
        ],
      ),
    );
  }

  /// Builds child progress section
  Widget _buildChildProgress() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.child_care,
                color: Color(0xFF4CAF50),
                size: 28,
              ),
              const SizedBox(width: 12),
              const Text(
                'Child Progress',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2E7D32),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Progress stats
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildProgressStat('Badges', _childStats['total']?.toString() ?? '0', Icons.emoji_events),
              _buildProgressStat('Stories', '5', Icons.book), // Placeholder
              _buildProgressStat('Hours', '2.5', Icons.access_time), // Placeholder
            ],
          ),
        ],
      ),
    );
  }

  /// Builds individual progress stat
  Widget _buildProgressStat(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: const Color(0xFF4CAF50), size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2E7D32),
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF757575),
          ),
        ),
      ],
    );
  }

  /// Builds parent action buttons
  Widget _buildParentActions() {
    return Column(
      children: [
        _buildActionCard(
          'Settings',
          'Adjust app preferences and controls',
          Icons.settings,
          const Color(0xFF2196F3),
          () => Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const ParentSettingsScreen()),
          ),
        ),
        
        const SizedBox(height: 16),
        
        _buildActionCard(
          'Feedback',
          'Share your thoughts and suggestions',
          Icons.feedback,
          const Color(0xFFFF9800),
          () => Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const FeedbackScreen()),
          ),
        ),
        
        const SizedBox(height: 16),
        
        _buildActionCard(
          'Help & Support',
          'Get help and find answers',
          Icons.help,
          const Color(0xFF9C27B0),
          () {
            // TODO: Implement help screen
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Help & Support coming soon!')),
            );
          },
        ),
      ],
    );
  }

  /// Builds individual action card
  Widget _buildActionCard(String title, String subtitle, IconData icon, Color color, VoidCallback onTap) {
    return AccessibilityHelper.createAccessibleButton(
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color, size: 28),
            ),
            
            const SizedBox(width: 16),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2E7D32),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF757575),
                    ),
                  ),
                ],
              ),
            ),
            
            Icon(
              Icons.arrow_forward_ios,
              color: color,
              size: 20,
            ),
          ],
        ),
      ),
      onPressed: onTap,
      semanticLabel: '$title: $subtitle',
    );
  }
}
