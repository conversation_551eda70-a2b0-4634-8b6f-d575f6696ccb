/// Story completion popup widget
///
/// Displays congratulatory message and options when a story is completed
/// with celebration animations and child-friendly design.
library;

import 'package:flutter/material.dart';
import '../models/story_model.dart';
import '../utils/device_utils.dart';

// Type alias for consistency
typedef Story = StoryModel;

/// Popup widget for story completion celebration
class CompletionPopupWidget extends StatefulWidget {
  /// The completed story
  final Story story;
  
  /// Callback when user chooses to replay the story
  final VoidCallback onReplay;
  
  /// Callback when user chooses to exit to library
  final VoidCallback onExit;
  
  /// Optional completion message
  final String? completionMessage;
  
  /// Whether to show replay option
  final bool showReplayOption;

  const CompletionPopupWidget({
    super.key,
    required this.story,
    required this.onReplay,
    required this.onExit,
    this.completionMessage,
    this.showReplayOption = true,
  });

  @override
  State<CompletionPopupWidget> createState() => _CompletionPopupWidgetState();
}

class _CompletionPopupWidgetState extends State<CompletionPopupWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _celebrationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _animationController.forward();
    _startCelebrationAnimation();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _celebrationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _celebrationController,
      curve: Curves.linear,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.bounceOut,
    ));
  }

  void _startCelebrationAnimation() {
    _celebrationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _celebrationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Dialog(
              backgroundColor: Colors.transparent,
              child: _buildCompletionDialog(context),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCompletionDialog(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.9,
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFE8F5E8), // Light green
            Color(0xFFF1F8E9), // Very light green
          ],
        ),
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(context),
          Flexible(
            child: _buildContent(context),
          ),
          _buildActions(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(DeviceUtils.getResponsiveSpacing(context, 20)),
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF4CAF50), Color(0xFF66BB6A)],
          ),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Column(
          children: [
            // Celebration stars
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(5, (index) {
                return AnimatedBuilder(
                  animation: _rotationAnimation,
                  builder: (context, child) {
                    return Transform.rotate(
                      angle: _rotationAnimation.value * 2 * 3.14159,
                      child: Icon(
                        Icons.star,
                        color: const Color(0xFFFFD700),
                        size: DeviceUtils.getResponsiveIconSize(context, 24),
                      ),
                    );
                  },
                );
              }),
            ),
            
            SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 12)),
            
            // Trophy icon
            Icon(
              Icons.emoji_events,
              color: const Color(0xFFFFD700),
              size: DeviceUtils.getResponsiveIconSize(context, 48),
            ),
            
            SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 8)),
            
            Text(
              'Story Complete!',
              style: TextStyle(
                fontSize: DeviceUtils.getResponsiveFontSize(context, 24),
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            
            SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 4)),
            
            Text(
              'Congratulations!',
              style: TextStyle(
                fontSize: DeviceUtils.getResponsiveFontSize(context, 16),
                color: Colors.white.withValues(alpha: 0.9),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(DeviceUtils.getResponsiveSpacing(context, 20)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Story title
          Text(
            'You finished "${widget.story.title}"',
            style: TextStyle(
              fontSize: DeviceUtils.getResponsiveFontSize(context, 18),
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2E7D32),
            ),
            textAlign: TextAlign.center,
          ),
          
          SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 16)),
          
          // Completion message
          Container(
            padding: EdgeInsets.all(DeviceUtils.getResponsiveSpacing(context, 16)),
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
              ),
            ),
            child: Text(
              widget.completionMessage ?? 
              'You did a great job reading this story! You made some wonderful choices along the way.',
              style: TextStyle(
                fontSize: DeviceUtils.getResponsiveFontSize(context, 14),
                color: const Color(0xFF2E7D32),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
          SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 16)),
          
          // Achievement badges
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildAchievementBadge(
                context,
                Icons.menu_book,
                'Story Reader',
                const Color(0xFF2196F3),
              ),
              _buildAchievementBadge(
                context,
                Icons.psychology,
                'Good Choices',
                const Color(0xFFFF9800),
              ),
              _buildAchievementBadge(
                context,
                Icons.favorite,
                'Kind Heart',
                const Color(0xFFE91E63),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementBadge(
    BuildContext context,
    IconData icon,
    String label,
    Color color,
  ) {
    return Column(
      children: [
        Container(
          width: DeviceUtils.getResponsiveIconSize(context, 40),
          height: DeviceUtils.getResponsiveIconSize(context, 40),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            shape: BoxShape.circle,
            border: Border.all(color: color, width: 2),
          ),
          child: Icon(
            icon,
            color: color,
            size: DeviceUtils.getResponsiveIconSize(context, 20),
          ),
        ),
        SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 4)),
        Text(
          label,
          style: TextStyle(
            fontSize: DeviceUtils.getResponsiveFontSize(context, 10),
            fontWeight: FontWeight.w600,
            color: const Color(0xFF2E7D32),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildActions(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(DeviceUtils.getResponsiveSpacing(context, 20)),
      child: Column(
        children: [
          if (widget.showReplayOption) ...[
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _replay,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4CAF50),
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    vertical: DeviceUtils.getResponsiveSpacing(context, 16),
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                  elevation: 4,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.replay,
                      size: DeviceUtils.getResponsiveIconSize(context, 20),
                    ),
                    SizedBox(width: DeviceUtils.getResponsiveSpacing(context, 8)),
                    Text(
                      'Read Again',
                      style: TextStyle(
                        fontSize: DeviceUtils.getResponsiveFontSize(context, 16),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 12)),
          ],
          
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: _exit,
              style: OutlinedButton.styleFrom(
                foregroundColor: const Color(0xFF4CAF50),
                side: const BorderSide(color: Color(0xFF4CAF50), width: 2),
                padding: EdgeInsets.symmetric(
                  vertical: DeviceUtils.getResponsiveSpacing(context, 16),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.library_books,
                    size: DeviceUtils.getResponsiveIconSize(context, 20),
                  ),
                  SizedBox(width: DeviceUtils.getResponsiveSpacing(context, 8)),
                  Text(
                    'Choose New Story',
                    style: TextStyle(
                      fontSize: DeviceUtils.getResponsiveFontSize(context, 16),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _replay() {
    _animationController.reverse().then((_) {
      widget.onReplay();
    });
  }

  void _exit() {
    _animationController.reverse().then((_) {
      widget.onExit();
    });
  }
}

/// Shows a story completion popup dialog
Future<void> showCompletionPopup({
  required BuildContext context,
  required Story story,
  required VoidCallback onReplay,
  required VoidCallback onExit,
  String? completionMessage,
  bool showReplayOption = true,
}) {
  return showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) => CompletionPopupWidget(
      story: story,
      onReplay: onReplay,
      onExit: onExit,
      completionMessage: completionMessage,
      showReplayOption: showReplayOption,
    ),
  );
}
