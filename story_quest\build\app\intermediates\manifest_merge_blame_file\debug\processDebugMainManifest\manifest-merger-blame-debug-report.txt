1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.story_quest"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
7-->D:\AliM Studio\App 01\App\story_quest\android\app\src\main\AndroidManifest.xml:2:5-74
8        android:minSdkVersion="23"
8-->D:\AliM Studio\App 01\App\story_quest\android\app\src\main\AndroidManifest.xml:2:15-41
9        android:targetSdkVersion="35" />
9-->D:\AliM Studio\App 01\App\story_quest\android\app\src\main\AndroidManifest.xml:2:42-71
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\AliM Studio\App 01\App\story_quest\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\AliM Studio\App 01\App\story_quest\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->D:\AliM Studio\App 01\App\story_quest\android\app\src\main\AndroidManifest.xml:40:5-45:15
24        <intent>
24-->D:\AliM Studio\App 01\App\story_quest\android\app\src\main\AndroidManifest.xml:41:9-44:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->D:\AliM Studio\App 01\App\story_quest\android\app\src\main\AndroidManifest.xml:42:13-72
25-->D:\AliM Studio\App 01\App\story_quest\android\app\src\main\AndroidManifest.xml:42:21-70
26
27            <data android:mimeType="text/plain" />
27-->D:\AliM Studio\App 01\App\story_quest\android\app\src\main\AndroidManifest.xml:43:13-50
27-->D:\AliM Studio\App 01\App\story_quest\android\app\src\main\AndroidManifest.xml:43:19-48
28        </intent>
29    </queries>
30
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:26:5-79
31-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:26:22-76
32    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
32-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3407c00353d595f9b08c76c1c5929a88\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
32-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3407c00353d595f9b08c76c1c5929a88\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
33
34    <permission
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
35        android:name="com.example.story_quest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="com.example.story_quest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
39
40    <application
41        android:name="android.app.Application"
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
43        android:debuggable="true"
44        android:extractNativeLibs="false"
45        android:icon="@mipmap/ic_launcher"
46        android:label="story_quest" >
47        <activity
48            android:name="com.example.story_quest.MainActivity"
49            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
50            android:exported="true"
51            android:hardwareAccelerated="true"
52            android:launchMode="singleTop"
53            android:taskAffinity=""
54            android:theme="@style/LaunchTheme"
55            android:windowSoftInputMode="adjustResize" >
56
57            <!--
58                 Specifies an Android theme to apply to this Activity as soon as
59                 the Android process has started. This theme is visible to the user
60                 while the Flutter UI initializes. After that, this theme continues
61                 to determine the Window background behind the Flutter UI.
62            -->
63            <meta-data
64                android:name="io.flutter.embedding.android.NormalTheme"
65                android:resource="@style/NormalTheme" />
66
67            <intent-filter>
68                <action android:name="android.intent.action.MAIN" />
69
70                <category android:name="android.intent.category.LAUNCHER" />
71            </intent-filter>
72        </activity>
73        <!--
74             Don't delete the meta-data below.
75             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
76        -->
77        <meta-data
78            android:name="flutterEmbedding"
79            android:value="2" />
80
81        <service
81-->[:firebase_auth] D:\AliM Studio\App 01\App\story_quest\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
82            android:name="com.google.firebase.components.ComponentDiscoveryService"
82-->[:firebase_auth] D:\AliM Studio\App 01\App\story_quest\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
83            android:directBootAware="true"
83-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
84            android:exported="false" >
84-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:68:13-37
85            <meta-data
85-->[:firebase_auth] D:\AliM Studio\App 01\App\story_quest\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
86                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
86-->[:firebase_auth] D:\AliM Studio\App 01\App\story_quest\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
87                android:value="com.google.firebase.components.ComponentRegistrar" />
87-->[:firebase_auth] D:\AliM Studio\App 01\App\story_quest\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
88            <meta-data
88-->[:cloud_firestore] D:\AliM Studio\App 01\App\story_quest\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
89                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
89-->[:cloud_firestore] D:\AliM Studio\App 01\App\story_quest\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
90                android:value="com.google.firebase.components.ComponentRegistrar" />
90-->[:cloud_firestore] D:\AliM Studio\App 01\App\story_quest\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
91            <meta-data
91-->[:firebase_storage] D:\AliM Studio\App 01\App\story_quest\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
92                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
92-->[:firebase_storage] D:\AliM Studio\App 01\App\story_quest\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
93                android:value="com.google.firebase.components.ComponentRegistrar" />
93-->[:firebase_storage] D:\AliM Studio\App 01\App\story_quest\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
94            <meta-data
94-->[:firebase_core] D:\AliM Studio\App 01\App\story_quest\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
95                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
95-->[:firebase_core] D:\AliM Studio\App 01\App\story_quest\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
96                android:value="com.google.firebase.components.ComponentRegistrar" />
96-->[:firebase_core] D:\AliM Studio\App 01\App\story_quest\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
97            <meta-data
97-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:69:13-71:85
98                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
98-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:70:17-109
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:71:17-82
100            <meta-data
100-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc4fa76342eea1211d350ed746ebb577\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:17:13-19:85
101                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
101-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc4fa76342eea1211d350ed746ebb577\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:18:17-122
102                android:value="com.google.firebase.components.ComponentRegistrar" />
102-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc4fa76342eea1211d350ed746ebb577\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:19:17-82
103            <meta-data
103-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc4fa76342eea1211d350ed746ebb577\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:20:13-22:85
104                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
104-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc4fa76342eea1211d350ed746ebb577\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:21:17-111
105                android:value="com.google.firebase.components.ComponentRegistrar" />
105-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc4fa76342eea1211d350ed746ebb577\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:22:17-82
106            <meta-data
106-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\569e59d367ccb503a5838d76eaff0ba8\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
107                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
107-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\569e59d367ccb503a5838d76eaff0ba8\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
108                android:value="com.google.firebase.components.ComponentRegistrar" />
108-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\569e59d367ccb503a5838d76eaff0ba8\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
109            <meta-data
109-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\569e59d367ccb503a5838d76eaff0ba8\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
110                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
110-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\569e59d367ccb503a5838d76eaff0ba8\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
111                android:value="com.google.firebase.components.ComponentRegistrar" />
111-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\569e59d367ccb503a5838d76eaff0ba8\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
112            <meta-data
112-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\67e08df8cc8d12a84af22bb7199109a2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
113                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
113-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\67e08df8cc8d12a84af22bb7199109a2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
114                android:value="com.google.firebase.components.ComponentRegistrar" />
114-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\67e08df8cc8d12a84af22bb7199109a2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
115            <meta-data
115-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\67e08df8cc8d12a84af22bb7199109a2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
116                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
116-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\67e08df8cc8d12a84af22bb7199109a2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
117                android:value="com.google.firebase.components.ComponentRegistrar" />
117-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\67e08df8cc8d12a84af22bb7199109a2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
118            <meta-data
118-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f6c3a36cd709297faeeb405e0f97f0c\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
119                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
119-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f6c3a36cd709297faeeb405e0f97f0c\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
120                android:value="com.google.firebase.components.ComponentRegistrar" />
120-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f6c3a36cd709297faeeb405e0f97f0c\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
121            <meta-data
121-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
122                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
122-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
123                android:value="com.google.firebase.components.ComponentRegistrar" />
123-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
124        </service>
125
126        <activity
126-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:29:9-46:20
127            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
127-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:30:13-80
128            android:excludeFromRecents="true"
128-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:31:13-46
129            android:exported="true"
129-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:32:13-36
130            android:launchMode="singleTask"
130-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:33:13-44
131            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
131-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:34:13-72
132            <intent-filter>
132-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:35:13-45:29
133                <action android:name="android.intent.action.VIEW" />
133-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
133-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
134
135                <category android:name="android.intent.category.DEFAULT" />
135-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
135-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
136                <category android:name="android.intent.category.BROWSABLE" />
136-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
136-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
137
138                <data
138-->D:\AliM Studio\App 01\App\story_quest\android\app\src\main\AndroidManifest.xml:43:13-50
139                    android:host="firebase.auth"
140                    android:path="/"
141                    android:scheme="genericidp" />
142            </intent-filter>
143        </activity>
144        <activity
144-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:47:9-64:20
145            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
145-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:48:13-79
146            android:excludeFromRecents="true"
146-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:49:13-46
147            android:exported="true"
147-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:50:13-36
148            android:launchMode="singleTask"
148-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:51:13-44
149            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
149-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:52:13-72
150            <intent-filter>
150-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:53:13-63:29
151                <action android:name="android.intent.action.VIEW" />
151-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
151-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
152
153                <category android:name="android.intent.category.DEFAULT" />
153-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
153-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
154                <category android:name="android.intent.category.BROWSABLE" />
154-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
154-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5a4b3b6debe2c9f13c820f7194375417\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
155
156                <data
156-->D:\AliM Studio\App 01\App\story_quest\android\app\src\main\AndroidManifest.xml:43:13-50
157                    android:host="firebase.auth"
158                    android:path="/"
159                    android:scheme="recaptcha" />
160            </intent-filter>
161        </activity>
162
163        <provider
163-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
164            android:name="com.google.firebase.provider.FirebaseInitProvider"
164-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
165            android:authorities="com.example.story_quest.firebaseinitprovider"
165-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
166            android:directBootAware="true"
166-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
167            android:exported="false"
167-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
168            android:initOrder="100" />
168-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
169
170        <service
170-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\85a7c287bc0d10f9a5bf501b6ebf14db\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
171            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
171-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\85a7c287bc0d10f9a5bf501b6ebf14db\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
172            android:enabled="true"
172-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\85a7c287bc0d10f9a5bf501b6ebf14db\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
173            android:exported="false" >
173-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\85a7c287bc0d10f9a5bf501b6ebf14db\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
174            <meta-data
174-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\85a7c287bc0d10f9a5bf501b6ebf14db\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
175                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
175-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\85a7c287bc0d10f9a5bf501b6ebf14db\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
176                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
176-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\85a7c287bc0d10f9a5bf501b6ebf14db\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
177        </service>
178
179        <activity
179-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\85a7c287bc0d10f9a5bf501b6ebf14db\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
180            android:name="androidx.credentials.playservices.HiddenActivity"
180-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\85a7c287bc0d10f9a5bf501b6ebf14db\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
181            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
181-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\85a7c287bc0d10f9a5bf501b6ebf14db\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
182            android:enabled="true"
182-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\85a7c287bc0d10f9a5bf501b6ebf14db\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
183            android:exported="false"
183-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\85a7c287bc0d10f9a5bf501b6ebf14db\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
184            android:fitsSystemWindows="true"
184-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\85a7c287bc0d10f9a5bf501b6ebf14db\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
185            android:theme="@style/Theme.Hidden" >
185-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\85a7c287bc0d10f9a5bf501b6ebf14db\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
186        </activity>
187        <activity
187-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41bee34b6117e933e1088d15118d4023\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
188            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
188-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41bee34b6117e933e1088d15118d4023\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
189            android:excludeFromRecents="true"
189-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41bee34b6117e933e1088d15118d4023\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
190            android:exported="false"
190-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41bee34b6117e933e1088d15118d4023\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
191            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
191-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41bee34b6117e933e1088d15118d4023\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
192        <!--
193            Service handling Google Sign-In user revocation. For apps that do not integrate with
194            Google Sign-In, this service will never be started.
195        -->
196        <service
196-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41bee34b6117e933e1088d15118d4023\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
197            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
197-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41bee34b6117e933e1088d15118d4023\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
198            android:exported="true"
198-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41bee34b6117e933e1088d15118d4023\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
199            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
199-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41bee34b6117e933e1088d15118d4023\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
200            android:visibleToInstantApps="true" />
200-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41bee34b6117e933e1088d15118d4023\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
201
202        <activity
202-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
203            android:name="com.google.android.gms.common.api.GoogleApiActivity"
203-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
204            android:exported="false"
204-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
205            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
205-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
206
207        <uses-library
207-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
208            android:name="androidx.window.extensions"
208-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
209            android:required="false" />
209-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
210        <uses-library
210-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
211            android:name="androidx.window.sidecar"
211-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
212            android:required="false" />
212-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
213
214        <meta-data
214-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6250b576436e3aba2e4229fd698d3169\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
215            android:name="com.google.android.gms.version"
215-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6250b576436e3aba2e4229fd698d3169\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
216            android:value="@integer/google_play_services_version" />
216-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6250b576436e3aba2e4229fd698d3169\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
217
218        <provider
218-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
219            android:name="androidx.startup.InitializationProvider"
219-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
220            android:authorities="com.example.story_quest.androidx-startup"
220-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
221            android:exported="false" >
221-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
222            <meta-data
222-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
223                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
223-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
224                android:value="androidx.startup" />
224-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
225            <meta-data
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
226                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
227                android:value="androidx.startup" />
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
228        </provider>
229
230        <receiver
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
231            android:name="androidx.profileinstaller.ProfileInstallReceiver"
231-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
232            android:directBootAware="false"
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
233            android:enabled="true"
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
234            android:exported="true"
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
235            android:permission="android.permission.DUMP" >
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
236            <intent-filter>
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
237                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
238            </intent-filter>
239            <intent-filter>
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
240                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
241            </intent-filter>
242            <intent-filter>
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
243                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
244            </intent-filter>
245            <intent-filter>
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
246                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
247            </intent-filter>
248        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
249        <activity
249-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac765428d701a2351974cfbfd0c53614\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
250            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
250-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac765428d701a2351974cfbfd0c53614\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
251            android:exported="false"
251-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac765428d701a2351974cfbfd0c53614\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
252            android:stateNotNeeded="true"
252-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac765428d701a2351974cfbfd0c53614\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
253            android:theme="@style/Theme.PlayCore.Transparent" />
253-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac765428d701a2351974cfbfd0c53614\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
254    </application>
255
256</manifest>
