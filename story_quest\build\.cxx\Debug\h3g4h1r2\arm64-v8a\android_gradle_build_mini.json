{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Install\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\AliM Studio\\App 01\\App\\story_quest\\build\\.cxx\\Debug\\h3g4h1r2\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\Install\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\AliM Studio\\App 01\\App\\story_quest\\build\\.cxx\\Debug\\h3g4h1r2\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}