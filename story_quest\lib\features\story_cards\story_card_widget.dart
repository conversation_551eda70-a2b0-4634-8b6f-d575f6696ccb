/// Story Card widget component
/// 
/// Reusable story card with 120px x 180px dimensions, colorful borders,
/// cover image, metadata overlay, and interactive buttons.
library;

import 'package:flutter/material.dart';
import '../../models/story_model.dart';
import '../../utils/accessibility_helper.dart';

/// Story card widget for displaying story information
class StoryCardWidget extends StatefulWidget {
  /// The story to display
  final StoryModel story;
  
  /// Whether the story is downloaded offline
  final bool isDownloaded;
  
  /// Whether the story is currently downloading
  final bool isDownloading;
  
  /// Download progress (0.0 to 1.0)
  final double downloadProgress;
  
  /// Callback when card is tapped
  final VoidCallback? onTap;
  
  /// Callback when play/download button is pressed
  final VoidCallback? onPlayDownload;
  
  /// Card width (default 120px)
  final double width;
  
  /// Card height (default 180px)
  final double height;

  const StoryCardWidget({
    super.key,
    required this.story,
    this.isDownloaded = false,
    this.isDownloading = false,
    this.downloadProgress = 0.0,
    this.onTap,
    this.onPlayDownload,
    this.width = 120,
    this.height = 180,
  });

  @override
  State<StoryCardWidget> createState() => _StoryCardWidgetState();
}

class _StoryCardWidgetState extends State<StoryCardWidget>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _downloadController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize hover animation
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));

    // Initialize download animation
    _downloadController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _downloadController,
      curve: Curves.easeInOut,
    ));

    if (widget.isDownloading) {
      _downloadController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(StoryCardWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isDownloading && !oldWidget.isDownloading) {
      _downloadController.repeat(reverse: true);
    } else if (!widget.isDownloading && oldWidget.isDownloading) {
      _downloadController.stop();
      _downloadController.reset();
    }
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _downloadController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: _buildCard(),
        );
      },
    );
  }

  /// Builds the main card structure
  Widget _buildCard() {
    return AccessibilityHelper.createAccessibleButton(
      child: MouseRegion(
        onEnter: (_) => _onHover(true),
        onExit: (_) => _onHover(false),
        child: Container(
          width: widget.width,
          height: widget.height,
          decoration: _buildCardDecoration(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildCoverImage(),
              _buildMetadataSection(),
            ],
          ),
        ),
      ),
      onPressed: widget.onTap,
      semanticLabel: _buildSemanticLabel(),
      width: widget.width,
      height: widget.height,
    );
  }

  /// Builds card decoration with border and shadow
  BoxDecoration _buildCardDecoration() {
    return BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      border: Border.all(
        color: _getBorderColor(),
        width: 2,
      ),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.2),
          blurRadius: _isHovered ? 12 : 8,
          offset: Offset(0, _isHovered ? 6 : 4),
        ),
        if (_isHovered || widget.isDownloading)
          BoxShadow(
            color: _getBorderColor().withValues(alpha: 0.4),
            blurRadius: 20,
            spreadRadius: 2,
          ),
      ],
    );
  }

  /// Builds the cover image section (70% of card)
  Widget _buildCoverImage() {
    return Expanded(
      flex: 7,
      child: Stack(
        children: [
          // Cover image
          ClipRRect(
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(10),
            ),
            child: _buildCoverImageContent(),
          ),
          
          // Overlay indicators
          _buildOverlayIndicators(),
          
          // Download progress
          if (widget.isDownloading)
            _buildDownloadProgress(),
        ],
      ),
    );
  }

  /// Builds overlay indicators (age badge, offline status)
  Widget _buildOverlayIndicators() {
    return Positioned.fill(
      child: Stack(
        children: [
          // Age badge
          Positioned(
            top: 8,
            left: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: AccessibilityHelper.createAccessibleText(
                widget.story.ageGroup,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                semanticLabel: 'Age group ${widget.story.ageGroup}',
              ),
            ),
          ),
          
          // Offline indicator
          if (widget.isDownloaded)
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Color(0xFF4CAF50),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.offline_pin,
                  color: Colors.white,
                  size: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Builds download progress indicator
  Widget _buildDownloadProgress() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.5),
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(10),
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                value: widget.downloadProgress,
                backgroundColor: Colors.white.withValues(alpha: 0.3),
                valueColor: const AlwaysStoppedAnimation<Color>(
                  Color(0xFF4CAF50),
                ),
                strokeWidth: 3,
              ),
              const SizedBox(height: 8),
              AccessibilityHelper.createAccessibleText(
                '${(widget.downloadProgress * 100).toInt()}%',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
                semanticLabel: 'Download progress ${(widget.downloadProgress * 100).toInt()} percent',
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the metadata section (30% of card)
  Widget _buildMetadataSection() {
    return Expanded(
      flex: 3,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.8),
          borderRadius: const BorderRadius.vertical(
            bottom: Radius.circular(10),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Expanded(
              child: AccessibilityHelper.createAccessibleText(
                widget.story.title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                semanticLabel: 'Story title: ${widget.story.title}',
              ),
            ),
            
            const SizedBox(height: 4),
            
            // Play/Download button
            _buildActionButton(),
          ],
        ),
      ),
    );
  }

  /// Builds the play/download action button
  Widget _buildActionButton() {
    final buttonText = widget.isDownloading 
        ? 'Downloading...'
        : widget.isDownloaded 
            ? 'Play' 
            : 'Download';
    
    return SizedBox(
      width: double.infinity,
      height: 24,
      child: ElevatedButton(
        onPressed: widget.isDownloading ? null : widget.onPlayDownload,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF4CAF50),
          foregroundColor: Colors.white,
          disabledBackgroundColor: Colors.grey,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: _isHovered ? 4 : 2,
          padding: const EdgeInsets.symmetric(horizontal: 8),
        ),
        child: AnimatedBuilder(
          animation: _glowAnimation,
          builder: (context, child) {
            return Container(
              decoration: _isHovered ? BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF4CAF50).withValues(alpha: 0.6 * _glowAnimation.value),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
              ) : null,
              child: Text(
                buttonText,
                style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// Handles hover state changes
  void _onHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });
    
    if (isHovered) {
      _hoverController.forward();
    } else {
      _hoverController.reverse();
    }
  }

  /// Gets border color based on story difficulty
  Color _getBorderColor() {
    switch (widget.story.difficulty.toLowerCase()) {
      case 'easy':
        return const Color(0xFF4CAF50); // Green
      case 'medium':
        return const Color(0xFFFF9800); // Orange
      case 'hard':
        return const Color(0xFFF44336); // Red
      default:
        return const Color(0xFF1976D2); // Blue
    }
  }

  /// Builds the cover image content with fallback handling
  Widget _buildCoverImageContent() {
    // Use the story's cover image path directly from the model
    final coverImagePath = widget.story.coverImage.isNotEmpty
        ? '${widget.story.storyId}/images/${widget.story.coverImage}'
        : 'default/default_image.png';

    return Image.asset(
      coverImagePath,
      width: double.infinity,
      height: double.infinity,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        // Fallback to default image if story-specific cover not found
        return Image.asset(
          'assets/default/default_image.png',
          width: double.infinity,
          height: double.infinity,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            // Final fallback if even default image fails
            return Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xFFE3F2FD),
                    const Color(0xFFBBDEFB),
                  ],
                ),
              ),
              child: Icon(
                Icons.auto_stories,
                size: 40,
                color: _getBorderColor(),
              ),
            );
          },
        );
      },
    );
  }

  /// Builds semantic label for accessibility
  String _buildSemanticLabel() {
    final status = widget.isDownloading
        ? 'downloading'
        : widget.isDownloaded
            ? 'downloaded and ready to play'
            : 'available for download';

    return '${widget.story.title}, age group ${widget.story.ageGroup}, '
           'difficulty ${widget.story.difficulty}, $status';
  }
}
