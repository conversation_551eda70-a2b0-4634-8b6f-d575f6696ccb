# Story Quest API Documentation

## Overview

Story Quest is an interactive children's storytelling app built with Flutter. This document provides comprehensive API documentation for developers working on the project.

## Architecture

### Core Services

#### 1. TTS Service (`lib/services/tts_service.dart`)

Abstract interface for text-to-speech functionality.

```dart
abstract class TTSService {
  Future<void> speak(String text, {VoiceModel? voiceConfig});
  Future<void> stop();
  Future<void> setLanguage(String language);
  Future<void> setVoice(VoiceModel voice);
  Future<bool> get isSpeaking;
  Future<List<String>> getAvailableVoices();
  Future<List<String>> getAvailableLanguages();
  Future<void> setSpeechRate(double rate);
  Future<void> setPitch(double pitch);
  Future<void> setVolume(double volume);
  Future<void> pause();
  Future<void> resume();
  Future<void> dispose();
}
```

**Implementation**: `FlutterTTSService` uses the `flutter_tts` package.

#### 2. Story Service (`lib/services/story_service.dart`)

Unified service for story management combining Firebase and offline capabilities.

```dart
class StoryService {
  Future<List<StoryModel>> getAvailableStories();
  Future<StoryModel?> getStory(String storyId);
  Future<bool> downloadStory(String storyId);
  Future<bool> isStoryDownloaded(String storyId);
  Future<void> deleteDownloadedStory(String storyId);
  Future<List<StoryModel>> getDownloadedStories();
}
```

#### 3. Badge Service (`lib/services/badge_service.dart`)

Manages user achievements and rewards system.

```dart
class BadgeService {
  Future<BadgeModel?> awardBadgeForChoice({
    required String userId,
    required StoryModel story,
    required String sceneId,
    required ChoiceModel choice,
  });
  Future<List<BadgeModel>> getBadgesForUser(String userId);
  Future<List<BadgeModel>> getBadgesByType(String userId, BadgeType type);
  Future<List<BadgeModel>> getNewBadges(String userId);
  Future<void> markBadgesAsViewed(String userId, List<String> badgeIds);
  Future<Map<String, dynamic>> getBadgeStats(String userId);
}
```

#### 4. Database Service (`lib/services/database_service.dart`)

Local SQLite database management for offline functionality.

```dart
class DatabaseService {
  Future<Database> get database;
  Future<void> close();
  Future<void> deleteDatabase();
}
```

#### 5. Firebase Service (`lib/services/firebase_service.dart`)

Firebase integration for cloud storage and data synchronization.

```dart
class FirebaseService {
  Future<void> initialize();
  Future<Map<String, dynamic>?> downloadStoryData(String storyId);
  Future<Uint8List?> downloadAsset(String assetPath);
  Future<List<Map<String, dynamic>>> getStoryList();
}
```

### Data Models

#### 1. Story Model (`lib/models/story_model.dart`)

```dart
class StoryModel {
  final String storyId;
  final String title;
  final String description;
  final String difficulty;
  final String coverImagePath;
  final List<SceneModel> scenes;
  final List<CharacterModel> characters;
  final String theme;
  final List<String> tags;
  final int estimatedDuration;
  final bool isDownloaded;
}
```

#### 2. Scene Model (`lib/models/scene_model.dart`)

```dart
class SceneModel {
  final String sceneId;
  final String text;
  final String backgroundImage;
  final String audioFile;
  final List<CharacterModel> characters;
  final List<ChoiceModel> choices;
  final String nextSceneId;
}
```

#### 3. Character Model (`lib/models/character_model.dart`)

```dart
class CharacterModel {
  final String characterId;
  final String name;
  final String description;
  final String imagePath;
  final String voiceId;
  final Map<String, String> emotions;
}
```

#### 4. Choice Model (`lib/models/choice_model.dart`)

```dart
class ChoiceModel {
  final String choiceId;
  final String option;
  final String nextSceneId;
  final String? moralLesson;
  final BadgeType? badgeType;
}
```

#### 5. Badge Model (`lib/models/badge_model.dart`)

```dart
class BadgeModel {
  final String id;
  final BadgeType type;
  final BadgeRarity rarity;
  final String name;
  final String description;
  final String storyId;
  final String sceneId;
  final String choiceText;
  final DateTime earnedAt;
  final bool isNew;
}
```

#### 6. Voice Model (`lib/models/voice_model.dart`)

```dart
class VoiceModel {
  final String name;
  final String language;
  final double pitch;
  final double rate;
  final double volume;
}
```

### UI Components

#### 1. Story Card Widget (`lib/widgets/story_card_widget.dart`)

Reusable card component for displaying story information.

```dart
class StoryCardWidget extends StatelessWidget {
  final StoryModel story;
  final VoidCallback? onTap;
  final VoidCallback? onDownload;
  final bool showDownloadButton;
}
```

#### 2. Choice Popup (`lib/widgets/choice_popup.dart`)

Interactive popup for story choice selection.

```dart
class ChoicePopup extends StatefulWidget {
  final List<ChoiceModel> choices;
  final String title;
  final String? subtitle;
  final ChoiceCallback? onChoiceSelected;
  final bool barrierDismissible;
}
```

#### 3. Moral Popup (`lib/widgets/moral_popup.dart`)

Post-choice reflection popup with moral lessons.

```dart
class MoralPopup extends StatefulWidget {
  final String moralText;
  final BadgeModel? earnedBadge;
  final String choiceText;
  final VoidCallback? onContinue;
  final bool barrierDismissible;
}
```

#### 4. Badge Card Widget (`lib/widgets/badge_card_widget.dart`)

Display component for individual badges.

```dart
class BadgeCardWidget extends StatefulWidget {
  final BadgeModel badge;
  final Duration animationDelay;
  final bool isNew;
}
```

### Screen Components

#### 1. Splash Screen (`lib/features/splash/splash_screen.dart`)

App launch screen with branding and navigation.

#### 2. FTUE Screen (`lib/features/ftue/ftue_screen.dart`)

First-time user experience with account creation.

#### 3. Child Profile Selection (`lib/features/profile_selection/child_profile_selection_screen.dart`)

Profile selection and creation interface.

#### 4. Homepage (`lib/features/homepage/homepage_screen.dart`)

Main navigation hub with story access and settings.

#### 5. Story Library (`lib/features/story_library/story_library_screen.dart`)

Grid view of available stories with filtering and search.

#### 6. Story Introduction (`lib/features/story_introduction/story_introduction_screen.dart`)

Story preview with character introduction and play options.

#### 7. Meet Characters (`lib/features/meet_characters/meet_characters_screen.dart`)

Character carousel with audio previews.

#### 8. Story Play (`lib/features/story_play/story_play_screen.dart`)

Main story reading interface with TTS and interactions.

#### 9. My Rewards (`lib/features/my_rewards/my_rewards_screen.dart`)

Badge collection and achievement display.

#### 10. Parent Zone (`lib/features/parent_zone/parent_zone_screen.dart`)

Secure parental controls and monitoring dashboard.

#### 11. Parent Settings (`lib/features/parent_settings/parent_settings_screen.dart`)

Parental configuration options and controls.

#### 12. Feedback Screen (`lib/features/feedback/feedback_screen.dart`)

Parent feedback and support interface.

### Utility Services

#### 1. Navigation Service (`lib/services/navigation_service.dart`)

Centralized navigation management with named routes.

#### 2. Performance Monitor (`lib/services/performance_monitor.dart`)

Performance tracking and optimization metrics.

#### 3. Enhanced Offline Manager (`lib/services/enhanced_offline_manager.dart`)

Advanced caching and offline functionality.

#### 4. Service Locator (`lib/services/service_locator.dart`)

Dependency injection using `get_it` package.

### Constants and Configuration

#### 1. App Constants (`lib/utils/constants.dart`)

Application-wide constants and configuration values.

#### 2. Accessibility Helper (`lib/utils/accessibility_helper.dart`)

Accessibility utilities and helper functions.

## API Usage Examples

### Playing a Story

```dart
// Get story service
final storyService = getIt<StoryService>();

// Load story
final story = await storyService.getStory('story013');
if (story != null) {
  // Navigate to story play screen
  Navigator.pushNamed(context, '/story-play', arguments: story);
}
```

### Awarding a Badge

```dart
// Get badge service
final badgeService = getIt<BadgeService>();

// Award badge for choice
final badge = await badgeService.awardBadgeForChoice(
  userId: 'user123',
  story: currentStory,
  sceneId: 'scene001',
  choice: selectedChoice,
);

if (badge != null) {
  // Show badge earned popup
  showMoralPopup(
    context: context,
    moralText: 'Great choice! You showed kindness.',
    choiceText: selectedChoice.option,
    earnedBadge: badge,
  );
}
```

### Text-to-Speech

```dart
// Get TTS service
final ttsService = getIt<TTSService>();

// Configure voice
await ttsService.setLanguage('en-US');
await ttsService.setSpeechRate(1.0);

// Speak text
await ttsService.speak('Once upon a time...');
```

### Offline Story Management

```dart
// Download story for offline use
final success = await storyService.downloadStory('story013');

// Check if story is available offline
final isOffline = await storyService.isStoryDownloaded('story013');

// Get offline stories
final offlineStories = await storyService.getDownloadedStories();
```

## Error Handling

All services implement comprehensive error handling with try-catch blocks and appropriate fallbacks. Errors are logged for debugging and user-friendly messages are displayed when appropriate.

## Performance Considerations

- Stories are cached locally for offline access
- Images are optimized and cached
- TTS audio is preloaded when possible
- Database queries are optimized with proper indexing
- Memory usage is monitored and managed

## Security

- Parental controls are PIN-protected
- User data is stored locally with encryption
- Firebase security rules protect cloud data
- No personal information is collected from children

## Testing

The app includes comprehensive testing:
- Unit tests for services and models
- Widget tests for UI components
- Integration tests for user flows
- Performance tests for optimization

## Deployment

The app is configured for deployment to:
- Google Play Store (Android)
- Apple App Store (iOS)
- Firebase hosting for web version (optional)

## Support and Maintenance

Regular updates include:
- New story content
- Performance improvements
- Bug fixes
- Feature enhancements
- Security updates

## Development Guidelines

### Code Style
- Follow Dart/Flutter conventions
- Use snake_case for file names
- Use PascalCase for class names
- Use camelCase for variables and methods
- Add comprehensive documentation comments

### Architecture Patterns
- Use dependency injection with get_it
- Implement repository pattern for data access
- Use provider/riverpod for state management
- Follow feature-based folder structure

### Testing Requirements
- Minimum 80% code coverage
- Unit tests for all services
- Widget tests for all custom widgets
- Integration tests for critical user flows

### Performance Standards
- App launch time < 3 seconds
- Story loading time < 2 seconds
- Memory usage < 200MB
- Smooth 60fps animations
