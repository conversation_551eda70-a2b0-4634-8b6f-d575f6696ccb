/// Child Profile Selection screen
/// 
/// Displays child profiles with circular avatars, allows profile selection,
/// and provides functionality to add new profiles. Responsive design for all devices.
library;

import 'package:flutter/material.dart';
import '../../models/child_profile_model.dart';
import '../../services/child_profile_service.dart';
import '../../utils/device_utils.dart';

/// Child profile selection screen widget
class ChildProfileSelectionScreen extends StatefulWidget {
  /// Callback when a profile is selected
  final Function(ChildProfileModel)? onProfileSelected;

  /// Callback for back navigation
  final VoidCallback? onBack;

  const ChildProfileSelectionScreen({
    super.key,
    this.onProfileSelected,
    this.onBack,
  });

  @override
  State<ChildProfileSelectionScreen> createState() => _ChildProfileSelectionScreenState();
}

class _ChildProfileSelectionScreenState extends State<ChildProfileSelectionScreen>
    with TickerProviderStateMixin {
  late AnimationController _addButtonController;
  late Animation<double> _addButtonAnimation;

  final ChildProfileService _profileService = ChildProfileService();
  List<ChildProfileModel> _profiles = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    // Initialize add button animation
    _addButtonController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _addButtonAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _addButtonController,
      curve: Curves.elasticOut,
    ));

    _loadProfiles();
  }

  /// Loads profiles from the service
  Future<void> _loadProfiles() async {
    try {
      final profiles = await _profileService.getProfiles();
      if (mounted) {
        setState(() {
          _profiles = profiles;
          _isLoading = false;
          _errorMessage = null;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to load profiles. Please try again.';
        });
      }
    }
  }

  @override
  void dispose() {
    _addButtonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveLayoutBuilder(
      builder: (context, deviceType, orientation) {
        return Scaffold(
          body: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFFFCE4EC), // Light pink
                  Color(0xFFC8E6C9), // Light green
                ],
              ),
            ),
            child: SafeArea(
              child: _buildContent(deviceType, orientation),
            ),
          ),
        );
      },
    );
  }

  /// Builds the main content based on loading state
  Widget _buildContent(DeviceType deviceType, ScreenOrientation orientation) {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_profiles.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        _buildHeader(deviceType),
        Expanded(
          child: _buildProfileGrid(deviceType, orientation),
        ),
        _buildBackButton(deviceType),
      ],
    );
  }

  /// Builds loading state
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
          ),
          SizedBox(height: 16),
          Text(
            'Loading profiles...',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF2E7D32),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds error state
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Color(0xFFE57373),
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage!,
            style: const TextStyle(
              fontSize: 18,
              color: Color(0xFF2E7D32),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadProfiles,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
            ),
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  /// Builds empty state (no profiles)
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.person_add,
            size: 64,
            color: Color(0xFF4CAF50),
          ),
          const SizedBox(height: 16),
          const Text(
            'No profiles yet!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2E7D32),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Create your first child profile to get started',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF2E7D32),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _showCreateProfileDialog,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
            child: const Text(
              'Create Profile',
              style: TextStyle(fontSize: 18),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the header section
  Widget _buildHeader(DeviceType deviceType) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          const Text(
            'Choose Your Profile',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2E7D32),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Select who\'s reading today',
            style: TextStyle(
              fontSize: 18,
              color: const Color(0xFF2E7D32).withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the profile grid with responsive layout
  Widget _buildProfileGrid(DeviceType deviceType, ScreenOrientation orientation) {
    final crossAxisCount = DeviceUtils.getResponsiveGridColumns(context);
    final avatarSize = DeviceUtils.getResponsiveIconSize(context, 100.0);

    return Padding(
      padding: DeviceUtils.getResponsivePadding(context),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: DeviceUtils.getResponsiveSpacing(context, 20),
          mainAxisSpacing: DeviceUtils.getResponsiveSpacing(context, 20),
          childAspectRatio: 0.8,
        ),
        itemCount: _profiles.length + 1, // +1 for add button
        itemBuilder: (context, index) {
          if (index < _profiles.length) {
            return _buildProfileAvatar(_profiles[index], avatarSize);
          } else {
            return _buildAddProfileButton(avatarSize);
          }
        },
      ),
    );
  }

  /// Builds individual profile avatar
  Widget _buildProfileAvatar(ChildProfileModel profile, double size) {
    return Semantics(
      button: true,
      label: 'Select profile for ${profile.name}',
      child: GestureDetector(
        onTap: () => _selectProfile(profile),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Avatar circle
            Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                color: _getProfileColor(profile),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: _buildAvatarContent(profile, size),
            ),

            SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 12)),

            // Name and level
            Column(
              children: [
                Text(
                  profile.name,
                  style: TextStyle(
                    fontSize: DeviceUtils.getResponsiveFontSize(context, 18),
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF2E7D32),
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 4)),
                Text(
                  profile.levelName,
                  style: TextStyle(
                    fontSize: DeviceUtils.getResponsiveFontSize(context, 12),
                    color: const Color(0xFF2E7D32).withValues(alpha: 0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the add profile button with animation
  Widget _buildAddProfileButton(double size) {
    return Semantics(
      button: true,
      label: 'Add new child profile',
      child: GestureDetector(
        onTap: () {
          _addButtonController.forward().then((_) {
            _addButtonController.reverse();
          });
          _showCreateProfileDialog();
        },
        child: AnimatedBuilder(
          animation: _addButtonAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _addButtonAnimation.value,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Add button circle
                  Container(
                    width: size,
                    height: size,
                    decoration: BoxDecoration(
                      color: const Color(0xFF4CAF50),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF4CAF50).withValues(alpha: 0.4),
                          blurRadius: 15,
                          spreadRadius: 2,
                        ),
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.add,
                      size: size * 0.5,
                      color: Colors.white,
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Add text
                  const Text(
                    'Add Profile',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2E7D32),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  /// Builds the back button
  Widget _buildBackButton(DeviceType deviceType) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Semantics(
          button: true,
          label: 'Go back to previous screen',
          child: GestureDetector(
            onTap: widget.onBack,
            child: Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: const Color(0xFFB0BEC5),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Selects a profile and sets it as active
  Future<void> _selectProfile(ChildProfileModel profile) async {
    try {
      await _profileService.setActiveProfile(profile.id);
      widget.onProfileSelected?.call(profile);
    } catch (e) {
      _showErrorMessage('Failed to select profile. Please try again.');
    }
  }

  /// Gets profile color based on theme or default
  Color _getProfileColor(ChildProfileModel profile) {
    // Generate color based on profile name hash for consistency
    final hash = profile.name.hashCode;
    final colors = [
      const Color(0xFFE91E63), // Pink
      const Color(0xFF2196F3), // Blue
      const Color(0xFF9C27B0), // Purple
      const Color(0xFFFF9800), // Orange
      const Color(0xFF4CAF50), // Green
      const Color(0xFFFF5722), // Deep Orange
      const Color(0xFF607D8B), // Blue Grey
      const Color(0xFF795548), // Brown
    ];
    return colors[hash.abs() % colors.length];
  }

  /// Builds avatar content (icon or image)
  Widget _buildAvatarContent(ChildProfileModel profile, double size) {
    // For now, use icons. In a real app, you'd load the avatar image
    final icons = [
      Icons.star,
      Icons.pets,
      Icons.favorite,
      Icons.auto_awesome,
      Icons.sports_soccer,
      Icons.music_note,
      Icons.palette,
      Icons.rocket_launch,
    ];

    final hash = profile.name.hashCode;
    final icon = icons[hash.abs() % icons.length];

    return Icon(
      icon,
      size: size * 0.5,
      color: Colors.white,
    );
  }

  /// Shows create profile dialog
  void _showCreateProfileDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _CreateProfileDialog(
        onProfileCreated: (profile) {
          _loadProfiles(); // Refresh the list
        },
      ),
    );
  }

  /// Shows error message
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }
}

/// Create profile dialog widget
class _CreateProfileDialog extends StatefulWidget {
  final Function(ChildProfileModel) onProfileCreated;

  const _CreateProfileDialog({
    required this.onProfileCreated,
  });

  @override
  State<_CreateProfileDialog> createState() => _CreateProfileDialogState();
}

class _CreateProfileDialogState extends State<_CreateProfileDialog> {
  final _nameController = TextEditingController();
  final _ageController = TextEditingController();
  final ChildProfileService _profileService = ChildProfileService();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create New Profile'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Child\'s Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _ageController,
              decoration: const InputDecoration(
                labelText: 'Age (3-12)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _createProfile,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Create'),
        ),
      ],
    );
  }

  Future<void> _createProfile() async {
    final name = _nameController.text.trim();
    final ageText = _ageController.text.trim();

    if (name.isEmpty || ageText.isEmpty) {
      _showError('Please fill in all fields.');
      return;
    }

    final age = int.tryParse(ageText);
    if (age == null || age < 3 || age > 12) {
      _showError('Please enter a valid age between 3 and 12.');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final profile = ChildProfileModel(
        id: '', // Will be set by Firestore
        name: name,
        age: age,
        avatarPath: ChildProfileModel.availableAvatars.first,
        parentId: '', // Will be set by the service
        createdAt: DateTime.now(),
        lastActiveAt: DateTime.now(),
      );

      final result = await _profileService.createProfile(profile);

      if (result == ProfileResult.success) {
        if (mounted) {
          Navigator.of(context).pop();
          widget.onProfileCreated(profile);
        }
      } else {
        _showError(ChildProfileService.getErrorMessage(result));
      }
    } catch (e) {
      _showError('An unexpected error occurred. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ageController.dispose();
    super.dispose();
  }
}


