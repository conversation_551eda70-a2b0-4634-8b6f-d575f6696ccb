/// Child Profile Selection screen
/// 
/// Displays child profiles with circular avatars, allows profile selection,
/// and provides functionality to add new profiles. Responsive design for all devices.
library;

import 'package:flutter/material.dart';

/// Model for child profile data
class ChildProfile {
  final String id;
  final String name;
  final IconData avatar;
  final Color color;

  const ChildProfile({
    required this.id,
    required this.name,
    required this.avatar,
    required this.color,
  });
}

/// Child profile selection screen widget
class ChildProfileSelectionScreen extends StatefulWidget {
  /// List of existing child profiles
  final List<ChildProfile> profiles;
  
  /// Callback when a profile is selected
  final Function(ChildProfile)? onProfileSelected;
  
  /// Callback when add profile is tapped
  final VoidCallback? onAddProfile;
  
  /// Callback for back navigation
  final VoidCallback? onBack;

  const ChildProfileSelectionScreen({
    super.key,
    this.profiles = const [],
    this.onProfileSelected,
    this.onAddProfile,
    this.onBack,
  });

  @override
  State<ChildProfileSelectionScreen> createState() => _ChildProfileSelectionScreenState();
}

class _ChildProfileSelectionScreenState extends State<ChildProfileSelectionScreen>
    with TickerProviderStateMixin {
  late AnimationController _addButtonController;
  late Animation<double> _addButtonAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize add button animation
    _addButtonController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _addButtonAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _addButtonController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _addButtonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    final isTV = screenSize.width > 1200;
    
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFFCE4EC), // Light pink
              Color(0xFFC8E6C9), // Light green
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: _buildProfileGrid(isTablet, isTV),
              ),
              _buildBackButton(),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the header section
  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          const Text(
            'Choose Your Profile',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2E7D32),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Select who\'s reading today',
            style: TextStyle(
              fontSize: 18,
              color: const Color(0xFF2E7D32).withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the profile grid with responsive layout
  Widget _buildProfileGrid(bool isTablet, bool isTV) {
    final crossAxisCount = isTV ? 4 : (isTablet ? 3 : 2);
    final avatarSize = isTV ? 150.0 : (isTablet ? 120.0 : 100.0);
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: 20,
          mainAxisSpacing: 20,
          childAspectRatio: 0.8,
        ),
        itemCount: widget.profiles.length + 1, // +1 for add button
        itemBuilder: (context, index) {
          if (index < widget.profiles.length) {
            return _buildProfileAvatar(widget.profiles[index], avatarSize);
          } else {
            return _buildAddProfileButton(avatarSize);
          }
        },
      ),
    );
  }

  /// Builds individual profile avatar
  Widget _buildProfileAvatar(ChildProfile profile, double size) {
    return Semantics(
      button: true,
      label: 'Select profile for ${profile.name}',
      child: GestureDetector(
        onTap: () => widget.onProfileSelected?.call(profile),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Avatar circle
            Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                color: profile.color,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                profile.avatar,
                size: size * 0.5,
                color: Colors.white,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Name
            Text(
              profile.name,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2E7D32),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the add profile button with animation
  Widget _buildAddProfileButton(double size) {
    return Semantics(
      button: true,
      label: 'Add new child profile',
      child: GestureDetector(
        onTap: () {
          _addButtonController.forward().then((_) {
            _addButtonController.reverse();
          });
          widget.onAddProfile?.call();
        },
        child: AnimatedBuilder(
          animation: _addButtonAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _addButtonAnimation.value,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Add button circle
                  Container(
                    width: size,
                    height: size,
                    decoration: BoxDecoration(
                      color: const Color(0xFF4CAF50),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF4CAF50).withValues(alpha: 0.4),
                          blurRadius: 15,
                          spreadRadius: 2,
                        ),
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.add,
                      size: size * 0.5,
                      color: Colors.white,
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Add text
                  const Text(
                    'Add Profile',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2E7D32),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  /// Builds the back button
  Widget _buildBackButton() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Semantics(
          button: true,
          label: 'Go back to previous screen',
          child: GestureDetector(
            onTap: widget.onBack,
            child: Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: const Color(0xFFB0BEC5),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Default child profiles for demonstration
class DefaultProfiles {
  static const List<ChildProfile> profiles = [
    ChildProfile(
      id: '1',
      name: 'Emma',
      avatar: Icons.star,
      color: Color(0xFFE91E63), // Pink
    ),
    ChildProfile(
      id: '2',
      name: 'Alex',
      avatar: Icons.pets,
      color: Color(0xFF2196F3), // Blue
    ),
    ChildProfile(
      id: '3',
      name: 'Sam',
      avatar: Icons.favorite,
      color: Color(0xFF9C27B0), // Purple
    ),
    ChildProfile(
      id: '4',
      name: 'Maya',
      avatar: Icons.auto_awesome,
      color: Color(0xFFFF9800), // Orange
    ),
  ];
}
