/// Theme management service for Story Quest app
///
/// Provides comprehensive theme management including light, dark, and high contrast themes
/// with persistent storage and accessibility features.
library;

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Available theme types
enum AppThemeType {
  light('Light', Icons.light_mode),
  dark('Dark', Icons.dark_mode),
  highContrast('High Contrast', Icons.contrast);

  const AppThemeType(this.label, this.icon);
  final String label;
  final IconData icon;
}

/// Theme service for managing app themes
class ThemeService extends ChangeNotifier {
  static final ThemeService _instance = ThemeService._internal();
  factory ThemeService() => _instance;
  ThemeService._internal();

  // Storage key
  static const String _themeKey = 'selected_theme';

  AppThemeType _currentTheme = AppThemeType.light;

  /// Gets the current theme type
  AppThemeType get currentTheme => _currentTheme;

  /// Gets the current theme data
  ThemeData get themeData {
    switch (_currentTheme) {
      case AppThemeType.light:
        return _lightTheme;
      case AppThemeType.dark:
        return _darkTheme;
      case AppThemeType.highContrast:
        return _highContrastTheme;
    }
  }

  /// Light theme configuration
  ThemeData get _lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF4CAF50),
        brightness: Brightness.light,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFF4CAF50),
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF4CAF50),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      cardTheme: const CardThemeData(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
      ),
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: Color(0xFF2E7D32),
        ),
        headlineMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: Color(0xFF2E7D32),
        ),
        titleLarge: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: Color(0xFF2E7D32),
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          color: Color(0xFF424242),
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          color: Color(0xFF424242),
        ),
      ),
    );
  }

  /// Dark theme configuration
  ThemeData get _darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF4CAF50),
        brightness: Brightness.dark,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFF1B5E20),
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF4CAF50),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      cardTheme: const CardThemeData(
        elevation: 4,
        color: Color(0xFF2E2E2E),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
      ),
      scaffoldBackgroundColor: const Color(0xFF121212),
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: Color(0xFFA5D6A7),
        ),
        headlineMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: Color(0xFFA5D6A7),
        ),
        titleLarge: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: Color(0xFFA5D6A7),
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          color: Color(0xFFE0E0E0),
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          color: Color(0xFFE0E0E0),
        ),
      ),
    );
  }

  /// High contrast theme configuration for accessibility
  ThemeData get _highContrastTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: const ColorScheme.light(
        primary: Colors.black,
        secondary: Colors.black,
        surface: Colors.white,
        background: Colors.white,
        error: Colors.red,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: Colors.black,
        onBackground: Colors.black,
        onError: Colors.white,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.black,
          foregroundColor: Colors.white,
          side: const BorderSide(color: Colors.black, width: 2),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.black,
          side: const BorderSide(color: Colors.black, width: 2),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      cardTheme: const CardThemeData(
        elevation: 4,
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8)),
          side: BorderSide(color: Colors.black, width: 2),
        ),
      ),
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          fontSize: 36,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
        headlineMedium: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
        titleLarge: TextStyle(
          fontSize: 26,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
        bodyLarge: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w500,
          color: Colors.black,
        ),
        bodyMedium: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Colors.black,
        ),
      ),
    );
  }

  /// Initializes the theme service
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeIndex = prefs.getInt(_themeKey) ?? 0;
      
      if (themeIndex >= 0 && themeIndex < AppThemeType.values.length) {
        _currentTheme = AppThemeType.values[themeIndex];
      }
    } catch (e) {
      _currentTheme = AppThemeType.light;
    }
  }

  /// Changes the current theme
  Future<void> setTheme(AppThemeType theme) async {
    if (_currentTheme == theme) return;

    _currentTheme = theme;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeKey, theme.index);
    } catch (e) {
      // Handle error silently
    }
  }

  /// Gets theme-appropriate colors for specific UI elements
  Color getStoryCardBorderColor(String difficulty) {
    switch (_currentTheme) {
      case AppThemeType.light:
        return _getLightDifficultyColor(difficulty);
      case AppThemeType.dark:
        return _getDarkDifficultyColor(difficulty);
      case AppThemeType.highContrast:
        return Colors.black;
    }
  }

  Color _getLightDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return const Color(0xFF4CAF50); // Green
      case 'medium':
        return const Color(0xFFFF9800); // Orange
      case 'hard':
        return const Color(0xFFF44336); // Red
      default:
        return const Color(0xFF2196F3); // Blue
    }
  }

  Color _getDarkDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return const Color(0xFF66BB6A); // Light Green
      case 'medium':
        return const Color(0xFFFFB74D); // Light Orange
      case 'hard':
        return const Color(0xFFEF5350); // Light Red
      default:
        return const Color(0xFF42A5F5); // Light Blue
    }
  }

  /// Gets background gradient colors for screens
  List<Color> getBackgroundGradient(String screenType) {
    switch (_currentTheme) {
      case AppThemeType.light:
        return _getLightGradient(screenType);
      case AppThemeType.dark:
        return _getDarkGradient(screenType);
      case AppThemeType.highContrast:
        return [Colors.white, Colors.white];
    }
  }

  List<Color> _getLightGradient(String screenType) {
    switch (screenType) {
      case 'splash':
        return [const Color(0xFF42A5F5), const Color(0xFFFFCA28)];
      case 'homepage':
        return [const Color(0xFFFF9800), const Color(0xFFFFCA28)];
      case 'library':
        return [const Color(0xFF4CAF50), const Color(0xFF8BC34A)];
      default:
        return [const Color(0xFF42A5F5), const Color(0xFFFFCA28)];
    }
  }

  List<Color> _getDarkGradient(String screenType) {
    switch (screenType) {
      case 'splash':
        return [const Color(0xFF1565C0), const Color(0xFF424242)];
      case 'homepage':
        return [const Color(0xFF424242), const Color(0xFF616161)];
      case 'library':
        return [const Color(0xFF2E7D32), const Color(0xFF388E3C)];
      default:
        return [const Color(0xFF1565C0), const Color(0xFF424242)];
    }
  }

  /// Checks if current theme is high contrast for accessibility
  bool get isHighContrast => _currentTheme == AppThemeType.highContrast;

  /// Gets accessible text color for the current theme
  Color get accessibleTextColor {
    switch (_currentTheme) {
      case AppThemeType.light:
        return const Color(0xFF2E7D32);
      case AppThemeType.dark:
        return const Color(0xFFA5D6A7);
      case AppThemeType.highContrast:
        return Colors.black;
    }
  }
}
