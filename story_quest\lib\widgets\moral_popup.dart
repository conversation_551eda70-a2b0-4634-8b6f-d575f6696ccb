/// Moral moment popup widget for post-choice reflections
///
/// Displays moral lessons and reflections after important story choices,
/// helping children understand the consequences and values of their decisions.
library;

import 'package:flutter/material.dart';
import '../models/badge_model.dart';
import '../utils/accessibility_helper.dart';

/// Moral popup widget for reflection moments
class MoralPopup extends StatefulWidget {
  /// The moral lesson or reflection text
  final String moralText;
  
  /// Optional badge that was earned from this choice
  final BadgeModel? earnedBadge;
  
  /// The choice that led to this moral moment
  final String choiceText;
  
  /// Callback when continue is pressed
  final VoidCallback? onContinue;
  
  /// Whether the popup can be dismissed by tapping outside
  final bool barrierDismissible;

  const MoralPopup({
    super.key,
    required this.moralText,
    this.earnedBadge,
    required this.choiceText,
    this.onContinue,
    this.barrierDismissible = false,
  });

  @override
  State<MoralPopup> createState() => _MoralPopupState();
}

class _MoralPopupState extends State<MoralPopup>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _badgeController;
  late AnimationController _sparkleController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _badgeAnimation;
  late Animation<double> _sparkleAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize slide animation
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // Initialize badge animation
    _badgeController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _badgeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _badgeController,
      curve: Curves.elasticOut,
    ));

    // Initialize sparkle animation
    _sparkleController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _sparkleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _sparkleController,
      curve: Curves.easeInOut,
    ));

    // Start animations
    _slideController.forward();
    
    if (widget.earnedBadge != null) {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _badgeController.forward();
          _sparkleController.repeat(reverse: true);
        }
      });
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _badgeController.dispose();
    _sparkleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => widget.barrierDismissible,
      child: Material(
        color: Colors.black.withValues(alpha: 0.8),
        child: Center(
          child: SlideTransition(
            position: _slideAnimation,
            child: _buildPopupContent(),
          ),
        ),
      ),
    );
  }

  /// Builds the main popup content
  Widget _buildPopupContent() {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    
    return Container(
      margin: EdgeInsets.all(isTablet ? 40 : 20),
      constraints: BoxConstraints(
        maxWidth: isTablet ? 500 : 350,
        maxHeight: screenSize.height * 0.8,
      ),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFFFF3E0), // Light orange
            Color(0xFFE8F5E8), // Light green
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50),
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          _buildContent(),
          if (widget.earnedBadge != null) _buildBadgeSection(),
          _buildContinueButton(),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  /// Builds the popup header
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Moral moment icon with sparkles
          Stack(
            alignment: Alignment.center,
            children: [
              // Sparkle effects
              if (widget.earnedBadge != null)
                AnimatedBuilder(
                  animation: _sparkleAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _sparkleAnimation.value * 0.7,
                      child: Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFFFFCA28).withValues(alpha: 0.6),
                              blurRadius: 20,
                              spreadRadius: 5,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              
              // Main icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.lightbulb,
                  color: Colors.white,
                  size: 40,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Title
          AccessibilityHelper.createAccessibleText(
            'Moral Moment',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2E7D32),
            ),
            semanticLabel: 'Moral moment reflection',
          ),
        ],
      ),
    );
  }

  /// Builds the main content section
  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        children: [
          // Choice reflection
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AccessibilityHelper.createAccessibleText(
                  'You chose to:',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF757575),
                  ),
                ),
                const SizedBox(height: 8),
                AccessibilityHelper.createAccessibleText(
                  '"${widget.choiceText}"',
                  style: const TextStyle(
                    fontSize: 16,
                    fontStyle: FontStyle.italic,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Moral lesson
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: AccessibilityHelper.createAccessibleText(
              widget.moralText,
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF2E7D32),
                height: 1.4,
              ),
              semanticLabel: 'Moral lesson: ${widget.moralText}',
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the badge section if a badge was earned
  Widget _buildBadgeSection() {
    return AnimatedBuilder(
      animation: _badgeAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _badgeAnimation.value,
          child: Opacity(
            opacity: _badgeAnimation.value,
            child: Container(
              margin: const EdgeInsets.all(24),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(widget.earnedBadge!.rarityColor).withValues(alpha: 0.1),
                    Color(widget.earnedBadge!.rarityColor).withValues(alpha: 0.2),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Color(widget.earnedBadge!.rarityColor),
                  width: 2,
                ),
              ),
              child: Column(
                children: [
                  // Badge earned text
                  AccessibilityHelper.createAccessibleText(
                    '🎉 Badge Earned! 🎉',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2E7D32),
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Badge display
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        widget.earnedBadge!.displayEmoji,
                        style: const TextStyle(fontSize: 32),
                      ),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.earnedBadge!.name,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Color(widget.earnedBadge!.rarityColor),
                            ),
                          ),
                          Text(
                            widget.earnedBadge!.rarity.displayName,
                            style: const TextStyle(
                              fontSize: 12,
                              color: Color(0xFF757575),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Builds the continue button
  Widget _buildContinueButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: SizedBox(
        width: double.infinity,
        height: 48,
        child: ElevatedButton(
          onPressed: _handleContinue,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF4CAF50),
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24),
            ),
            elevation: 4,
          ),
          child: const Text(
            'Continue Story',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  /// Handles continue button press
  void _handleContinue() {
    AccessibilityHelper.provideHapticFeedback();
    Navigator.of(context).pop();
    widget.onContinue?.call();
  }
}

/// Shows a moral popup dialog
Future<void> showMoralPopup({
  required BuildContext context,
  required String moralText,
  required String choiceText,
  BadgeModel? earnedBadge,
  VoidCallback? onContinue,
  bool barrierDismissible = false,
}) {
  return showDialog<void>(
    context: context,
    barrierDismissible: barrierDismissible,
    builder: (context) => MoralPopup(
      moralText: moralText,
      earnedBadge: earnedBadge,
      choiceText: choiceText,
      barrierDismissible: barrierDismissible,
      onContinue: onContinue,
    ),
  );
}
