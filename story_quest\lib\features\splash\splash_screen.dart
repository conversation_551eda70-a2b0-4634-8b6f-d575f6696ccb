/// Splash screen for Story Quest app
/// 
/// Full-screen welcome screen with gradient background, animated mascot,
/// and continue button. Responsive design for all screen sizes.
library;

import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../../utils/device_utils.dart';

/// Splash screen widget with animated elements
class SplashScreen extends StatefulWidget {
  /// Callback function when continue button is pressed
  final VoidCallback? onContinue;

  const SplashScreen({
    super.key,
    this.onContinue,
  });

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _sparkleController;
  late AnimationController _scaleController;
  late Animation<double> _sparkleAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize sparkle animation
    _sparkleController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _sparkleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _sparkleController,
      curve: Curves.easeInOut,
    ));

    // Initialize scale animation for mascot
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    // Start animations
    _sparkleController.repeat(reverse: true);
    _scaleController.forward();
  }

  @override
  void dispose() {
    _sparkleController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveLayoutBuilder(
      builder: (context, deviceType, orientation) {
        return Scaffold(
          body: Container(
            width: double.infinity,
            height: double.infinity,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF42A5F5), // Bright blue
                  Color(0xFFFFCA28), // Yellow
                ],
              ),
            ),
            child: SafeArea(
              child: _buildAdaptiveLayout(context, deviceType, orientation),
            ),
          ),
        );
      },
    );
  }

  /// Builds adaptive layout based on device type and orientation
  Widget _buildAdaptiveLayout(BuildContext context, DeviceType deviceType, ScreenOrientation orientation) {
    switch (deviceType) {
      case DeviceType.mobile:
        return orientation == ScreenOrientation.portrait
            ? _buildMobilePortraitLayout(context)
            : _buildMobileLandscapeLayout(context);
      case DeviceType.tablet:
        return orientation == ScreenOrientation.portrait
            ? _buildTabletPortraitLayout(context)
            : _buildTabletLandscapeLayout(context);
      case DeviceType.desktop:
        return _buildDesktopLayout(context);
      case DeviceType.tv:
        return _buildTVLayout(context);
    }
  }

  /// Builds the mobile portrait layout
  Widget _buildMobilePortraitLayout(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Spacer(flex: 2),
        _buildAnimatedMascot(),
        SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 40)),
        _buildWelcomeText(),
        const Spacer(flex: 2),
        _buildContinueButton(),
        SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 40)),
      ],
    );
  }

  /// Builds the mobile landscape layout
  Widget _buildMobileLandscapeLayout(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildAnimatedMascot(),
              const SizedBox(height: 20),
              _buildWelcomeText(),
            ],
          ),
        ),
        Expanded(
          flex: 2,
          child: Center(
            child: _buildContinueButton(),
          ),
        ),
      ],
    );
  }

  /// Builds the tablet portrait layout
  Widget _buildTabletPortraitLayout(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Spacer(flex: 2),
        _buildAnimatedMascot(),
        SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 50)),
        _buildWelcomeText(),
        const Spacer(flex: 2),
        _buildContinueButton(),
        SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 50)),
      ],
    );
  }

  /// Builds the tablet landscape layout
  Widget _buildTabletLandscapeLayout(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildAnimatedMascot(),
              SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 30)),
              _buildWelcomeText(),
            ],
          ),
        ),
        Expanded(
          flex: 2,
          child: Center(
            child: _buildContinueButton(),
          ),
        ),
      ],
    );
  }

  /// Builds the desktop layout
  Widget _buildDesktopLayout(BuildContext context) {
    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: DeviceUtils.getMaxContentWidth(context),
        ),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildAnimatedMascot(),
                  SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 40)),
                  _buildWelcomeText(),
                ],
              ),
            ),
            Expanded(
              child: Center(
                child: _buildContinueButton(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the TV layout
  Widget _buildTVLayout(BuildContext context) {
    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: DeviceUtils.getMaxContentWidth(context),
        ),
        child: Row(
          children: [
            Expanded(
              flex: 3,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildAnimatedMascot(),
                  SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 60)),
                  _buildWelcomeText(),
                ],
              ),
            ),
            Expanded(
              flex: 2,
              child: Center(
                child: _buildContinueButton(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the animated mascot (owl) with sparkle effects
  Widget _buildAnimatedMascot() {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Sparkle effects
              AnimatedBuilder(
                animation: _sparkleAnimation,
                builder: (context, child) {
                  return Opacity(
                    opacity: _sparkleAnimation.value,
                    child: Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.white.withValues(alpha: 0.6),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
              
              // Mascot (owl icon as placeholder)
              Container(
                width: 150,
                height: 150,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 15,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.auto_stories, // Book icon as mascot placeholder
                  size: 80,
                  color: Color(0xFF42A5F5),
                ),
              ),
              
              // Sparkle particles
              ..._buildSparkleParticles(),
            ],
          ),
        );
      },
    );
  }

  /// Builds sparkle particle effects around the mascot
  List<Widget> _buildSparkleParticles() {
    return List.generate(6, (index) {
      final angle = (index * 60.0) * (3.14159 / 180); // Convert to radians
      final radius = 100.0;
      
      return AnimatedBuilder(
        animation: _sparkleAnimation,
        builder: (context, child) {
          return Positioned(
            left: 100 + radius * math.cos(angle) * _sparkleAnimation.value,
            top: 100 + radius * math.sin(angle) * _sparkleAnimation.value,
            child: Opacity(
              opacity: _sparkleAnimation.value,
              child: const Icon(
                Icons.star,
                color: Colors.white,
                size: 16,
              ),
            ),
          );
        },
      );
    });
  }

  /// Builds the welcome text with shadow effect
  Widget _buildWelcomeText() {
    final fontSize = DeviceUtils.getResponsiveFontSize(context, 28.0);
    
    return Semantics(
      label: 'Welcome to Story Quest! Interactive storytelling app for kids',
      child: Text(
        'Welcome to Story Quest!',
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          shadows: const [
            Shadow(
              color: Colors.black,
              offset: Offset(2, 2),
              blurRadius: 4,
            ),
          ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// Builds the continue button with glow effect
  Widget _buildContinueButton() {
    final buttonHeight = DeviceUtils.getResponsiveButtonHeight(context);
    final buttonSize = buttonHeight * 1.5; // Make it circular but larger
    
    return Semantics(
      button: true,
      label: 'Continue to start your story adventure',
      child: GestureDetector(
        onTap: widget.onContinue,
        child: Container(
          width: buttonSize,
          height: buttonSize,
          decoration: BoxDecoration(
            color: const Color(0xFF4CAF50), // Green
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF4CAF50).withValues(alpha: 0.6),
                blurRadius: 20,
                spreadRadius: 5,
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Center(
            child: Text(
              'Continue',
              style: TextStyle(
                color: Colors.white,
                fontSize: DeviceUtils.getResponsiveFontSize(context, 18),
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }
}
