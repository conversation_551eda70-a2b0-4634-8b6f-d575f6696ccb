/// Choice popup widget for story decision points
///
/// Displays interactive choices during story playback with
/// child-friendly design and accessibility features.
library;

import 'package:flutter/material.dart';
import '../models/choice_model.dart';
import '../utils/device_utils.dart';

/// Popup widget for displaying story choices
class ChoicePopupWidget extends StatefulWidget {
  /// List of available choices
  final List<Choice> choices;
  
  /// Callback when a choice is selected
  final Function(Choice) onChoiceSelected;
  
  /// Optional title for the choice prompt
  final String? title;
  
  /// Whether to show choice consequences
  final bool showConsequences;

  const ChoicePopupWidget({
    super.key,
    required this.choices,
    required this.onChoiceSelected,
    this.title,
    this.showConsequences = false,
  });

  @override
  State<ChoicePopupWidget> createState() => _ChoicePopupWidgetState();
}

class _ChoicePopupWidgetState extends State<ChoicePopupWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _animationController.forward();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Dialog(
              backgroundColor: Colors.transparent,
              child: _buildChoiceDialog(context),
            ),
          ),
        );
      },
    );
  }

  Widget _buildChoiceDialog(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: DeviceUtils.getResponsiveWidth(context, 0.9),
        maxHeight: DeviceUtils.getResponsiveHeight(context, 0.8),
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(context),
          Flexible(
            child: _buildChoicesList(context),
          ),
          _buildFooter(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(DeviceUtils.getResponsiveSpacing(context, 20)),
      decoration: const BoxDecoration(
        color: Color(0xFF4CAF50),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.psychology,
            color: Colors.white,
            size: DeviceUtils.getResponsiveIconSize(context, 32),
          ),
          SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 8)),
          Text(
            widget.title ?? 'What would you like to do?',
            style: TextStyle(
              fontSize: DeviceUtils.getResponsiveFontSize(context, 20),
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 4)),
          Text(
            'Choose wisely!',
            style: TextStyle(
              fontSize: DeviceUtils.getResponsiveFontSize(context, 14),
              color: Colors.white.withValues(alpha: 0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildChoicesList(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(DeviceUtils.getResponsiveSpacing(context, 16)),
      child: ListView.separated(
        shrinkWrap: true,
        itemCount: widget.choices.length,
        separatorBuilder: (context, index) => SizedBox(
          height: DeviceUtils.getResponsiveSpacing(context, 12),
        ),
        itemBuilder: (context, index) {
          final choice = widget.choices[index];
          return _buildChoiceButton(context, choice, index);
        },
      ),
    );
  }

  Widget _buildChoiceButton(BuildContext context, Choice choice, int index) {
    final colors = [
      const Color(0xFF2196F3), // Blue
      const Color(0xFFFF9800), // Orange
      const Color(0xFF9C27B0), // Purple
      const Color(0xFF4CAF50), // Green
    ];
    
    final color = colors[index % colors.length];

    return Semantics(
      button: true,
      label: 'Choice ${index + 1}: ${choice.text}',
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _selectChoice(choice),
          borderRadius: BorderRadius.circular(15),
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.all(DeviceUtils.getResponsiveSpacing(context, 16)),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              border: Border.all(color: color, width: 2),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: DeviceUtils.getResponsiveIconSize(context, 24),
                      height: DeviceUtils.getResponsiveIconSize(context, 24),
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '${index + 1}',
                          style: TextStyle(
                            fontSize: DeviceUtils.getResponsiveFontSize(context, 12),
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: DeviceUtils.getResponsiveSpacing(context, 12)),
                    Expanded(
                      child: Text(
                        choice.text,
                        style: TextStyle(
                          fontSize: DeviceUtils.getResponsiveFontSize(context, 16),
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF2E7D32),
                        ),
                      ),
                    ),
                  ],
                ),
                if (widget.showConsequences && choice.consequence != null) ...[
                  SizedBox(height: DeviceUtils.getResponsiveSpacing(context, 8)),
                  Text(
                    choice.consequence!,
                    style: TextStyle(
                      fontSize: DeviceUtils.getResponsiveFontSize(context, 12),
                      color: const Color(0xFF757575),
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(DeviceUtils.getResponsiveSpacing(context, 16)),
      child: Row(
        children: [
          Icon(
            Icons.lightbulb_outline,
            color: const Color(0xFFFF9800),
            size: DeviceUtils.getResponsiveIconSize(context, 16),
          ),
          SizedBox(width: DeviceUtils.getResponsiveSpacing(context, 8)),
          Expanded(
            child: Text(
              'Think about what the character would do!',
              style: TextStyle(
                fontSize: DeviceUtils.getResponsiveFontSize(context, 12),
                color: const Color(0xFF757575),
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _selectChoice(Choice choice) {
    // Add haptic feedback for better UX
    // HapticFeedback.lightImpact();
    
    // Animate out before calling callback
    _animationController.reverse().then((_) {
      widget.onChoiceSelected(choice);
    });
  }
}

/// Shows a choice popup dialog
Future<void> showChoicePopup({
  required BuildContext context,
  required List<Choice> choices,
  required Function(Choice) onChoiceSelected,
  String? title,
  bool showConsequences = false,
}) {
  return showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) => ChoicePopupWidget(
      choices: choices,
      onChoiceSelected: onChoiceSelected,
      title: title,
      showConsequences: showConsequences,
    ),
  );
}
